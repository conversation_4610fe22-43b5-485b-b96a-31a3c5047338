---
type: "always_apply"
---

Before working on any task, **use context7 MCP**.

Think carefully and only action the specific task I have given you with the most concise and elegant solution that changes as little code as possible. Always focus on the task at hand and do not touch unrelated code.

## Fundamental Principles

* Write clean, simple, readable code.
* You are an expert in **React, Vite, Node, and Express**.
* Implement features in the simplest possible way.
* Keep files small and focused (<200 lines).
* Test after every meaningful change.
* Focus on core functionality before optimization.
* Use clear, consistent naming.
* Think thoroughly before coding. Write **2–3 reasoning paragraphs** before giving the code.
* ALWAYS write simple, clean, and modular code.
* Use clear and easy-to-understand language. Write in short sentences.
* Focus only on the areas of code relevant to the task.
* Write thorough tests for all major functionality.
* Avoid making major changes to established patterns or architecture unless explicitly instructed.
* Always consider how other methods or areas of code might be affected by changes.
* When adding new features or UI/UX design, use the existing style and design consistently.
* Always implement **high-quality, production-ready code** using the requested tools and libraries. Do NOT use mocks, simulations, or shortcuts.
* Follow TDD: **write tests first, then the code, run tests, and update until they pass**.
* **Before creating any new function, library, or module, always check if it already exists in the project. Reuse existing solutions whenever possible.**

## Error Fixing

* **DO NOT jump to conclusions.** Consider multiple possible causes first.
* Explain the problem in plain English.
* Make minimal necessary changes — change as few lines of code as possible.
* For strange or unclear errors, suggest the user perform a **Perplexity web search** for up-to-date information.

## Coding & Structuring Rules

* For complex code changes, start with a **plan of action** and ask for approval before coding.
* For simple changes, make the code change directly but always think step by step.
* When a file becomes too long, split it into smaller files.
* **React components** must not exceed 300 lines. Break them into reusable pieces.
* When a function becomes too long, split it into smaller functions.
* When debugging, ensure you have enough information to deeply understand the problem.
* Add more logging and tracing when needed to understand issues. If logs make the cause obvious, implement a fix. If not, consider **4–6 possible causes**, narrow to 1–2 most likely, then proceed (add logging or fix if very confident).
* If markdown files are provided, read them as reference for structure, but **do not edit them**.
* When creating or updating files, always include a **comment at the top** describing the changes you made.