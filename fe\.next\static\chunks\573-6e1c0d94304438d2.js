"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[573],{285:(e,t,a)=>{a.d(t,{$:()=>c});var s=a(5155);a(2115);var r=a(9708),o=a(2085),n=a(9434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:a,size:o,asChild:c=!1,...d}=e,l=c?r.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(i({variant:a,size:o,className:t})),...d})}},5731:(e,t,a)=>{a.d(t,{FH:()=>d,H2:()=>c,R2:()=>l,SP:()=>n});var s=a(3464),r=a(9509);let o=r.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",n=s.A.create({baseURL:o,headers:{"Content-Type":"application/json"},timeout:3e4});function i(){return localStorage.getItem("token")}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:a="GET",body:s,headers:r={},skipAuth:o=!1}=t,i={method:a.toLowerCase(),url:e,headers:{...r},metadata:{skipAuth:o}};s&&"GET"!==a&&(i.data=s);try{return(await n(i)).data}catch(e){throw e}}n.interceptors.request.use(e=>{var t,a;let s=i();return console.log("\uD83D\uDD0D API Call Debug:",{endpoint:e.url,fullUrl:"".concat(o).concat(e.url),API_URL:o,hasToken:!!s,tokenPreview:s?s.substring(0,3)+"***":"none",method:null==(t=e.method)?void 0:t.toUpperCase(),data:e.data,"process.env.NEXT_PUBLIC_API_URL":r.env.NEXT_PUBLIC_API_URL}),!s||(null==(a=e.metadata)?void 0:a.skipAuth)||(e.headers.Authorization="Bearer ".concat(s)),e},e=>(console.error("\uD83D\uDCA5 Request interceptor error:",e),Promise.reject(e))),n.interceptors.response.use(e=>(console.log("\uD83D\uDCE1 Response status:",e.status,e.statusText),console.log("✅ API Success:",e.data),e),e=>{var t,a,s,r;throw console.error("❌ API Error:",(null==(t=e.response)?void 0:t.data)||e.message),console.error("\uD83D\uDCA5 API call failed:",e),Error((null==(s=e.response)||null==(a=s.data)?void 0:a.message)||e.message||"HTTP error! status: ".concat(null==(r=e.response)?void 0:r.status))});let d={get:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"GET"})},post:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"POST"})},put:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"PUT"})},delete:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"DELETE"})},health:()=>c("/health"),chat:(e,t,a,s)=>c("/api/chat",{method:"POST",body:{message:e,threadId:t,expertId:a,expertContext:s}}),getThreadMessages:e=>c("/api/thread/".concat(e,"/messages")),getSessionMessages:(e,t)=>c("/api/chat/sessions/".concat(e,"/messages").concat(t?"?limit=".concat(t):"")),getUserChatSessions:e=>c("/api/chat/sessions".concat(e?"?limit=".concat(e):"")),getUserStats:()=>c("/api/chat/stats"),getActiveSessionForExpert:e=>c("/api/chat/sessions/expert/".concat(e)),updateSessionTitle:(e,t)=>c("/api/chat/sessions/".concat(e,"/title"),{method:"PUT",body:{title:t}}),deleteSession:e=>c("/api/chat/sessions/".concat(e),{method:"DELETE"}),createThread:()=>c("/assistant/thread",{method:"POST"}),sendMessage:(e,t)=>c("/assistant/message",{method:"POST",body:{threadId:e,message:t}}),runAssistant:e=>c("/assistant/run",{method:"POST",body:{threadId:e}}),getMessages:e=>c("/assistant/messages/".concat(e)),createExpert:async e=>{let t=i();try{return(await s.A.post("".concat(o,"/api/experts"),e,{headers:{"Content-Type":"multipart/form-data",...t?{Authorization:"Bearer ".concat(t)}:{}}})).data}catch(e){var a,r,n;throw Error((null==(r=e.response)||null==(a=r.data)?void 0:a.message)||e.message||"HTTP error! status: ".concat(null==(n=e.response)?void 0:n.status))}},listExperts:()=>c("/api/experts"),getPublicExperts:()=>c("/api/experts/public",{skipAuth:!0}),getExpert:e=>c("/api/experts/".concat(e)),updateExpert:async(e,t,a,r)=>{let n=i(),c=new FormData;Object.keys(t).forEach(e=>{void 0!==t[e]&&null!==t[e]&&("labels"===e&&Array.isArray(t[e])?c.append(e,JSON.stringify(t[e])):c.append(e,t[e].toString()))}),a&&c.append("file",a),r&&c.append("image",r);try{return(await s.A.put("".concat(o,"/api/experts/").concat(e),c,{headers:{"Content-Type":"multipart/form-data",...n?{Authorization:"Bearer ".concat(n)}:{}}})).data}catch(e){var d,l,p;throw Error((null==(l=e.response)||null==(d=l.data)?void 0:d.message)||e.message||"HTTP error! status: ".concat(null==(p=e.response)?void 0:p.status))}},getAvailableModels:()=>c("/api/models"),getModelPricing:e=>c("/api/models/".concat(e,"/pricing")),calculateCost:(e,t,a,s)=>c("/api/calculate-cost",{method:"POST",body:{model:e,inputTokens:t,outputTokens:a,pricingPercentage:s}}),getExpertStats:e=>c("/api/experts/".concat(e,"/stats")),createReview:e=>c("/api/reviews",{method:"POST",body:e}),updateReview:(e,t)=>c("/api/reviews/".concat(e),{method:"PUT",body:t}),getExpertReviews:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return c("/api/reviews/expert/".concat(e,"?page=").concat(t,"&limit=").concat(a),{skipAuth:!0})},getUserReviews:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return c("/api/reviews/my?page=".concat(e,"&limit=").concat(t))},getReview:e=>c("/api/reviews/".concat(e),{skipAuth:!0}),canUserReview:e=>c("/api/reviews/expert/".concat(e,"/can-review")),getExpertRatingStats:e=>c("/api/reviews/expert/".concat(e,"/stats"),{skipAuth:!0}),getFilteredExperts:e=>{let t=new URLSearchParams;return e.filter&&t.append("filter",e.filter),e.timeline&&t.append("timeline",e.timeline),e.search&&t.append("search",e.search),e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),c("/api/experts/filtered?".concat(t.toString()),{skipAuth:!0})}},l={register:e=>c("/api/users/register",{method:"POST",body:e,skipAuth:!0}),verifyOTP:e=>c("/api/users/verify-otp",{method:"POST",body:e,skipAuth:!0}),login:e=>c("/api/users/login",{method:"POST",body:e,skipAuth:!0}),getProfile:()=>c("/api/users/profile"),updateProfile:e=>c("/api/users/profile",{method:"PUT",body:e}),changePassword:e=>c("/api/users/change-password",{method:"POST",body:e}),resendOTP:e=>c("/api/users/resend-otp",{method:"POST",body:{phone:e},skipAuth:!0}),forgotPassword:e=>c("/api/users/forgot-password",{method:"POST",body:{phone:e},skipAuth:!0}),resetPassword:(e,t,a)=>c("/api/users/reset-password",{method:"POST",body:{phone:e,code:t,newPassword:a},skipAuth:!0}),logout:()=>c("/api/users/logout",{method:"POST"}),getBalanceSummary:()=>c("/api/balance/summary"),getPointTransactions:e=>c("/api/balance/transactions/points".concat(e?"?limit=".concat(e):"")),getCreditTransactions:e=>c("/api/balance/transactions/credits".concat(e?"?limit=".concat(e):"")),checkAffordability:e=>c("/api/balance/can-afford",{method:"POST",body:{amount:e}}),addPoints:(e,t)=>c("/api/balance/points/add",{method:"POST",body:{amount:e,description:t}}),addCredits:(e,t)=>c("/api/balance/credits/add",{method:"POST",body:{amount:e,description:t}})}},6695:(e,t,a)=>{a.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>o,aR:()=>n});var s=a(5155);a(2115);var r=a(9434);function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},9434:(e,t,a)=>{a.d(t,{L:()=>i,cn:()=>n});var s=a(2596),r=a(9688);let o=a(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:3001";function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){return"".concat(o,"/").concat(e.startsWith("/")?e.replace("/",""):e)}}}]);