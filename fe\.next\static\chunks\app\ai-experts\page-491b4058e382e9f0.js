(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{103:(e,s,r)=>{Promise.resolve().then(r.bind(r,5175))},1394:(e,s,r)=>{"use strict";r.d(s,{BK:()=>n,eu:()=>i,q5:()=>c});var t=r(5155);r(2115);var a=r(4011),l=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)(a.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)(a._V,{"data-slot":"avatar-image",className:(0,l.cn)("aspect-square size-full",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",s),...r})}},2523:(e,s,r)=>{"use strict";r.d(s,{p:()=>l});var t=r(5155);r(2115);var a=r(9434);function l(e){let{className:s,type:r,...l}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},5175:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>W});var t=r(5155),a=r(5695),l=r(2115),i=r(9409),n=r(9434);let c=e=>{let{options:s,selectedValue:r,onSelect:a,placeholder:l="Select filter",className:c="",disabled:o=!1}=e;return(0,t.jsxs)(i.l6,{value:r,onValueChange:e=>{a(e)},disabled:o,children:[(0,t.jsx)(i.bq,{className:(0,n.cn)("w-full sm:w-[200px] h-10 bg-white border border-gray-200 hover:border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200",o&&"opacity-50 cursor-not-allowed",c),"aria-label":"Filter experts",children:(0,t.jsx)(i.yv,{placeholder:l})}),(0,t.jsx)(i.gC,{className:"bg-white border border-gray-200 shadow-lg rounded-lg p-1 min-w-[200px]",children:s.map(e=>(0,t.jsxs)(i.eb,{value:e.id,className:(0,n.cn)("flex items-center gap-3 px-3 py-2.5 rounded-md cursor-pointer transition-colors duration-150","hover:bg-gray-50 focus:bg-gray-50 focus:outline-none","data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary",r===e.id&&"bg-primary/5"),children:[(0,t.jsx)("span",{className:"text-base flex-shrink-0",role:"img","aria-hidden":"true",children:e.icon}),(0,t.jsxs)("div",{className:"flex flex-col items-start",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 text-sm",children:e.label}),e.description&&(0,t.jsx)("span",{className:"text-xs text-gray-500 mt-0.5",children:e.description})]})]},e.id))})]})},o=e=>{let{options:s,selectedValue:r,onSelect:a,className:l="",disabled:c=!1}=e;return(0,t.jsxs)(i.l6,{value:r,onValueChange:e=>{a(e)},disabled:c,children:[(0,t.jsx)(i.bq,{className:(0,n.cn)("w-full sm:w-[150px] h-10 bg-white border border-gray-200 hover:border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200",c&&"opacity-50 cursor-not-allowed",l),"aria-label":"Select timeline",children:(0,t.jsx)(i.yv,{placeholder:"Last 30 Days"})}),(0,t.jsx)(i.gC,{className:"bg-white border border-gray-200 shadow-lg rounded-lg p-1 min-w-[150px]",children:s.map(e=>(0,t.jsx)(i.eb,{value:e.id,className:(0,n.cn)("px-3 py-2 rounded-md cursor-pointer transition-colors duration-150","hover:bg-gray-50 focus:bg-gray-50 focus:outline-none","data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary",r===e.id&&"bg-primary/5"),children:(0,t.jsx)("span",{className:"font-medium text-gray-900 text-sm",children:e.label})},e.id))})]})};var d=r(6695),m=r(1394),x=r(6126),u=r(285);let h=function(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"bg-yellow-200 text-yellow-900 px-1 rounded";if(!s||!e)return e;let a=s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),l=RegExp("(".concat(a,")"),"gi"),i=e.split(l);return(0,t.jsx)(t.Fragment,{children:i.map((e,a)=>e.toLowerCase()===s.toLowerCase()?(0,t.jsx)("mark",{className:r,children:e},a):e)})},g=function(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:150,t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"bg-yellow-200 text-yellow-900 px-1 rounded";if(!e)return e;let a=e;if(e.length>r&&s){let t=e.toLowerCase().indexOf(s.toLowerCase());if(-1!==t){let l=Math.max(0,t-Math.floor((r-s.length)/2)),i=Math.min(e.length,l+r);a=e.substring(l,i),l>0&&(a="..."+a),i<e.length&&(a+="...")}else a=e.substring(0,r)+(e.length>r?"...":"")}else e.length>r&&(a=e.substring(0,r)+"...");return h(a,s,t)};var p=r(8564),f=r(1366),y=r(5868);let b=e=>{let s,{expert:r,onChatClick:a,className:l="",showStats:i=!0,searchTerm:c=""}=e,o=()=>{a&&a(r.id)};return(0,t.jsxs)(d.Zp,{className:(0,n.cn)("group hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-gray-300 bg-white","hover:scale-[1.02] cursor-pointer",l),onClick:o,children:[(0,t.jsx)(d.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsxs)(m.eu,{className:"h-12 w-12 border-2 border-gray-100",children:[(0,t.jsx)(m.BK,{src:r.imageUrl,alt:r.name,className:"object-cover"}),(0,t.jsx)(m.q5,{className:"bg-primary/10 text-primary font-semibold",children:r.name.split(" ").map(e=>e[0]).join("").toUpperCase()})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900 text-lg leading-tight truncate",children:c?h(r.name,c,"bg-yellow-200 text-yellow-900 px-0.5 rounded"):r.name}),r.averageRating>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 mt-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:r.averageRating.toFixed(1)}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["(",r.totalReviews," reviews)"]})]})]})]})}),(0,t.jsxs)(d.Wu,{className:"pt-0",children:[(0,t.jsx)("div",{className:"text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3",children:c?g(r.description,c,120,"bg-yellow-200 text-yellow-900 px-0.5 rounded"):r.description}),r.labels&&r.labels.length>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-1.5 mb-4",children:[r.labels.slice(0,3).map((e,s)=>(0,t.jsx)(x.E,{variant:"secondary",className:"text-xs px-2 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200",children:e},s)),r.labels.length>3&&(0,t.jsxs)(x.E,{variant:"outline",className:"text-xs px-2 py-1 text-gray-500",children:["+",r.labels.length-3," more"]})]}),i&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:[(e=>e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString())(r.totalChats)," chats"]})]}),r.totalRevenue>0&&(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:(s=r.totalRevenue,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}).format(s))})]})]}),(0,t.jsx)(u.$,{className:"w-full group-hover:bg-primary group-hover:text-white transition-colors duration-200",variant:"outline",onClick:e=>{e.stopPropagation(),o()},children:"Start Chat"})]})]})};var v=r(2523);let j={selectedFilter:"recommended",selectedTimeline:"last-30-days",searchQuery:"",isLoading:!1},N=(e,s)=>{switch(s.type){case"SET_FILTER":return{...e,selectedFilter:s.payload};case"SET_TIMELINE":return{...e,selectedTimeline:s.payload};case"SET_SEARCH_QUERY":return{...e,searchQuery:s.payload};case"SET_LOADING":return{...e,isLoading:s.payload};case"RESET_FILTERS":return{...j};case"LOAD_FROM_STORAGE":return{...e,...s.payload};default:return e}},w=(0,l.createContext)(void 0),E={FILTER:"marketplace_filter",TIMELINE:"marketplace_timeline",SEARCH:"marketplace_search"},T=e=>{let{children:s}=e,[r,a]=(0,l.useReducer)(N,j);return(0,l.useEffect)(()=>{try{let e=sessionStorage.getItem(E.FILTER),s=sessionStorage.getItem(E.TIMELINE),r=sessionStorage.getItem(E.SEARCH),t={};e&&["trending","most-popular","top-rated","recommended","newest"].includes(e)&&(t.selectedFilter=e),s&&["last-7-days","last-30-days","all-time"].includes(s)&&(t.selectedTimeline=s),r&&(t.searchQuery=r),Object.keys(t).length>0&&a({type:"LOAD_FROM_STORAGE",payload:t})}catch(e){console.warn("Failed to load filter state from session storage:",e)}},[]),(0,l.useEffect)(()=>{try{sessionStorage.setItem(E.FILTER,r.selectedFilter),sessionStorage.setItem(E.TIMELINE,r.selectedTimeline),sessionStorage.setItem(E.SEARCH,r.searchQuery)}catch(e){console.warn("Failed to save filter state to session storage:",e)}},[r.selectedFilter,r.selectedTimeline,r.searchQuery]),(0,t.jsx)(w.Provider,{value:{state:r,actions:{setFilter:e=>{a({type:"SET_FILTER",payload:e})},setTimeline:e=>{a({type:"SET_TIMELINE",payload:e})},setSearchQuery:e=>{a({type:"SET_SEARCH_QUERY",payload:e})},resetFilters:()=>{a({type:"RESET_FILTERS"});try{sessionStorage.removeItem(E.FILTER),sessionStorage.removeItem(E.TIMELINE),sessionStorage.removeItem(E.SEARCH)}catch(e){console.warn("Failed to clear filter state from session storage:",e)}}}},children:s})},S=()=>{let e=(0,l.useContext)(w);if(void 0===e)throw Error("useFilters must be used within a FilterProvider");return e},C=[{id:"recommended",label:"Recommended for You",hasTimeline:!1,icon:"\uD83D\uDCA1"},{id:"trending",label:"Trending",hasTimeline:!1,icon:"\uD83D\uDCC8"},{id:"most-popular",label:"Most Popular",hasTimeline:!0,icon:"\uD83D\uDD25"},{id:"top-rated",label:"Top Rated",hasTimeline:!0,icon:"⭐"},{id:"newest",label:"Newest",hasTimeline:!1,icon:"\uD83C\uDD95"}],F=[{id:"last-30-days",label:"Last 30 Days",days:30},{id:"last-7-days",label:"Last 7 Days",days:7},{id:"all-time",label:"All Time"}];var k=r(133),A=r(7924),R=r(4416);let L=e=>{var s;let{className:r="",onFiltersChange:a}=e,{state:i,actions:d}=S(),[m,x]=(0,l.useState)(i.searchQuery),[h,g]=(0,l.useState)(null),p=C.find(e=>e.id===i.selectedFilter),f=(null==p?void 0:p.hasTimeline)||!1;(0,l.useEffect)(()=>{h&&clearTimeout(h);let e=setTimeout(()=>{d.setSearchQuery(m)},300);return g(e),()=>{e&&clearTimeout(e)}},[m,d,h]),(0,l.useEffect)(()=>{a&&a()},[i.selectedFilter,i.selectedTimeline,i.searchQuery,a]);let y=i.searchQuery||"recommended"!==i.selectedFilter||"last-30-days"!==i.selectedTimeline;return(0,t.jsxs)("div",{className:(0,n.cn)("space-y-4",r),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Find AI Experts"}),y&&(0,t.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{x(""),d.resetFilters()},className:"text-gray-500 hover:text-gray-700",children:[(0,t.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Reset Filters"]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(v.p,{type:"text",placeholder:"Search experts by name, description, or skills...",value:m,onChange:e=>{x(e.target.value)},className:"pl-10 pr-10 h-12 bg-white border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20"}),m&&(0,t.jsx)(u.$,{variant:"ghost",size:"sm",onClick:()=>{x(""),d.setSearchQuery("")},className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100",children:(0,t.jsx)(R.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:items-center",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(c,{options:C,selectedValue:i.selectedFilter,onSelect:d.setFilter,disabled:i.isLoading,className:"w-full"})}),f&&(0,t.jsx)("div",{className:"sm:w-auto",children:(0,t.jsx)(o,{options:F,selectedValue:i.selectedTimeline,onSelect:d.setTimeline,disabled:i.isLoading,className:"w-full sm:w-[150px]"})})]}),y&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 text-sm text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"Active filters:"}),"recommended"!==i.selectedFilter&&(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md",children:null==p?void 0:p.label}),f&&"last-30-days"!==i.selectedTimeline&&(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md",children:null==(s=F.find(e=>e.id===i.selectedTimeline))?void 0:s.label}),i.searchQuery&&(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md",children:['Search: "',i.searchQuery,'"']})]})]})};var I=r(1154),_=r(5339);let M=e=>{let{experts:s,loading:r=!1,error:a=null,onChatClick:l,onLoadMore:i,hasMore:c=!1,loadingMore:o=!1,className:d="",emptyStateMessage:m="No experts found matching your criteria.",searchTerm:x=""}=e;return r&&0===s.length?(0,t.jsx)("div",{className:(0,n.cn)("flex items-center justify-center py-12",d),children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(I.A,{className:"h-8 w-8 animate-spin text-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading experts..."})]})}):a?(0,t.jsx)("div",{className:(0,n.cn)("flex items-center justify-center py-12",d),children:(0,t.jsxs)("div",{className:"text-center max-w-md",children:[(0,t.jsx)(_.A,{className:"h-8 w-8 text-red-500 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Something went wrong"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:a}),(0,t.jsx)(u.$,{onClick:()=>window.location.reload(),variant:"outline",children:"Try Again"})]})}):r||0!==s.length?(0,t.jsxs)("div",{className:(0,n.cn)("space-y-6",d),children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[s.length," expert",1!==s.length?"s":""," found"]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:s.map(e=>(0,t.jsx)(b,{expert:e,onChatClick:l,className:"h-full",searchTerm:x},e.id))}),c&&i&&(0,t.jsx)("div",{className:"flex justify-center pt-6",children:(0,t.jsx)(u.$,{onClick:i,disabled:o,variant:"outline",size:"lg",className:"min-w-[120px]",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin mr-2"}),"Loading..."]}):"Load More"})}),o&&(0,t.jsx)("div",{className:"flex justify-center py-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Loading more experts..."})]})})]}):(0,t.jsx)("div",{className:(0,n.cn)("flex items-center justify-center py-12",d),children:(0,t.jsxs)("div",{className:"text-center max-w-md",children:[(0,t.jsx)(A.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"No experts found"}),(0,t.jsx)("p",{className:"text-gray-600",children:m})]})})};var Q=r(5365),P=r(1243),z=r(3904),D=r(3109),O=r(6932);let $=e=>{let{error:s,onRetry:r,showTrendingFallback:a=!0}=e,{actions:l}=S();return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(Q.Fc,{variant:"destructive",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsx)(Q.XL,{children:"Filter Error"}),(0,t.jsx)(Q.TN,{children:(null==s?void 0:s.message)||"We're having trouble loading the filtered results. This might be due to a temporary server issue."})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,t.jsx)(d.Zp,{children:(0,t.jsxs)(d.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,t.jsx)(z.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"font-semibold",children:"Try Again"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Retry the current filter selection. This often resolves temporary connection issues."}),(0,t.jsxs)(u.$,{onClick:()=>{r?r():window.location.reload()},className:"w-full",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Retry Current Filter"]})]})}),a&&(0,t.jsx)(d.Zp,{children:(0,t.jsxs)(d.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,t.jsx)(D.A,{className:"h-5 w-5 text-green-600"}),(0,t.jsx)("h3",{className:"font-semibold",children:"View Trending"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Switch to trending experts while we resolve the issue with your current filter."}),(0,t.jsxs)(u.$,{onClick:()=>{l.setFilter("trending"),l.setTimeline("last-30-days"),l.setSearchQuery("")},variant:"outline",className:"w-full",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Show Trending Experts"]})]})})]}),(0,t.jsx)(d.Zp,{className:"bg-gray-50",children:(0,t.jsx)(d.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(O.A,{className:"h-5 w-5 text-gray-500 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-sm mb-1",children:"Having persistent issues?"}),(0,t.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:"Try clearing your browser cache or switching to a different filter option. If the problem continues, please contact our support team."}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(u.$,{size:"sm",variant:"ghost",onClick:()=>localStorage.clear(),className:"text-xs h-7",children:"Clear Cache"}),(0,t.jsx)(u.$,{size:"sm",variant:"ghost",onClick:()=>window.location.href="/support",className:"text-xs h-7",children:"Contact Support"})]})]})]})})})]})};var H=r(5731);class V extends l.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,s){console.error("ErrorBoundary caught an error:",e,s),this.props.onError&&this.props.onError(e,s)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[200px] p-4",children:(0,t.jsxs)(Q.Fc,{className:"max-w-md",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsx)(Q.XL,{children:"Something went wrong"}),(0,t.jsxs)(Q.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-4",children:"We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists."}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(u.$,{onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Try Again"]}),(0,t.jsx)(u.$,{onClick:()=>window.location.reload(),size:"sm",children:"Refresh Page"})]})]})]})}):this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}}let q=e=>{let{className:s="",onChatClick:r}=e,{state:a}=S(),[i,c]=(0,l.useState)(1),[o,d]=(0,l.useState)([]),{data:m,loading:x,error:u,retry:h}=(e=>{let[s,r]=(0,l.useState)(null),[t,a]=(0,l.useState)(!1),[i,n]=(0,l.useState)(null),[c,o]=(0,l.useState)(0),[d,m]=(0,l.useState)(!1),x=(0,l.useRef)(0),u=(0,l.useCallback)(async function(){let s=arguments.length>0&&void 0!==arguments[0]&&arguments[0];a(!0),n(null),s&&(m(!0),x.current+=1,o(x.current));try{let s=await H.FH.getFilteredExperts({filter:e.filter,timeline:e.timeline,search:e.search,page:e.page,limit:e.limit});r(s.data)}catch(s){let e="An unexpected error occurred";s instanceof Error&&(e=s.message,s.message.includes("Failed to fetch")||s.message.includes("NetworkError")?e="Network connection error. Please check your internet connection and try again.":s.message.includes("500")?e="Server error. Our team has been notified. Please try again in a few moments.":s.message.includes("404")?e="The requested resource was not found. Please try a different filter.":s.message.includes("403")&&(e="Access denied. Please log in and try again.")),n(e),console.error("Error fetching filtered experts:",s),x.current<3&&(e.includes("Network")||e.includes("Server"))&&setTimeout(()=>{u(!0)},1e3*Math.pow(2,x.current))}finally{a(!1),m(!1)}},[e.filter,e.timeline,e.search,e.page,e.limit]);(0,l.useEffect)(()=>{x.current=0,o(0)},[e.filter,e.timeline,e.search,e.page,e.limit]),(0,l.useEffect)(()=>{u()},[u]);let h=(0,l.useCallback)(()=>{x.current=0,o(0),u()},[u]);return{data:s,loading:t,error:i,retryCount:c,isRetrying:d,refetch:h,retry:(0,l.useCallback)(()=>{u(!0)},[u]),clearError:(0,l.useCallback)(()=>{n(null),x.current=0,o(0)},[])}})({filter:a.selectedFilter,timeline:a.selectedTimeline,search:a.searchQuery,page:i,limit:20}),g=(0,l.useCallback)(()=>{c(1),d([])},[]),p=(0,l.useCallback)(()=>{m&&m.pagination.page<m.pagination.totalPages&&c(e=>e+1)},[m]);l.useEffect(()=>{m&&(1===i?d(m.experts):d(e=>[...e,...m.experts]))},[m,i]);let f=!!m&&m.pagination.page<m.pagination.totalPages,y=x&&i>1;return(0,t.jsxs)("div",{className:(0,n.cn)("container mx-auto px-4 py-8 space-y-8",s),children:[(0,t.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:(0,t.jsx)(L,{onFiltersChange:g})}),(0,t.jsx)("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:u?(0,t.jsx)($,{error:Error(u),onRetry:h,showTrendingFallback:"trending"!==a.selectedFilter}):(0,t.jsx)(M,{experts:o,loading:x&&1===i,error:null,onChatClick:r,onLoadMore:p,hasMore:f,loadingMore:y,searchTerm:a.searchQuery,emptyStateMessage:a.searchQuery?'No experts found for "'.concat(a.searchQuery,'". Try adjusting your search or filters.'):"No experts found matching your current filters. Try adjusting your criteria."})})]})},U=e=>(0,t.jsx)(V,{onError:(e,s)=>{console.error("Marketplace error:",e,s)},children:(0,t.jsx)(T,{children:(0,t.jsx)(V,{fallback:(0,t.jsx)($,{error:Error("Filter system error"),showTrendingFallback:!0}),children:(0,t.jsx)(q,{...e})})})}),W=()=>{let e=(0,a.useRouter)();return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"AI Experts"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Discover and connect with AI specialists"})]}),(0,t.jsx)(U,{onChatClick:s=>{e.push("/chat?expert=".concat(s))}})]})})}},5365:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>c,TN:()=>d,XL:()=>o});var t=r(5155),a=r(2115),l=r(2085),i=r(9434);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,s)=>{let{className:r,variant:a,...l}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:(0,i.cn)(n({variant:a}),r),...l})});c.displayName="Alert";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h5",{ref:s,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})});o.displayName="AlertTitle";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",r),...a})});d.displayName="AlertDescription"},6126:(e,s,r)=>{"use strict";r.d(s,{E:()=>n});var t=r(5155);r(2115);var a=r(2085),l=r(9434);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{className:(0,l.cn)(i({variant:r}),s),...a})}},9409:(e,s,r)=>{"use strict";r.d(s,{bq:()=>o,eb:()=>x,gC:()=>m,l6:()=>c,yv:()=>d});var t=r(5155),a=r(2115),l=r(6474),i=r(9434);let n=a.createContext({open:!1,setOpen:()=>{}}),c=e=>{let{value:s,onValueChange:r,children:l}=e,[i,c]=a.useState(!1);return(0,t.jsx)(n.Provider,{value:{value:s,onValueChange:r,open:i,setOpen:c},children:(0,t.jsx)("div",{className:"relative",children:l})})},o=e=>{let{className:s,children:r}=e,{open:c,setOpen:o}=a.useContext(n);return(0,t.jsxs)("button",{type:"button",className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),onClick:()=>o(!c),children:[r,(0,t.jsx)(l.A,{className:"h-4 w-4 opacity-50"})]})},d=e=>{let{placeholder:s,className:r}=e,{value:l}=a.useContext(n);return(0,t.jsx)("span",{className:(0,i.cn)("block truncate",r),children:l||s})},m=e=>{let{className:s,children:r}=e,{open:l,setOpen:c}=a.useContext(n);return l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>c(!1)}),(0,t.jsx)("div",{className:(0,i.cn)("absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md animate-in fade-in-0 zoom-in-95",s),children:r})]}):null},x=e=>{let{value:s,className:r,children:l,onSelect:c}=e,{onValueChange:o,setOpen:d}=a.useContext(n);return(0,t.jsx)("div",{className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),onClick:()=>{null==o||o(s),d(!1),null==c||c()},children:l})}}},e=>{e.O(0,[445,352,610,573,441,964,358],()=>e(e.s=103)),_N_E=e.O()}]);