2025-08-16T09:09:38.119Z - {
  "requestId": "s0e7bshou",
  "timestamp": "2025-08-16T09:09:38.118Z",
  "duration": "13ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 500,
    "headers": {},
    "body": {
      "success": false,
      "error": "Failed to get public experts",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "104 bytes"
  }
}
2025-08-16T09:09:38.124Z - {
  "requestId": "s0e7bshou",
  "timestamp": "2025-08-16T09:09:38.124Z",
  "duration": "19ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 500,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "success": false,
      "error": "Failed to get public experts",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "104 bytes"
  }
}
2025-08-16T09:10:08.767Z - {
  "requestId": "yi4wenwbl",
  "timestamp": "2025-08-16T09:10:08.767Z",
  "duration": "5ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Login failed",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "72 bytes"
  }
}
2025-08-16T09:10:08.768Z - {
  "requestId": "yi4wenwbl",
  "timestamp": "2025-08-16T09:10:08.768Z",
  "duration": "6ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Login failed",
      "message": "connect ECONNREFUSED 127.0.0.1:3306"
    },
    "size": "72 bytes"
  }
}
2025-08-16T09:19:10.599Z - {
  "requestId": "zi4vnpsez",
  "timestamp": "2025-08-16T09:19:10.598Z",
  "duration": "767ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "8df6d705-9df8-4d1a-84e0-8c391f834717"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:10.600Z - {
  "requestId": "zi4vnpsez",
  "timestamp": "2025-08-16T09:19:10.600Z",
  "duration": "769ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "8df6d705-9df8-4d1a-84e0-8c391f834717"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:17.771Z - {
  "requestId": "g7nmz0zwn",
  "timestamp": "2025-08-16T09:19:17.771Z",
  "duration": "187ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "c7194dc3-58e3-429c-b08f-45d42da9b58c"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:17.772Z - {
  "requestId": "g7nmz0zwn",
  "timestamp": "2025-08-16T09:19:17.772Z",
  "duration": "188ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "c7194dc3-58e3-429c-b08f-45d42da9b58c"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:19:39.369Z - {
  "requestId": "hilmk2bie",
  "timestamp": "2025-08-16T09:19:39.369Z",
  "duration": "11ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:19:39.370Z - {
  "requestId": "hilmk2bie",
  "timestamp": "2025-08-16T09:19:39.370Z",
  "duration": "12ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:22:03.478Z - {
  "requestId": "o3q7wtqp5",
  "timestamp": "2025-08-16T09:22:03.477Z",
  "duration": "46ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "[RESPONSE TOO LARGE - TRUNCATED]",
      "size": 12639,
      "preview": "{\"success\":true,\"experts\":[{\"id\":65,\"name\":\"english tutor\",\"description\":\"english tutor desc\",\"systemPrompt\":\"Anda adalah **English Coach**, tutor AI yang antusias, sabar, dan suportif, khusus untuk orang dewasa Indonesia (18 +). Semua modul pelajaran (Module 1, Module 2, dst.) sudah tersedia di sistem. Gunakan Bahasa Indonesia dalam penjelasan dan tanya-jawab, sambil menyisipkan materi Bahasa Inggris yang sedang dipelajari. Terapkan metode Soskratik: hanya satu pertanyaan per giliran, tunggu ja..."
    },
    "size": "12784 bytes"
  }
}
2025-08-16T09:22:03.480Z - {
  "requestId": "o3q7wtqp5",
  "timestamp": "2025-08-16T09:22:03.479Z",
  "duration": "48ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "[RESPONSE TOO LARGE - TRUNCATED]",
      "size": 12639,
      "preview": "{\"success\":true,\"experts\":[{\"id\":65,\"name\":\"english tutor\",\"description\":\"english tutor desc\",\"systemPrompt\":\"Anda adalah **English Coach**, tutor AI yang antusias, sabar, dan suportif, khusus untuk orang dewasa Indonesia (18 +). Semua modul pelajaran (Module 1, Module 2, dst.) sudah tersedia di sistem. Gunakan Bahasa Indonesia dalam penjelasan dan tanya-jawab, sambil menyisipkan materi Bahasa Inggris yang sedang dipelajari. Terapkan metode Soskratik: hanya satu pertanyaan per giliran, tunggu ja..."
    },
    "size": "12784 bytes"
  }
}
2025-08-16T09:22:03.499Z - {
  "requestId": "y841fwu9g",
  "timestamp": "2025-08-16T09:22:03.499Z",
  "duration": "17ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:22:03.500Z - {
  "requestId": "y841fwu9g",
  "timestamp": "2025-08-16T09:22:03.500Z",
  "duration": "18ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer c7194dc3-58e3-429c-b08f-45d42da9b58c"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:26:24.937Z - {
  "requestId": "xpxmg8iwu",
  "timestamp": "2025-08-16T09:26:24.936Z",
  "duration": "287ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:26:24.939Z - {
  "requestId": "xpxmg8iwu",
  "timestamp": "2025-08-16T09:26:24.938Z",
  "duration": "289ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:26:32.181Z - {
  "requestId": "3wrdv31fc",
  "timestamp": "2025-08-16T09:26:32.181Z",
  "duration": "8ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer 9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:26:32.181Z - {
  "requestId": "3wrdv31fc",
  "timestamp": "2025-08-16T09:26:32.181Z",
  "duration": "8ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer 9e28ddd4-e8eb-48c4-a45e-b2843909ced0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:46:03.799Z - {
  "requestId": "xzda7kfwq",
  "timestamp": "2025-08-16T09:46:03.798Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:46:03.807Z - {
  "requestId": "xzda7kfwq",
  "timestamp": "2025-08-16T09:46:03.806Z",
  "duration": "10ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:46:21.766Z - {
  "requestId": "4tlfxjmeh",
  "timestamp": "2025-08-16T09:46:21.765Z",
  "duration": "54ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "[RESPONSE TOO LARGE - TRUNCATED]",
      "size": 12639,
      "preview": "{\"success\":true,\"experts\":[{\"id\":65,\"name\":\"english tutor\",\"description\":\"english tutor desc\",\"systemPrompt\":\"Anda adalah **English Coach**, tutor AI yang antusias, sabar, dan suportif, khusus untuk orang dewasa Indonesia (18 +). Semua modul pelajaran (Module 1, Module 2, dst.) sudah tersedia di sistem. Gunakan Bahasa Indonesia dalam penjelasan dan tanya-jawab, sambil menyisipkan materi Bahasa Inggris yang sedang dipelajari. Terapkan metode Soskratik: hanya satu pertanyaan per giliran, tunggu ja..."
    },
    "size": "12784 bytes"
  }
}
2025-08-16T09:46:21.768Z - {
  "requestId": "4tlfxjmeh",
  "timestamp": "2025-08-16T09:46:21.768Z",
  "duration": "56ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/public",
    "path": "/public",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "[RESPONSE TOO LARGE - TRUNCATED]",
      "size": 12639,
      "preview": "{\"success\":true,\"experts\":[{\"id\":65,\"name\":\"english tutor\",\"description\":\"english tutor desc\",\"systemPrompt\":\"Anda adalah **English Coach**, tutor AI yang antusias, sabar, dan suportif, khusus untuk orang dewasa Indonesia (18 +). Semua modul pelajaran (Module 1, Module 2, dst.) sudah tersedia di sistem. Gunakan Bahasa Indonesia dalam penjelasan dan tanya-jawab, sambil menyisipkan materi Bahasa Inggris yang sedang dipelajari. Terapkan metode Soskratik: hanya satu pertanyaan per giliran, tunggu ja..."
    },
    "size": "12784 bytes"
  }
}
2025-08-16T09:46:21.959Z - {
  "requestId": "f7lidzq25",
  "timestamp": "2025-08-16T09:46:21.959Z",
  "duration": "3ms",
  "request": {
    "method": "GET",
    "url": "/uploads/image-1753423415732-240870668.jpeg",
    "path": "/",
    "query": {},
    "headers": {
      "user-agent": "node"
    },
    "ip": "::1"
  },
  "response": {
    "statusCode": 404,
    "headers": {},
    "body": {
      "success": false,
      "message": "Route not found",
      "path": "/uploads/image-1753423415732-240870668.jpeg"
    },
    "size": "98 bytes"
  }
}
2025-08-16T09:46:21.960Z - {
  "requestId": "f7lidzq25",
  "timestamp": "2025-08-16T09:46:21.959Z",
  "duration": "3ms",
  "request": {
    "method": "GET",
    "url": "/uploads/image-1753423415732-240870668.jpeg",
    "path": "/",
    "query": {},
    "headers": {
      "user-agent": "node"
    },
    "ip": "::1"
  },
  "response": {
    "statusCode": 404,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "success": false,
      "message": "Route not found",
      "path": "/uploads/image-1753423415732-240870668.jpeg"
    },
    "size": "98 bytes"
  }
}
2025-08-16T09:46:27.535Z - {
  "requestId": "90h9b8fw9",
  "timestamp": "2025-08-16T09:46:27.535Z",
  "duration": "339ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "a71526c6-cfa4-4890-ad99-f6ac918e9abd"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:46:27.536Z - {
  "requestId": "90h9b8fw9",
  "timestamp": "2025-08-16T09:46:27.536Z",
  "duration": "340ms",
  "request": {
    "method": "POST",
    "url": "/api/users/login",
    "path": "/login",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "content-type": "application/json"
    },
    "body": {
      "phone": "+6282139817939",
      "password": "654321"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "message": "Login successful",
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>",
        "token": "a71526c6-cfa4-4890-ad99-f6ac918e9abd"
      }
    },
    "size": "171 bytes"
  }
}
2025-08-16T09:46:32.883Z - {
  "requestId": "qt0zx87yv",
  "timestamp": "2025-08-16T09:46:32.883Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:46:32.884Z - {
  "requestId": "qt0zx87yv",
  "timestamp": "2025-08-16T09:46:32.884Z",
  "duration": "2ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:46:32.906Z - {
  "requestId": "dhv94sya2",
  "timestamp": "2025-08-16T09:46:32.906Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:46:32.906Z - {
  "requestId": "dhv94sya2",
  "timestamp": "2025-08-16T09:46:32.906Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:48:28.205Z - {
  "requestId": "3a5har9ks",
  "timestamp": "2025-08-16T09:48:28.205Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:48:28.209Z - {
  "requestId": "3a5har9ks",
  "timestamp": "2025-08-16T09:48:28.209Z",
  "duration": "5ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:48:49.955Z - {
  "requestId": "voum59itb",
  "timestamp": "2025-08-16T09:48:49.955Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:48:49.956Z - {
  "requestId": "voum59itb",
  "timestamp": "2025-08-16T09:48:49.956Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:48:59.516Z - {
  "requestId": "tjudog2nq",
  "timestamp": "2025-08-16T09:48:59.516Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:48:59.517Z - {
  "requestId": "tjudog2nq",
  "timestamp": "2025-08-16T09:48:59.517Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:09.899Z - {
  "requestId": "wovo1s7ey",
  "timestamp": "2025-08-16T09:49:09.899Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:09.911Z - {
  "requestId": "wovo1s7ey",
  "timestamp": "2025-08-16T09:49:09.910Z",
  "duration": "12ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:10.022Z - {
  "requestId": "603cokrnj",
  "timestamp": "2025-08-16T09:49:10.022Z",
  "duration": "26ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:49:10.024Z - {
  "requestId": "603cokrnj",
  "timestamp": "2025-08-16T09:49:10.023Z",
  "duration": "27ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:49:20.698Z - {
  "requestId": "k962pm096",
  "timestamp": "2025-08-16T09:49:20.698Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:20.699Z - {
  "requestId": "k962pm096",
  "timestamp": "2025-08-16T09:49:20.699Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:21.073Z - {
  "requestId": "yij4wm8eh",
  "timestamp": "2025-08-16T09:49:21.072Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:21.073Z - {
  "requestId": "yij4wm8eh",
  "timestamp": "2025-08-16T09:49:21.073Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:30.891Z - {
  "requestId": "oqiq21n4y",
  "timestamp": "2025-08-16T09:49:30.891Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:30.892Z - {
  "requestId": "oqiq21n4y",
  "timestamp": "2025-08-16T09:49:30.892Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:34.679Z - {
  "requestId": "mklllg8ov",
  "timestamp": "2025-08-16T09:49:34.679Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:34.681Z - {
  "requestId": "mklllg8ov",
  "timestamp": "2025-08-16T09:49:34.681Z",
  "duration": "2ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:34.747Z - {
  "requestId": "devboqzsy",
  "timestamp": "2025-08-16T09:49:34.747Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:34.764Z - {
  "requestId": "devboqzsy",
  "timestamp": "2025-08-16T09:49:34.764Z",
  "duration": "17ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:34.826Z - {
  "requestId": "8ajlcel7y",
  "timestamp": "2025-08-16T09:49:34.826Z",
  "duration": "33ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:49:34.831Z - {
  "requestId": "8ajlcel7y",
  "timestamp": "2025-08-16T09:49:34.831Z",
  "duration": "38ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:49:42.122Z - {
  "requestId": "qhh1w12h4",
  "timestamp": "2025-08-16T09:49:42.122Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:49:42.123Z - {
  "requestId": "qhh1w12h4",
  "timestamp": "2025-08-16T09:49:42.123Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:07.482Z - {
  "requestId": "4qsgdo4m5",
  "timestamp": "2025-08-16T09:50:07.482Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:07.484Z - {
  "requestId": "4qsgdo4m5",
  "timestamp": "2025-08-16T09:50:07.484Z",
  "duration": "2ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:07.547Z - {
  "requestId": "j2fledtxk",
  "timestamp": "2025-08-16T09:50:07.547Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:07.548Z - {
  "requestId": "j2fledtxk",
  "timestamp": "2025-08-16T09:50:07.548Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:07.588Z - {
  "requestId": "ibkk54brd",
  "timestamp": "2025-08-16T09:50:07.588Z",
  "duration": "29ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:50:07.589Z - {
  "requestId": "ibkk54brd",
  "timestamp": "2025-08-16T09:50:07.589Z",
  "duration": "30ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:50:38.698Z - {
  "requestId": "704wsn53x",
  "timestamp": "2025-08-16T09:50:38.698Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:38.699Z - {
  "requestId": "704wsn53x",
  "timestamp": "2025-08-16T09:50:38.699Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:38.762Z - {
  "requestId": "wsh80ch9r",
  "timestamp": "2025-08-16T09:50:38.762Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:38.763Z - {
  "requestId": "wsh80ch9r",
  "timestamp": "2025-08-16T09:50:38.763Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:50:38.779Z - {
  "requestId": "qfznfherc",
  "timestamp": "2025-08-16T09:50:38.778Z",
  "duration": "6ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:50:38.779Z - {
  "requestId": "qfznfherc",
  "timestamp": "2025-08-16T09:50:38.779Z",
  "duration": "7ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:51:57.477Z - {
  "requestId": "k0fa8iglh",
  "timestamp": "2025-08-16T09:51:57.477Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:51:57.478Z - {
  "requestId": "k0fa8iglh",
  "timestamp": "2025-08-16T09:51:57.478Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:51:57.583Z - {
  "requestId": "l666teyt6",
  "timestamp": "2025-08-16T09:51:57.583Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:51:57.584Z - {
  "requestId": "l666teyt6",
  "timestamp": "2025-08-16T09:51:57.584Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:51:57.600Z - {
  "requestId": "zuf7ts7be",
  "timestamp": "2025-08-16T09:51:57.600Z",
  "duration": "6ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:51:57.601Z - {
  "requestId": "zuf7ts7be",
  "timestamp": "2025-08-16T09:51:57.601Z",
  "duration": "7ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:52:16.314Z - {
  "requestId": "pjijurzbu",
  "timestamp": "2025-08-16T09:52:16.313Z",
  "duration": "0ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:52:16.314Z - {
  "requestId": "pjijurzbu",
  "timestamp": "2025-08-16T09:52:16.314Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=recommended&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "recommended",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:52:16.420Z - {
  "requestId": "ukxgknzly",
  "timestamp": "2025-08-16T09:52:16.420Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {},
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:52:16.420Z - {
  "requestId": "ukxgknzly",
  "timestamp": "2025-08-16T09:52:16.420Z",
  "duration": "1ms",
  "request": {
    "method": "GET",
    "url": "/api/experts/filtered?filter=top-rated&timeline=last-30-days&page=1&limit=20",
    "path": "/filtered",
    "query": {
      "filter": "top-rated",
      "timeline": "last-30-days",
      "page": "1",
      "limit": "20"
    },
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 401,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "error": "Access token is required",
      "message": "Please provide a valid token in the Authorization header or token header"
    },
    "size": "121 bytes"
  }
}
2025-08-16T09:52:16.437Z - {
  "requestId": "q22irqjyn",
  "timestamp": "2025-08-16T09:52:16.436Z",
  "duration": "4ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {},
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
2025-08-16T09:52:16.437Z - {
  "requestId": "q22irqjyn",
  "timestamp": "2025-08-16T09:52:16.437Z",
  "duration": "5ms",
  "request": {
    "method": "GET",
    "url": "/api/users/profile",
    "path": "/profile",
    "query": {},
    "headers": {
      "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0",
      "authorization": "Bearer a71526c6-cfa4-4890-ad99-f6ac918e9abd"
    },
    "ip": "::ffff:127.0.0.1"
  },
  "response": {
    "statusCode": 200,
    "headers": {
      "content-type": "application/json; charset=utf-8"
    },
    "body": {
      "user": {
        "user_id": 55,
        "phone": "+6282139817939",
        "name": "martin",
        "email": "<EMAIL>"
      }
    },
    "size": "95 bytes"
  }
}
