(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{9690:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(5155),a=r(2115),l=r(5695),n=r(6874),d=r.n(n),i=r(283),o=r(9420),c=r(2919),m=r(8749),x=r(2657),h=r(2138);let u=()=>{let[e,s]=(0,a.useState)({phone:"",password:""}),[r,n]=(0,a.useState)(!1),[u,b]=(0,a.useState)(!1),[p,g]=(0,a.useState)(""),{login:f}=(0,i.A)(),j=(0,l.useRouter)(),N=e=>{let{name:r,value:t}=e.target;s(e=>({...e,[r]:t})),p&&g("")},y=async s=>{if(s.preventDefault(),e.phone.trim()?!!e.password||(g("Password is required"),!1):(g("Phone number is required"),!1)){b(!0),g("");try{await f(e.phone.trim(),e.password),j.push("/dashboard")}catch(e){g(e.message||"Login failed. Please check your credentials.")}finally{b(!1)}}};return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"max-w-md w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome Back"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Sign in to your AI Trainer Hub account"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,t.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[p&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,t.jsx)("p",{className:"text-red-600 text-sm",children:p})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:N,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"+*************",disabled:u})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:r?"text":"password",id:"password",name:"password",value:e.password,onChange:N,className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Enter your password",disabled:u}),(0,t.jsx)("button",{type:"button",onClick:()=>n(!r),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:u,children:r?(0,t.jsx)(m.A,{className:"w-5 h-5"}):(0,t.jsx)(x.A,{className:"w-5 h-5"})})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)(d(),{href:"/forgot-password",className:"text-sm text-blue-600 hover:text-blue-700 transition-colors",children:"Forgot your password?"})}),(0,t.jsx)("button",{type:"submit",disabled:u,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:u?(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,t.jsxs)(t.Fragment,{children:["Sign In",(0,t.jsx)(h.A,{className:"ml-2 w-4 h-4"})]})})]}),(0,t.jsxs)("div",{className:"mt-6 space-y-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or"})})]}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",(0,t.jsx)(d(),{href:"/register",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Create one now"})]})})]}),(0,t.jsxs)("div",{className:"mt-6 bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Demo Credentials"}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Phone:"})," +*************"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Password:"})," password"]})]})]})]})]})})}},9844:(e,s,r)=>{Promise.resolve().then(r.bind(r,9690))}},e=>{e.O(0,[445,874,319,441,964,358],()=>e(e.s=9844)),_N_E=e.O()}]);