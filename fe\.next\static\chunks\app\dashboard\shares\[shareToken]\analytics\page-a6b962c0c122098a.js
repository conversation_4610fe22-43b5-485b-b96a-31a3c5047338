(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[820],{5843:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>A});var a=t(5155),r=t(5695),i=t(2115),c=t(6695),l=t(285),n=t(6126),d=t(7313),o=t(9409),x=t(4738),m=t(6767),h=t(2713),u=t(5169),j=t(3904),p=t(9407),f=t(2103),v=t(1366),g=t(7580),N=t(9074),b=t(4869),y=t(7481),w=t(5731);function k(e){let{shareToken:s}=e,{toast:t}=(0,y.d)(),k=(0,r.useRouter)(),[C,A]=(0,i.useState)(null),[S,R]=(0,i.useState)(!0),[B,D]=(0,i.useState)("7d"),[E,L]=(0,i.useState)(!1),T=(0,i.useCallback)(async()=>{try{R(!0);let e=await w.FH.get("/sharing/analytics/".concat(s,"?timeRange=").concat(B),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});e.data.success&&A(e.data.data)}catch(e){console.error("Error loading analytics:",e),t({title:"Error",description:"Failed to load analytics data. Please try again.",variant:"destructive"})}finally{R(!1)}},[s,B,t]);(0,i.useEffect)(()=>{T()},[s,B,T]);let Z=async()=>{L(!0),await T(),L(!1),t({title:"Analytics Refreshed",description:"Analytics data has been updated with the latest information."})},F=async()=>{try{let e=await w.FH.get("/sharing/analytics/".concat(s,"/export?timeRange=").concat(B,"&format=csv"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}}),a=new Blob([e.data],{type:"text/csv"}),r=window.URL.createObjectURL(a),i=document.createElement("a");i.href=r,i.download="share-analytics-".concat(s,"-").concat(B,".csv"),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(r),t({title:"Export Complete",description:"Analytics data has been exported successfully."})}catch(e){console.error("Error exporting data:",e),t({title:"Export Failed",description:"Failed to export analytics data. Please try again.",variant:"destructive"})}};if(S)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4"})]})},s))})]})});if(!C)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Analytics Data"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Analytics data is not available for this share."}),(0,a.jsxs)(l.$,{onClick:()=>k.back(),className:"mt-4",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Go Back"]})]});let{share:W,analytics:z}=C;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(l.$,{variant:"ghost",onClick:()=>k.back(),children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:W.expert.name}),(0,a.jsx)("p",{className:"text-gray-600",children:"Share Analytics"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-4 sm:mt-0",children:[(0,a.jsxs)(o.l6,{value:B,onValueChange:D,children:[(0,a.jsx)(o.bq,{className:"w-32",children:(0,a.jsx)(o.yv,{})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"24h",children:"Last 24h"}),(0,a.jsx)(o.eb,{value:"7d",children:"Last 7 days"}),(0,a.jsx)(o.eb,{value:"30d",children:"Last 30 days"}),(0,a.jsx)(o.eb,{value:"90d",children:"Last 90 days"})]})]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:Z,disabled:E,children:[(0,a.jsx)(j.A,{className:"h-4 w-4 mr-2 ".concat(E?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:F,children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Export"]})]})]}),(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:W.expert.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:W.expert.description})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(n.E,{variant:W.isActive?"default":"secondary",children:W.isActive?"Active":"Inactive"}),(0,a.jsx)(n.E,{variant:"outline",className:"capitalize",children:W.shareType})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Created"}),(0,a.jsx)("p",{className:"font-medium",children:new Date(W.createdAt).toLocaleDateString()})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600 mb-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Total Clicks"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:z.totalClicks.toLocaleString()}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[z.uniqueVisitors," unique visitors"]})]})}),(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-green-600 mb-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Conversions"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-900",children:z.totalConversions.toLocaleString()}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[z.conversionRate.toFixed(1),"% conversion rate"]})]})}),(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-purple-600 mb-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Returning Visitors"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:z.returningVisitors.toLocaleString()}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[(z.returningVisitors/z.uniqueVisitors*100).toFixed(1),"% return rate"]})]})}),(0,a.jsx)(c.Zp,{children:(0,a.jsxs)(c.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-orange-600 mb-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Avg. Session"})]}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-orange-900",children:[Math.round(z.avgSessionDuration/60),"m"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[z.avgSessionDuration,"s average"]})]})})]}),(0,a.jsxs)(d.tU,{defaultValue:"traffic",className:"space-y-6",children:[(0,a.jsxs)(d.j7,{children:[(0,a.jsx)(d.Xi,{value:"traffic",children:"Traffic Sources"}),(0,a.jsx)(d.Xi,{value:"devices",children:"Devices"}),(0,a.jsx)(d.Xi,{value:"geography",children:"Geography"}),(0,a.jsx)(d.Xi,{value:"timeline",children:"Timeline"})]}),(0,a.jsx)(d.av,{value:"traffic",className:"space-y-6",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Top Referrers"}),(0,a.jsx)(c.BT,{children:"Sources that drive the most traffic to your shared expert"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:z.topReferrers.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.source||"Direct"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.percentage.toFixed(1),"% of traffic"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-bold",children:e.clicks}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"clicks"})]})]},s))})})]})}),(0,a.jsx)(d.av,{value:"devices",className:"space-y-6",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Device Breakdown"}),(0,a.jsx)(c.BT,{children:"How users access your shared expert across different devices"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:Object.entries(z.deviceBreakdown).map(e=>{let[s,t]=e,r=t/z.totalClicks*100;return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(e=>{switch(e){case"desktop":default:return(0,a.jsx)(x.A,{className:"h-4 w-4"});case"mobile":case"tablet":return(0,a.jsx)(m.A,{className:"h-4 w-4"})}})(s),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium capitalize",children:s}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[r.toFixed(1),"% of traffic"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"font-bold",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"clicks"})]})]},s)})})})]})}),(0,a.jsx)(d.av,{value:"geography",className:"space-y-6",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Geographic Distribution"}),(0,a.jsx)(c.BT,{children:"Where your shared expert is being accessed from around the world"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:z.geographicData.slice(0,10).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(b.A,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.country}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[(e.clicks/z.totalClicks*100).toFixed(1),"% of traffic"]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-bold",children:[e.clicks," clicks"]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.conversions," conversions"]})]})]},s))})})]})}),(0,a.jsx)(d.av,{value:"timeline",className:"space-y-6",children:(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Performance Over Time"}),(0,a.jsx)(c.BT,{children:"Track how your shared expert performs across different time periods"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-4",children:"Daily Performance"}),(0,a.jsx)("div",{className:"space-y-2",children:z.timeSeriesData.slice(-7).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded",children:[(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"font-medium",children:new Date(e.date).toLocaleDateString()})}),(0,a.jsxs)("div",{className:"flex space-x-6 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-bold text-blue-600",children:e.clicks}),(0,a.jsx)("p",{className:"text-gray-600",children:"clicks"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"font-bold text-green-600",children:e.conversions}),(0,a.jsx)("p",{className:"text-gray-600",children:"conversions"})]})]})]},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-4",children:"Peak Hours"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:z.hourlyData.sort((e,s)=>s.clicks-e.clicks).slice(0,8).map((e,s)=>(0,a.jsxs)("div",{className:"text-center p-3 border rounded",children:[(0,a.jsxs)("p",{className:"font-bold",children:[e.hour,":00"]}),(0,a.jsxs)("p",{className:"text-sm text-blue-600",children:[e.clicks," clicks"]}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:[e.conversions," chats"]})]},s))})]})]})})]})})]})]})}function C(){let e=(0,r.useParams)().shareToken;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"py-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Share Analytics"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Detailed performance insights for your shared expert"})]})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(k,{shareToken:e})})]})}function A(){return(0,a.jsx)(C,{})}},6126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(5155);t(2115);var r=t(2085),i=t(9434);let c=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(c({variant:t}),s),...r})}},7313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>n,av:()=>d,j7:()=>l,tU:()=>c});var a=t(5155);t(2115);var r=t(888),i=t(9434);function c(e){let{className:s,...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",s),...t})}},7481:(e,s,t)=>{"use strict";t.d(s,{d:()=>n});var a=t(2115);let r={toasts:[]},i=[];function c(){i.forEach(e=>e(r))}function l(e){let s=r.toasts.findIndex(s=>s.id===e);s>-1&&(r.toasts.splice(s,1),c())}function n(){let[e,s]=(0,a.useState)(r),t=(0,a.useCallback)(e=>(i.push(e),()=>{i=i.filter(s=>s!==e)}),[]),n=(0,a.useCallback)(e=>(function(e){let s=Math.random().toString(36).substr(2,9),t={id:s,duration:5e3,...e};return r.toasts.push(t),c(),t.duration&&t.duration>0&&setTimeout(()=>{l(s)},t.duration),s})(e),[]),d=(0,a.useCallback)(e=>{l(e)},[]);return(0,a.useState)(()=>t(s)),{toast:n,dismiss:d,toasts:e.toasts}}},7929:(e,s,t)=>{Promise.resolve().then(t.bind(t,5843))},9409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>d,eb:()=>m,gC:()=>x,l6:()=>n,yv:()=>o});var a=t(5155),r=t(2115),i=t(6474),c=t(9434);let l=r.createContext({open:!1,setOpen:()=>{}}),n=e=>{let{value:s,onValueChange:t,children:i}=e,[c,n]=r.useState(!1);return(0,a.jsx)(l.Provider,{value:{value:s,onValueChange:t,open:c,setOpen:n},children:(0,a.jsx)("div",{className:"relative",children:i})})},d=e=>{let{className:s,children:t}=e,{open:n,setOpen:d}=r.useContext(l);return(0,a.jsxs)("button",{type:"button",className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),onClick:()=>d(!n),children:[t,(0,a.jsx)(i.A,{className:"h-4 w-4 opacity-50"})]})},o=e=>{let{placeholder:s,className:t}=e,{value:i}=r.useContext(l);return(0,a.jsx)("span",{className:(0,c.cn)("block truncate",t),children:i||s})},x=e=>{let{className:s,children:t}=e,{open:i,setOpen:n}=r.useContext(l);return i?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>n(!1)}),(0,a.jsx)("div",{className:(0,c.cn)("absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md animate-in fade-in-0 zoom-in-95",s),children:t})]}):null},m=e=>{let{value:s,className:t,children:i,onSelect:n}=e,{onValueChange:d,setOpen:o}=r.useContext(l);return(0,a.jsx)("div",{className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),onClick:()=>{null==d||d(s),o(!1),null==n||n()},children:i})}}},e=>{e.O(0,[445,352,888,744,573,441,964,358],()=>e(e.s=7929)),_N_E=e.O()}]);