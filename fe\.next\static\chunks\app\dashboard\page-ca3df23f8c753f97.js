(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},4835:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(s,{useSearchParams:function(){return r.useSearchParams}})},8697:(e,s,a)=>{Promise.resolve().then(a.bind(a,9662))},8883:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9662:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var r=a(5155),t=a(2115),l=a(5695),i=a(283),n=a(4835),c=a(1007),d=a(9420),o=a(8883);let x=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var m=a(6874),h=a.n(m);let u=()=>{let{user:e,isAuthenticated:s,isLoading:a,logout:m}=(0,i.A)(),u=(0,l.useRouter)();(0,t.useEffect)(()=>{a||s||u.push("/login")},[s,a,u]);let g=async()=>{await m(),u.push("/login")};return a?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"})}):s&&e?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"AI Trainer Hub"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",e.name]}),(0,r.jsxs)("button",{onClick:g,className:"inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Logout"]})]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your AI experts and training sessions"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Profile"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Your account information"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Full Name"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.phone}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Phone Number"})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"w-4 h-4 text-gray-400 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Email Address"})]})]})]}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,r.jsxs)(h(),{href:"/profile",className:"w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(x,{className:"w-4 h-4 mr-2"}),"Edit Profile"]})})]})}),(0,r.jsxs)("div",{className:"lg:col-span-2",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(h(),{href:"/ai-experts",className:"group",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)("span",{className:"text-blue-600 group-hover:text-blue-700 transition-colors",children:"→"})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Browse AI Experts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Discover and connect with AI specialists"})]})}),(0,r.jsx)(h(),{href:"/experts",className:"group",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,r.jsx)("span",{className:"text-blue-600 group-hover:text-blue-700 transition-colors",children:"→"})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Manage Experts"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Create and manage your AI experts"})]})}),(0,r.jsx)(h(),{href:"/history",className:"group",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("span",{className:"text-blue-600 group-hover:text-blue-700 transition-colors",children:"→"})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Chat History"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"View your previous conversations"})]})}),(0,r.jsx)(h(),{href:"/chat",className:"group",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"})})}),(0,r.jsx)("span",{className:"text-blue-600 group-hover:text-blue-700 transition-colors",children:"→"})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Start New Chat"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Begin a conversation with an AI expert"})]})})]}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Recent Activity"}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm text-gray-900",children:"Account verified successfully"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Welcome to AI Trainer Hub!"})]}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Just now"})]})})]})})]})]})]})]}):null}}},e=>{e.O(0,[445,874,283,441,964,358],()=>e(e.s=8697)),_N_E=e.O()}]);