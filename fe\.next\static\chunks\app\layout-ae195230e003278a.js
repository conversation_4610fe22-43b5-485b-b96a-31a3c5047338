(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[558],{347:()=>{},5332:(e,t,n)=>{"use strict";n.d(t,{F:()=>i,SocketProvider:()=>d});var o=n(5155),r=n(2115),s=n(4298),l=n(283),a=n(9509);let c=(0,r.createContext)({socket:null,isConnected:!1,connectionError:null,joinChat:()=>{},leaveChat:()=>{},sendChatMessage:()=>{},currentChatRoom:null,reconnect:()=>{}}),i=()=>{let e=(0,r.useContext)(c);if(!e)throw Error("useSocket must be used within a SocketProvider");return e},d=e=>{let{children:t}=e,{token:n,isAuthenticated:i,isLoading:d}=(0,l.A)(),[u,h]=(0,r.useState)(null),[x,m]=(0,r.useState)(!1),[g,p]=(0,r.useState)(null),[b,f]=(0,r.useState)(null),v=(0,r.useRef)(0),y=(0,r.useRef)(null);(0,r.useEffect)(()=>{if(console.log("\uD83D\uDD0C Socket initialization check:",{isLoading:d,isAuthenticated:i,hasToken:!!n,tokenLength:null==n?void 0:n.length,tokenStart:(null==n?void 0:n.substring(0,20))+"..."}),d)return void console.log("\uD83D\uDD0C Auth still loading, waiting...");if(y.current&&(console.log("\uD83E\uDDF9 Cleaning up existing socket connection"),y.current.disconnect(),y.current=null,h(null),m(!1),f(null)),!i||!n){console.log("\uD83D\uDD0C User not authenticated, skipping socket connection"),p("Please log in to use real-time chat");return}let e=a.env.NEXT_PUBLIC_API_URL||"http://localhost:3001";console.log("\uD83D\uDD0C Initializing Socket.IO connection to:",e);let t=(0,s.io)(e,{auth:{token:n},transports:["polling","websocket"],timeout:2e4,reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3,reconnectionDelayMax:5e3,upgrade:!0,rememberUpgrade:!1});return t.on("connect",()=>{console.log("✅ Socket connected:",t.id),console.log("\uD83D\uDE80 Transport used:",t.io.engine.transport.name),m(!0),p(null),v.current=0}),t.on("disconnect",e=>{console.log("❌ Socket disconnected:",e),m(!1),f(null),"io server disconnect"===e&&t.connect()}),t.on("connect_error",e=>{console.error("\uD83D\uDD0C Socket connection error:",e),console.error("\uD83D\uDD0C Error details:",{message:e.message,name:e.name,stack:e.stack}),p(e.message||"Connection failed"),m(!1),v.current+=1,v.current>=5&&p("Failed to connect after multiple attempts")}),t.on("chat_joined",e=>{console.log("\uD83C\uDFE0 Joined chat room:",e),f(e.room)}),t.on("chat_error",e=>{console.error("\uD83D\uDCAC Chat error:",e)}),t.on("typing_start",e=>{console.log("⌨️ User started typing:",e)}),t.on("typing_stop",e=>{console.log("⌨️ User stopped typing:",e)}),t.on("error",e=>{console.error("\uD83D\uDD10 Socket authentication error:",e),e.message&&e.message.includes("token")?(p("Authentication failed - please log in again"),localStorage.removeItem("token")):p("Connection failed"),m(!1)}),t.io.engine.on("upgrade",()=>{console.log("\uD83D\uDD04 Transport upgraded to:",t.io.engine.transport.name)}),t.io.engine.on("upgradeError",e=>{console.error("❌ Transport upgrade failed:",e)}),y.current=t,h(t),()=>{console.log("\uD83E\uDDF9 Cleaning up socket connection on unmount/change"),y.current&&(y.current.disconnect(),y.current=null)}},[d,i,n]);let j=(0,r.useCallback)((e,t)=>{if(!u||!x)return void console.warn("⚠️ Cannot join chat: socket not connected");console.log("\uD83C\uDFE0 Joining chat:",{expertId:e,sessionId:t}),u.emit("join_chat",{expertId:e,sessionId:t})},[u,x]),k=(0,r.useCallback)(()=>{if(!u||!x)return void console.warn("⚠️ Cannot leave chat: socket not connected");console.log("\uD83D\uDEAA Leaving chat"),u.emit("leave_chat"),f(null)},[u,x]),w=(0,r.useCallback)((e,t,n)=>{if(!u||!x)return void console.warn("⚠️ Cannot send message: socket not connected");console.log("\uD83D\uDCE4 Sending chat message:",{message:e.substring(0,50)+"...",expertId:t,sessionId:n}),u.emit("start_chat_stream",{message:e,expertId:t,sessionId:n})},[u,x]),N=(0,r.useCallback)(()=>{console.log("\uD83D\uDD04 Manual reconnection requested"),u&&u.disconnect(),h(null),m(!1),p(null),f(null),v.current=0,n&&setTimeout(()=>{console.log("\uD83D\uDD04 Reconnection will be handled by useEffect")},100)},[u,n]);return(0,o.jsx)(c.Provider,{value:{socket:u,isConnected:x,connectionError:g,joinChat:j,leaveChat:k,sendChatMessage:w,currentChatRoom:b,reconnect:N},children:t})}},5506:(e,t,n)=>{"use strict";n.d(t,{default:()=>p});var o=n(5155),r=n(6874),s=n.n(r),l=n(5695),a=n(2115),c=n(1007),i=n(7580),d=n(9785),u=n(6474),h=n(4835),x=n(306),m=n(2318),g=n(283);let p=()=>{let e=(0,l.usePathname)(),t=(0,l.useRouter)(),{user:n,isAuthenticated:r,logout:p}=(0,g.A)(),[b,f]=(0,a.useState)(!1),[v,y]=(0,a.useState)(!1),j=[{href:"/ai-experts",label:"AI Experts"},...r?[{href:"/history",label:"History"}]:[]],k=[{href:"/profile",label:"My Profile",icon:c.A},{href:"/affiliate",label:"Affiliate Program",icon:i.A},{href:"/balance",label:"Saldo",icon:d.A}],w=async()=>{await p(),y(!1),t.push("/")};return(0,o.jsx)("nav",{className:"bg-white shadow-lg border-b border-gray-100",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,o.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,o.jsx)(s(),{href:"/",className:"text-2xl font-bold",style:{color:"#1E3A8A"},children:"PakarAI"}),(0,o.jsxs)("div",{className:"flex space-x-6",children:[j.map(t=>(0,o.jsx)(s(),{href:t.href,className:"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ".concat(e===t.href?"text-white shadow-lg":"text-gray-600 hover:text-white hover:shadow-md"),style:e===t.href?{backgroundColor:"#1E3A8A"}:{backgroundColor:"transparent"},onMouseEnter:n=>{e!==t.href&&(n.currentTarget.style.backgroundColor="#1E3A8A")},onMouseLeave:n=>{e!==t.href&&(n.currentTarget.style.backgroundColor="transparent")},children:t.label},t.href)),r&&(0,o.jsxs)("div",{className:"relative",onMouseEnter:()=>f(!0),onMouseLeave:()=>f(!1),children:[(0,o.jsxs)("button",{className:"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-1 ".concat(e.startsWith("/experts")?"text-white shadow-lg":"text-gray-600 hover:text-white hover:shadow-md"),style:e.startsWith("/experts")?{backgroundColor:"#1E3A8A"}:{backgroundColor:"transparent"},onMouseEnter:t=>{e.startsWith("/experts")||(t.currentTarget.style.backgroundColor="#1E3A8A")},onMouseLeave:t=>{e.startsWith("/experts")||(t.currentTarget.style.backgroundColor="transparent")},children:["My Experts",(0,o.jsx)(u.A,{className:"w-4 h-4"})]}),b&&(0,o.jsx)("div",{className:"absolute top-full left-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[{href:"/experts?view=overview",label:"Overview"},{href:"/experts?view=manage",label:"Manage Expert"}].map(e=>(0,o.jsx)(s(),{href:e.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",children:e.label},e.href))})]})]})]}),r&&n?(0,o.jsxs)("div",{className:"relative",onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:[(0,o.jsxs)("button",{className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200",children:[(0,o.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:(0,o.jsx)(c.A,{className:"w-4 h-4 text-white"})}),(0,o.jsx)("span",{className:"hidden md:block",children:n.name}),(0,o.jsx)(u.A,{className:"w-4 h-4"})]}),v&&(0,o.jsxs)("div",{className:"absolute top-full right-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,o.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,o.jsx)("p",{className:"text-sm font-medium text-gray-900",children:n.name}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:n.email})]}),(0,o.jsxs)(s(),{href:"/dashboard",className:"flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",children:[(0,o.jsx)(c.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Dashboard"})]}),k.map(e=>{let t=e.icon;return(0,o.jsxs)(s(),{href:e.href,className:"flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors",children:[(0,o.jsx)(t,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:e.label})]},e.href)}),(0,o.jsx)("hr",{className:"my-1 border-gray-200"}),(0,o.jsxs)("button",{onClick:w,className:"flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left",children:[(0,o.jsx)(h.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Logout"})]})]})]}):(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsxs)(s(),{href:"/login",className:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors",children:[(0,o.jsx)(x.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Login"})]}),(0,o.jsxs)(s(),{href:"/register",className:"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",children:[(0,o.jsx)(m.A,{className:"w-4 h-4"}),(0,o.jsx)("span",{children:"Sign Up"})]})]})]})})})}},6070:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var o=n(5155),r=n(2922),s=n(6715),l=n(2115);function a(e){let{children:t}=e,[n]=(0,l.useState)(()=>new r.E);return(0,o.jsx)(s.Ht,{client:n,children:t})}},9340:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,9243,23)),Promise.resolve().then(n.t.bind(n,2093,23)),Promise.resolve().then(n.t.bind(n,7735,23)),Promise.resolve().then(n.bind(n,6070)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,5506)),Promise.resolve().then(n.bind(n,283)),Promise.resolve().then(n.bind(n,5332))}},e=>{e.O(0,[360,445,874,640,570,283,441,964,358],()=>e(e.s=9340)),_N_E=e.O()}]);