import { useState, useEffect, useCallback, useRef } from "react";
import { FilterParams, FilteredExpertsResponse } from "@/types/filters";
import { api } from "@/lib/api";

interface UseFilteredExpertsResult {
  data: FilteredExpertsResponse | null;
  loading: boolean;
  error: string | null;
  retryCount: number;
  isRetrying: boolean;
  refetch: () => void;
  retry: () => void;
  clearError: () => void;
}

/**
 * Custom hook for fetching filtered experts
 */
export const useFilteredExperts = (params: FilterParams): UseFilteredExpertsResult => {
  const [data, setData] = useState<FilteredExpertsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  // Use ref to track retry count for auto-retry logic to avoid dependency issues
  const retryCountRef = useRef(0);

  const fetchExperts = useCallback(async (isRetryAttempt = false) => {
    setLoading(true);
    setError(null);

    if (isRetryAttempt) {
      setIsRetrying(true);
      retryCountRef.current += 1;
      setRetryCount(retryCountRef.current);
    }

    try {
      const result = await api.getFilteredExperts({
        filter: params.filter,
        timeline: params.timeline,
        search: params.search,
        page: params.page,
        limit: params.limit
      });

      setData(result.data);
    } catch (err) {
      let errorMessage = 'An unexpected error occurred';

      if (err instanceof Error) {
        errorMessage = err.message;

        // Provide more specific error messages based on error type
        if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {
          errorMessage = 'Network connection error. Please check your internet connection and try again.';
        } else if (err.message.includes('500')) {
          errorMessage = 'Server error. Our team has been notified. Please try again in a few moments.';
        } else if (err.message.includes('404')) {
          errorMessage = 'The requested resource was not found. Please try a different filter.';
        } else if (err.message.includes('403')) {
          errorMessage = 'Access denied. Please log in and try again.';
        }
      }

      setError(errorMessage);
      console.error('Error fetching filtered experts:', err);

      // Auto-retry for network errors (max 3 attempts)
      // Use ref value to avoid dependency issues
      if (retryCountRef.current < 3 && (errorMessage.includes('Network') || errorMessage.includes('Server'))) {
        setTimeout(() => {
          fetchExperts(true);
        }, Math.pow(2, retryCountRef.current) * 1000); // Exponential backoff
      }
    } finally {
      setLoading(false);
      setIsRetrying(false);
    }
  }, [params.filter, params.timeline, params.search, params.page, params.limit]); // Remove retryCount from dependencies

  // Reset retry count when parameters change
  useEffect(() => {
    retryCountRef.current = 0;
    setRetryCount(0);
  }, [params.filter, params.timeline, params.search, params.page, params.limit]);

  // Fetch data when parameters change
  useEffect(() => {
    fetchExperts();
  }, [fetchExperts]);

  const refetch = useCallback(() => {
    retryCountRef.current = 0; // Reset retry count ref
    setRetryCount(0); // Reset retry count state for UI
    fetchExperts();
  }, [fetchExperts]);

  const retry = useCallback(() => {
    fetchExperts(true);
  }, [fetchExperts]);

  const clearError = useCallback(() => {
    setError(null);
    retryCountRef.current = 0; // Reset retry count ref
    setRetryCount(0); // Reset retry count state
  }, []);

  return {
    data,
    loading,
    error,
    retryCount,
    isRetrying,
    refetch,
    retry,
    clearError
  };
};