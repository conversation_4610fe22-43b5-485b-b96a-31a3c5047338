"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283],{283:(e,t,a)=>{a.d(t,{A:()=>c,AuthProvider:()=>n});var s=a(5155),r=a(2115),o=a(5731);let i=(0,r.createContext)(void 0),n=e=>{let{children:t}=e,[a,n]=(0,r.useState)(null),[c,l]=(0,r.useState)(null),[d,p]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("user");if(e&&t)try{l(e),n(JSON.parse(t));let a=await o.R2.getProfile();n(a.user)}catch(e){console.error("Token validation failed:",e),localStorage.removeItem("token"),localStorage.removeItem("user"),l(null),n(null)}p(!1)})()},[]);let u=async(e,t)=>{try{let a=(await o.R2.login({phone:e,password:t})).user,s=a.token,r={user_id:a.user_id,phone:a.phone,name:a.name,email:a.email};n(r),l(s),localStorage.setItem("token",s),localStorage.setItem("user",JSON.stringify(r))}catch(e){throw Error(e.message||"Login failed")}},h=async e=>{try{return await o.R2.register(e)}catch(e){throw Error(e.message||"Registration failed")}},g=async(e,t)=>{try{let a=(await o.R2.verifyOTP({phone:e,code:t})).user,s=a.token,r={user_id:a.user_id,phone:a.phone,name:a.name,email:a.email};n(r),l(s),localStorage.setItem("token",s),localStorage.setItem("user",JSON.stringify(r))}catch(e){throw Error(e.message||"OTP verification failed")}},m=async()=>{try{c&&await o.R2.logout()}catch(e){console.error("Logout API call failed:",e)}finally{n(null),l(null),localStorage.removeItem("token"),localStorage.removeItem("user")}},P=async e=>{try{return await o.R2.resendOTP(e)}catch(e){throw Error(e.message||"Failed to resend OTP")}},v=async e=>{try{return await o.R2.forgotPassword(e)}catch(e){throw Error(e.message||"Failed to request password reset")}},y=async(e,t,a)=>{try{await o.R2.resetPassword(e,t,a)}catch(e){throw Error(e.message||"Password reset failed")}};return(0,s.jsx)(i.Provider,{value:{user:a,token:c,isLoading:d,isAuthenticated:!!a&&!!c,login:u,register:h,verifyOTP:g,logout:m,resendOTP:P,forgotPassword:v,resetPassword:y},children:t})},c=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5731:(e,t,a)=>{a.d(t,{FH:()=>l,H2:()=>c,R2:()=>d,SP:()=>i});var s=a(3464),r=a(9509);let o=r.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",i=s.A.create({baseURL:o,headers:{"Content-Type":"application/json"},timeout:3e4});function n(){return localStorage.getItem("token")}async function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:a="GET",body:s,headers:r={},skipAuth:o=!1}=t,n={method:a.toLowerCase(),url:e,headers:{...r},metadata:{skipAuth:o}};s&&"GET"!==a&&(n.data=s);try{return(await i(n)).data}catch(e){throw e}}i.interceptors.request.use(e=>{var t,a;let s=n();return console.log("\uD83D\uDD0D API Call Debug:",{endpoint:e.url,fullUrl:"".concat(o).concat(e.url),API_URL:o,hasToken:!!s,tokenPreview:s?s.substring(0,3)+"***":"none",method:null==(t=e.method)?void 0:t.toUpperCase(),data:e.data,"process.env.NEXT_PUBLIC_API_URL":r.env.NEXT_PUBLIC_API_URL}),!s||(null==(a=e.metadata)?void 0:a.skipAuth)||(e.headers.Authorization="Bearer ".concat(s)),e},e=>(console.error("\uD83D\uDCA5 Request interceptor error:",e),Promise.reject(e))),i.interceptors.response.use(e=>(console.log("\uD83D\uDCE1 Response status:",e.status,e.statusText),console.log("✅ API Success:",e.data),e),e=>{var t,a,s,r;throw console.error("❌ API Error:",(null==(t=e.response)?void 0:t.data)||e.message),console.error("\uD83D\uDCA5 API call failed:",e),Error((null==(s=e.response)||null==(a=s.data)?void 0:a.message)||e.message||"HTTP error! status: ".concat(null==(r=e.response)?void 0:r.status))});let l={get:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"GET"})},post:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"POST"})},put:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"PUT"})},delete:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return c(e,{...t,method:"DELETE"})},health:()=>c("/health"),chat:(e,t,a,s)=>c("/api/chat",{method:"POST",body:{message:e,threadId:t,expertId:a,expertContext:s}}),getThreadMessages:e=>c("/api/thread/".concat(e,"/messages")),getSessionMessages:(e,t)=>c("/api/chat/sessions/".concat(e,"/messages").concat(t?"?limit=".concat(t):"")),getUserChatSessions:e=>c("/api/chat/sessions".concat(e?"?limit=".concat(e):"")),getUserStats:()=>c("/api/chat/stats"),getActiveSessionForExpert:e=>c("/api/chat/sessions/expert/".concat(e)),updateSessionTitle:(e,t)=>c("/api/chat/sessions/".concat(e,"/title"),{method:"PUT",body:{title:t}}),deleteSession:e=>c("/api/chat/sessions/".concat(e),{method:"DELETE"}),createThread:()=>c("/assistant/thread",{method:"POST"}),sendMessage:(e,t)=>c("/assistant/message",{method:"POST",body:{threadId:e,message:t}}),runAssistant:e=>c("/assistant/run",{method:"POST",body:{threadId:e}}),getMessages:e=>c("/assistant/messages/".concat(e)),createExpert:async e=>{let t=n();try{return(await s.A.post("".concat(o,"/api/experts"),e,{headers:{"Content-Type":"multipart/form-data",...t?{Authorization:"Bearer ".concat(t)}:{}}})).data}catch(e){var a,r,i;throw Error((null==(r=e.response)||null==(a=r.data)?void 0:a.message)||e.message||"HTTP error! status: ".concat(null==(i=e.response)?void 0:i.status))}},listExperts:()=>c("/api/experts"),getPublicExperts:()=>c("/api/experts/public",{skipAuth:!0}),getExpert:e=>c("/api/experts/".concat(e)),updateExpert:async(e,t,a,r)=>{let i=n(),c=new FormData;Object.keys(t).forEach(e=>{void 0!==t[e]&&null!==t[e]&&("labels"===e&&Array.isArray(t[e])?c.append(e,JSON.stringify(t[e])):c.append(e,t[e].toString()))}),a&&c.append("file",a),r&&c.append("image",r);try{return(await s.A.put("".concat(o,"/api/experts/").concat(e),c,{headers:{"Content-Type":"multipart/form-data",...i?{Authorization:"Bearer ".concat(i)}:{}}})).data}catch(e){var l,d,p;throw Error((null==(d=e.response)||null==(l=d.data)?void 0:l.message)||e.message||"HTTP error! status: ".concat(null==(p=e.response)?void 0:p.status))}},getAvailableModels:()=>c("/api/models"),getModelPricing:e=>c("/api/models/".concat(e,"/pricing")),calculateCost:(e,t,a,s)=>c("/api/calculate-cost",{method:"POST",body:{model:e,inputTokens:t,outputTokens:a,pricingPercentage:s}}),getExpertStats:e=>c("/api/experts/".concat(e,"/stats")),createReview:e=>c("/api/reviews",{method:"POST",body:e}),updateReview:(e,t)=>c("/api/reviews/".concat(e),{method:"PUT",body:t}),getExpertReviews:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return c("/api/reviews/expert/".concat(e,"?page=").concat(t,"&limit=").concat(a),{skipAuth:!0})},getUserReviews:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return c("/api/reviews/my?page=".concat(e,"&limit=").concat(t))},getReview:e=>c("/api/reviews/".concat(e),{skipAuth:!0}),canUserReview:e=>c("/api/reviews/expert/".concat(e,"/can-review")),getExpertRatingStats:e=>c("/api/reviews/expert/".concat(e,"/stats"),{skipAuth:!0}),getFilteredExperts:e=>{let t=new URLSearchParams;return e.filter&&t.append("filter",e.filter),e.timeline&&t.append("timeline",e.timeline),e.search&&t.append("search",e.search),e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),c("/api/experts/filtered?".concat(t.toString()),{skipAuth:!0})}},d={register:e=>c("/api/users/register",{method:"POST",body:e,skipAuth:!0}),verifyOTP:e=>c("/api/users/verify-otp",{method:"POST",body:e,skipAuth:!0}),login:e=>c("/api/users/login",{method:"POST",body:e,skipAuth:!0}),getProfile:()=>c("/api/users/profile"),updateProfile:e=>c("/api/users/profile",{method:"PUT",body:e}),changePassword:e=>c("/api/users/change-password",{method:"POST",body:e}),resendOTP:e=>c("/api/users/resend-otp",{method:"POST",body:{phone:e},skipAuth:!0}),forgotPassword:e=>c("/api/users/forgot-password",{method:"POST",body:{phone:e},skipAuth:!0}),resetPassword:(e,t,a)=>c("/api/users/reset-password",{method:"POST",body:{phone:e,code:t,newPassword:a},skipAuth:!0}),logout:()=>c("/api/users/logout",{method:"POST"}),getBalanceSummary:()=>c("/api/balance/summary"),getPointTransactions:e=>c("/api/balance/transactions/points".concat(e?"?limit=".concat(e):"")),getCreditTransactions:e=>c("/api/balance/transactions/credits".concat(e?"?limit=".concat(e):"")),checkAffordability:e=>c("/api/balance/can-afford",{method:"POST",body:{amount:e}}),addPoints:(e,t)=>c("/api/balance/points/add",{method:"POST",body:{amount:e,description:t}}),addCredits:(e,t)=>c("/api/balance/credits/add",{method:"POST",body:{amount:e,description:t}})}}}]);