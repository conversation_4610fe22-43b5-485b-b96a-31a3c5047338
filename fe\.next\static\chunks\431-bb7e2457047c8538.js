"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[431],{1285:(e,t,n)=>{n.d(t,{B:()=>l});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function l(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},3470:(e,t,n)=>{n.d(t,{bm:()=>ta,UC:()=>tn,VY:()=>to,hJ:()=>tt,ZL:()=>te,bL:()=>e9,hE:()=>tr,l9:()=>e4});var r,o,a,i=n(2115),u=n(5185),l=n(6101),c=n(6081),s=n(1285),d=n(5845),f=n(3655),v=n(9033),p=n(5155),m="dismissableLayer.update",h=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:c,onPointerDownOutside:s,onFocusOutside:d,onInteractOutside:g,onDismiss:b,...w}=e,N=i.useContext(h),[C,R]=i.useState(null),S=null!=(r=null==C?void 0:C.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,x]=i.useState({}),O=(0,l.s)(t,e=>R(e)),D=Array.from(N.layers),[T]=[...N.layersWithOutsidePointerEventsDisabled].slice(-1),L=D.indexOf(T),M=C?D.indexOf(C):-1,P=N.layersWithOutsidePointerEventsDisabled.size>0,A=M>=L,k=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),o=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){E("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...N.branches].some(e=>e.contains(t));A&&!n&&(null==s||s(e),null==g||g(e),e.defaultPrevented||null==b||b())},S),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&E("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...N.branches].some(e=>e.contains(t))&&(null==d||d(e),null==g||g(e),e.defaultPrevented||null==b||b())},S);return!function(e,t=globalThis?.document){let n=(0,v.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{M===N.layers.size-1&&(null==c||c(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},S),i.useEffect(()=>{if(C)return a&&(0===N.layersWithOutsidePointerEventsDisabled.size&&(o=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),N.layersWithOutsidePointerEventsDisabled.add(C)),N.layers.add(C),y(),()=>{a&&1===N.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=o)}},[C,S,a,N]),i.useEffect(()=>()=>{C&&(N.layers.delete(C),N.layersWithOutsidePointerEventsDisabled.delete(C),y())},[C,N]),i.useEffect(()=>{let e=()=>x({});return document.addEventListener(m,e),()=>document.removeEventListener(m,e)},[]),(0,p.jsx)(f.sG.div,{...w,ref:O,style:{pointerEvents:P?A?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,u.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,u.m)(e.onPointerDownCapture,k.onPointerDownCapture)})});function y(){let e=new CustomEvent(m);document.dispatchEvent(e)}function E(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,f.hO)(a,i):a.dispatchEvent(i)}g.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(h),r=i.useRef(null),o=(0,l.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(f.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var b="focusScope.autoFocusOnMount",w="focusScope.autoFocusOnUnmount",N={bubbles:!1,cancelable:!0},C=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...u}=e,[c,s]=i.useState(null),d=(0,v.c)(o),m=(0,v.c)(a),h=i.useRef(null),g=(0,l.s)(t,e=>s(e)),y=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(y.paused||!c)return;let t=e.target;c.contains(t)?h.current=t:x(h.current,{select:!0})},t=function(e){if(y.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||x(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&x(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,y.paused]),i.useEffect(()=>{if(c){O.add(y);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(b,N);c.addEventListener(b,d),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(x(r,{select:t}),document.activeElement!==n)return}(R(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&x(c))}return()=>{c.removeEventListener(b,d),setTimeout(()=>{let t=new CustomEvent(w,N);c.addEventListener(w,m),c.dispatchEvent(t),t.defaultPrevented||x(null!=e?e:document.body,{select:!0}),c.removeEventListener(w,m),O.remove(y)},0)}}},[c,d,m,y]);let E=i.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=R(e);return[S(t,e),S(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&x(a,{select:!0})):(e.preventDefault(),n&&x(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,p.jsx)(f.sG.div,{tabIndex:-1,...u,ref:g,onKeyDown:E})});function R(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function S(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function x(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}C.displayName="FocusScope";var O=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=D(e,t)).unshift(t)},remove(t){var n;null==(n=(e=D(e,t))[0])||n.resume()}}}();function D(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var T=n(7650),L=n(2712),M=i.forwardRef((e,t)=>{var n,r;let{container:o,...a}=e,[u,l]=i.useState(!1);(0,L.N)(()=>l(!0),[]);let c=o||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?T.createPortal((0,p.jsx)(f.sG.div,{...a,ref:t}),c):null});M.displayName="Portal";var P=n(8905),A=0;function k(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var I=function(){return(I=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function j(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var F=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),_="width-before-scroll-bar";function W(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var B="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,U=new WeakMap;function $(e){return e}var G=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=$),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return o.options=I({async:!0,ssr:!1},e),o}(),K=function(){},Y=i.forwardRef(function(e,t){var n,r,o,a,u=i.useRef(null),l=i.useState({onScrollCapture:K,onWheelCapture:K,onTouchMoveCapture:K}),c=l[0],s=l[1],d=e.forwardProps,f=e.children,v=e.className,p=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,E=e.noIsolation,b=e.inert,w=e.allowPinchZoom,N=e.as,C=e.gapMode,R=j(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[u,t],r=function(e){return n.forEach(function(t){return W(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,B(function(){var e=U.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||W(e,null)}),r.forEach(function(e){t.has(e)||W(e,o)})}U.set(a,n)},[n]),a),x=I(I({},R),c);return i.createElement(i.Fragment,null,m&&i.createElement(g,{sideCar:G,removeScrollBar:p,shards:h,noRelative:y,noIsolation:E,inert:b,setCallbacks:s,allowPinchZoom:!!w,lockRef:u,gapMode:C}),d?i.cloneElement(i.Children.only(f),I(I({},x),{ref:S})):i.createElement(void 0===N?"div":N,I({},x,{className:v,ref:S}),f))});Y.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Y.classNames={fullWidth:_,zeroRight:F};var X=function(e){var t=e.sideCar,n=j(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,I({},n))};X.isSideCarExport=!0;var q=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},z=function(){var e=q();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Z=function(){var e=z();return function(t){return e(t.styles,t.dynamic),null}},H={left:0,top:0,right:0,gap:0},V=function(e){return parseInt(e||"",10)||0},J=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[V(n),V(r),V(o)]},Q=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return H;var t=J(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},ee=Z(),et="data-scroll-locked",en=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(et,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(F," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(_," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(F," .").concat(F," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(_," .").concat(_," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(et,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},er=function(){var e=parseInt(document.body.getAttribute(et)||"0",10);return isFinite(e)?e:0},eo=function(){i.useEffect(function(){return document.body.setAttribute(et,(er()+1).toString()),function(){var e=er()-1;e<=0?document.body.removeAttribute(et):document.body.setAttribute(et,e.toString())}},[])},ea=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;eo();var a=i.useMemo(function(){return Q(o)},[o]);return i.createElement(ee,{styles:en(a,!t,o,n?"":"!important")})},ei=!1;if("undefined"!=typeof window)try{var eu=Object.defineProperty({},"passive",{get:function(){return ei=!0,!0}});window.addEventListener("test",eu,eu),window.removeEventListener("test",eu,eu)}catch(e){ei=!1}var el=!!ei&&{passive:!1},ec=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},es=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ed(e,r)){var o=ef(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ed=function(e,t){return"v"===e?ec(t,"overflowY"):ec(t,"overflowX")},ef=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ev=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,v=0;do{if(!l)break;var p=ef(e,l),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&ed(e,l)&&(f+=h,v+=m);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},ep=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},em=function(e){return[e.deltaX,e.deltaY]},eh=function(e){return e&&"current"in e?e.current:e},eg=0,ey=[];let eE=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(eg++)[0],a=i.useState(Z)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eh),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=ep(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=es(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=es(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var v=r.current||o;return ev(v,t,e,"h"===v?l:c,!0)},[]),c=i.useCallback(function(e){if(ey.length&&ey[ey.length-1]===a){var n="deltaY"in e?em(e):ep(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(eh).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=ep(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,em(t),t.target,l(t,e.lockRef.current))},[]),v=i.useCallback(function(t){s(t.type,ep(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return ey.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",c,el),document.addEventListener("touchmove",c,el),document.addEventListener("touchstart",d,el),function(){ey=ey.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,el),document.removeEventListener("touchmove",c,el),document.removeEventListener("touchstart",d,el)}},[]);var p=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?i.createElement(ea,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},G.useMedium(r),X);var eb=i.forwardRef(function(e,t){return i.createElement(Y,I({},e,{ref:t,sideCar:eE}))});eb.classNames=Y.classNames;var ew=new WeakMap,eN=new WeakMap,eC={},eR=0,eS=function(e){return e&&(e.host||eS(e.parentNode))},ex=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eS(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eC[n]||(eC[n]=new WeakMap);var a=eC[n],i=[],u=new Set,l=new Set(o),c=function(e){!e||u.has(e)||(u.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(ew.get(e)||0)+1,c=(a.get(e)||0)+1;ew.set(e,l),a.set(e,c),i.push(e),1===l&&o&&eN.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),u.clear(),eR++,function(){i.forEach(function(e){var t=ew.get(e)-1,o=a.get(e)-1;ew.set(e,t),a.set(e,o),t||(eN.has(e)||e.removeAttribute(r),eN.delete(e)),o||e.removeAttribute(n)}),--eR||(ew=new WeakMap,ew=new WeakMap,eN=new WeakMap,eC={})}},eO=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),ex(r,o,n,"aria-hidden")):function(){return null}},eD=n(9708),eT="Dialog",[eL,eM]=(0,c.A)(eT),[eP,eA]=eL(eT),ek=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,l=i.useRef(null),c=i.useRef(null),[f,v]=(0,d.i)({prop:r,defaultProp:null!=o&&o,onChange:a,caller:eT});return(0,p.jsx)(eP,{scope:t,triggerRef:l,contentRef:c,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:v,onOpenToggle:i.useCallback(()=>v(e=>!e),[v]),modal:u,children:n})};ek.displayName=eT;var eI="DialogTrigger",ej=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eA(eI,n),a=(0,l.s)(t,o.triggerRef);return(0,p.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e5(o.open),...r,ref:a,onClick:(0,u.m)(e.onClick,o.onOpenToggle)})});ej.displayName=eI;var eF="DialogPortal",[e_,eW]=eL(eF,{forceMount:void 0}),eB=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=eA(eF,t);return(0,p.jsx)(e_,{scope:t,forceMount:n,children:i.Children.map(r,e=>(0,p.jsx)(P.C,{present:n||a.open,children:(0,p.jsx)(M,{asChild:!0,container:o,children:e})}))})};eB.displayName=eF;var eU="DialogOverlay",e$=i.forwardRef((e,t)=>{let n=eW(eU,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eA(eU,e.__scopeDialog);return a.modal?(0,p.jsx)(P.C,{present:r||a.open,children:(0,p.jsx)(eK,{...o,ref:t})}):null});e$.displayName=eU;var eG=(0,eD.TL)("DialogOverlay.RemoveScroll"),eK=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eA(eU,n);return(0,p.jsx)(eb,{as:eG,allowPinchZoom:!0,shards:[o.contentRef],children:(0,p.jsx)(f.sG.div,{"data-state":e5(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eY="DialogContent",eX=i.forwardRef((e,t)=>{let n=eW(eY,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eA(eY,e.__scopeDialog);return(0,p.jsx)(P.C,{present:r||a.open,children:a.modal?(0,p.jsx)(eq,{...o,ref:t}):(0,p.jsx)(ez,{...o,ref:t})})});eX.displayName=eY;var eq=i.forwardRef((e,t)=>{let n=eA(eY,e.__scopeDialog),r=i.useRef(null),o=(0,l.s)(t,n.contentRef,r);return i.useEffect(()=>{let e=r.current;if(e)return eO(e)},[]),(0,p.jsx)(eZ,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,u.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,u.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,u.m)(e.onFocusOutside,e=>e.preventDefault())})}),ez=i.forwardRef((e,t)=>{let n=eA(eY,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return(0,p.jsx)(eZ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(r.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let u=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eZ=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,c=eA(eY,n),s=i.useRef(null),d=(0,l.s)(t,s);return i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:k()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:k()),A++,()=>{1===A&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),A--}},[]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(C,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,p.jsx)(g,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":e5(c.open),...u,ref:d,onDismiss:()=>c.onOpenChange(!1)})}),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(e7,{titleId:c.titleId}),(0,p.jsx)(e3,{contentRef:s,descriptionId:c.descriptionId})]})]})}),eH="DialogTitle",eV=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eA(eH,n);return(0,p.jsx)(f.sG.h2,{id:o.titleId,...r,ref:t})});eV.displayName=eH;var eJ="DialogDescription",eQ=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eA(eJ,n);return(0,p.jsx)(f.sG.p,{id:o.descriptionId,...r,ref:t})});eQ.displayName=eJ;var e0="DialogClose",e1=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eA(e0,n);return(0,p.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,u.m)(e.onClick,()=>o.onOpenChange(!1))})});function e5(e){return e?"open":"closed"}e1.displayName=e0;var e2="DialogTitleWarning",[e8,e6]=(0,c.q)(e2,{contentName:eY,titleName:eH,docsSlug:"dialog"}),e7=e=>{let{titleId:t}=e,n=e6(e2),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},e3=e=>{let{contentRef:t,descriptionId:n}=e,r=e6("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return i.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},e9=ek,e4=ej,te=eB,tt=e$,tn=eX,tr=eV,to=eQ,ta=e1},3655:(e,t,n)=>{n.d(t,{hO:()=>l,sG:()=>u});var r=n(2115),o=n(7650),a=n(9708),i=n(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},5845:(e,t,n)=>{n.d(t,{i:()=>u});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[a,u,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),a=o.useRef(n),u=o.useRef(t);return i(()=>{u.current=t},[t]),o.useEffect(()=>{a.current!==n&&(u.current?.(n),a.current=n)},[n,a]),[n,r,u]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else u(t)},[c,e,u,l])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>a});var r=n(2115),o=n(5155);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,i=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:i,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let i=r.createContext(a),u=n.length;n=[...n,a];let l=t=>{let{scope:n,children:a,...l}=t,c=n?.[e]?.[u]||i,s=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(c.Provider,{value:s,children:a})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[u]||i,c=r.useContext(l);if(c)return c;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},8905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(2115),o=n(6101),a=n(2712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(l.current);s.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=l.current,n=c.current;if(n!==e){let r=s.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=u(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(s.current=u(l.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),l="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),c=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||i.isPresent?r.cloneElement(l,{ref:c}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}}}]);