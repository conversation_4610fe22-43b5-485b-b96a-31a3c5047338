{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/app/client-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { QueryClient, QueryClientProvider } from \"@tanstack/react-query\";\r\nimport { useState } from \"react\";\r\n\r\nexport default function ClientProvider({ children }: { children: React.ReactNode }) {\r\n  const [queryClient] = useState(() => new QueryClient());\r\n  \r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAKe,SAAS,eAAe,EAAE,QAAQ,EAAiC;IAChF,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,IAAI,6KAAA,CAAA,cAAW;IAEpD,qBACE,8OAAC,sLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosRequestConfig, AxiosResponse } from \"axios\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n\r\ninterface ApiOptions {\r\n  method?: \"GET\" | \"POST\" | \"PUT\" | \"DELETE\";\r\n  body?: any;\r\n  headers?: Record<string, string>;\r\n  skipAuth?: boolean; // Skip authentication for public endpoints\r\n}\r\n\r\n// User registration/authentication types\r\nexport interface RegisterData {\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  password: string;\r\n  referralCode?: string;\r\n}\r\n\r\nexport interface LoginData {\r\n  phone: string;\r\n  password: string;\r\n}\r\n\r\nexport interface VerifyOTPData {\r\n  phone: string;\r\n  code: string;\r\n}\r\n\r\nexport interface UpdateProfileData {\r\n  name: string;\r\n  email: string;\r\n  bank_name?: string;\r\n  account_holder_name?: string;\r\n  account_number?: string;\r\n}\r\n\r\nexport interface ChangePasswordData {\r\n  currentPassword: string;\r\n  newPassword: string;\r\n}\r\n\r\nexport interface User {\r\n  user_id: number;\r\n  phone: string;\r\n  name: string;\r\n  email: string;\r\n  bank_name?: string;\r\n  account_holder_name?: string;\r\n  account_number?: string;\r\n  token?: string;\r\n}\r\n\r\n// Create axios instance with default configuration\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n  },\r\n  timeout: 30000, // 30 seconds timeout\r\n});\r\n\r\n// Helper function to get authentication token\r\nfunction getAuthToken(): string | null {\r\n  if (typeof window !== \"undefined\") {\r\n    return localStorage.getItem(\"token\");\r\n  }\r\n  return null;\r\n}\r\n\r\n// Request interceptor to add auth token\r\naxiosInstance.interceptors.request.use(\r\n  (config: any) => {\r\n    const authToken = getAuthToken();\r\n\r\n    // Debug logging\r\n    console.log(\"🔍 API Call Debug:\", {\r\n      endpoint: config.url,\r\n      fullUrl: `${API_URL}${config.url}`,\r\n      API_URL,\r\n      hasToken: !!authToken,\r\n      tokenPreview: authToken ? authToken.substring(0, 3) + \"***\" : \"none\",\r\n      method: config.method?.toUpperCase(),\r\n      data: config.data,\r\n      \"process.env.NEXT_PUBLIC_API_URL\": process.env.NEXT_PUBLIC_API_URL,\r\n    });\r\n\r\n    // Add auth token if available and not skipped\r\n    if (authToken && !config.metadata?.skipAuth) {\r\n      config.headers.Authorization = `Bearer ${authToken}`;\r\n    }\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error(\"💥 Request interceptor error:\", error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for logging and error handling\r\naxiosInstance.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    console.log(\"📡 Response status:\", response.status, response.statusText);\r\n    console.log(\"✅ API Success:\", response.data);\r\n    return response;\r\n  },\r\n  (error) => {\r\n    console.error(\"❌ API Error:\", error.response?.data || error.message);\r\n    console.error(\"💥 API call failed:\", error);\r\n\r\n    // Extract error message\r\n    const errorMessage =\r\n      error.response?.data?.message ||\r\n      error.message ||\r\n      `HTTP error! status: ${error.response?.status}`;\r\n\r\n    throw new Error(errorMessage);\r\n  }\r\n);\r\n\r\ninterface ApiCallConfig extends AxiosRequestConfig {\r\n  metadata: any;\r\n}\r\n\r\nexport async function apiCall(endpoint: string, options: ApiOptions = {}) {\r\n  const { method = \"GET\", body, headers = {}, skipAuth = false } = options;\r\n\r\n  const config: ApiCallConfig = {\r\n    method: method.toLowerCase() as any,\r\n    url: endpoint,\r\n    headers: {\r\n      ...headers,\r\n    },\r\n    metadata: { skipAuth } as any, // Custom metadata for interceptor\r\n  };\r\n\r\n  // Add data for non-GET requests\r\n  if (body && method !== \"GET\") {\r\n    config.data = body;\r\n  }\r\n\r\n  try {\r\n    const response = await axiosInstance(config);\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Specific API functions\r\nexport const api = {\r\n  // Generic HTTP methods\r\n  get: (endpoint: string, options: ApiOptions = {}) => \r\n    apiCall(endpoint, { ...options, method: \"GET\" }),\r\n  \r\n  post: (endpoint: string, options: ApiOptions = {}) => \r\n    apiCall(endpoint, { ...options, method: \"POST\" }),\r\n  \r\n  put: (endpoint: string, options: ApiOptions = {}) => \r\n    apiCall(endpoint, { ...options, method: \"PUT\" }),\r\n  \r\n  delete: (endpoint: string, options: ApiOptions = {}) => \r\n    apiCall(endpoint, { ...options, method: \"DELETE\" }),\r\n\r\n  // Health check (no token required)\r\n  health: () => apiCall(\"/health\"),\r\n\r\n  // Chat endpoints (token required)\r\n  chat: (\r\n    message: string,\r\n    threadId?: string,\r\n    expertId?: string,\r\n    expertContext?: any\r\n  ) =>\r\n    apiCall(\"/api/chat\", {\r\n      method: \"POST\",\r\n      body: { message, threadId, expertId, expertContext },\r\n    }),\r\n\r\n  getThreadMessages: (threadId: string) =>\r\n    apiCall(`/api/thread/${threadId}/messages`),\r\n\r\n  getSessionMessages: (sessionId: number, limit?: number) =>\r\n    apiCall(\r\n      `/api/chat/sessions/${sessionId}/messages${limit ? `?limit=${limit}` : \"\"\r\n      }`\r\n    ),\r\n\r\n  // Chat session endpoints\r\n  getUserChatSessions: (limit?: number) =>\r\n    apiCall(`/api/chat/sessions${limit ? `?limit=${limit}` : \"\"}`),\r\n\r\n  getUserStats: () => apiCall(\"/api/chat/stats\"),\r\n\r\n  getActiveSessionForExpert: (expertId: string) =>\r\n    apiCall(`/api/chat/sessions/expert/${expertId}`),\r\n\r\n  updateSessionTitle: (sessionId: string, title: string) =>\r\n    apiCall(`/api/chat/sessions/${sessionId}/title`, {\r\n      method: \"PUT\",\r\n      body: { title },\r\n    }),\r\n\r\n  deleteSession: (sessionId: string) =>\r\n    apiCall(`/api/chat/sessions/${sessionId}`, {\r\n      method: \"DELETE\",\r\n    }),\r\n\r\n\r\n\r\n  // Assistant endpoints (token required)\r\n  createThread: () => apiCall(\"/assistant/thread\", { method: \"POST\" }),\r\n\r\n  sendMessage: (threadId: string, message: string) =>\r\n    apiCall(\"/assistant/message\", {\r\n      method: \"POST\",\r\n      body: { threadId, message },\r\n    }),\r\n\r\n  runAssistant: (threadId: string) =>\r\n    apiCall(\"/assistant/run\", {\r\n      method: \"POST\",\r\n      body: { threadId },\r\n    }),\r\n\r\n  getMessages: (threadId: string) => apiCall(`/assistant/messages/${threadId}`),\r\n\r\n  // Expert endpoints (token required)\r\n  createExpert: async (expertData: FormData) => {\r\n    const authToken = getAuthToken();\r\n\r\n    try {\r\n      const response = await axios.post(`${API_URL}/api/experts`, expertData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n          ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),\r\n        },\r\n      });\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.message ||\r\n        error.message ||\r\n        `HTTP error! status: ${error.response?.status}`;\r\n      throw new Error(errorMessage);\r\n    }\r\n  },\r\n\r\n  listExperts: () => apiCall(\"/api/experts\"),\r\n\r\n  // Get public experts (no authentication required)\r\n  getPublicExperts: () => apiCall(\"/api/experts/public\", { skipAuth: true }),\r\n\r\n  getExpert: (expertId: string) => apiCall(`/api/experts/${expertId}`),\r\n\r\n  updateExpert: async (\r\n    expertId: number,\r\n    expertData: any,\r\n    knowledgeBaseFile?: File | null,\r\n    imageFile?: File | null\r\n  ) => {\r\n    const authToken = getAuthToken();\r\n    const formData = new FormData();\r\n\r\n    // Add text fields\r\n    Object.keys(expertData).forEach((key) => {\r\n      if (expertData[key] !== undefined && expertData[key] !== null) {\r\n        if (key === \"labels\" && Array.isArray(expertData[key])) {\r\n          formData.append(key, JSON.stringify(expertData[key]));\r\n        } else {\r\n          formData.append(key, expertData[key].toString());\r\n        }\r\n      }\r\n    });\r\n\r\n    // Add files\r\n    if (knowledgeBaseFile) {\r\n      formData.append(\"file\", knowledgeBaseFile);\r\n    }\r\n    if (imageFile) {\r\n      formData.append(\"image\", imageFile);\r\n    }\r\n\r\n    try {\r\n      const response = await axios.put(\r\n        `${API_URL}/api/experts/${expertId}`,\r\n        formData,\r\n        {\r\n          headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n            ...(authToken ? { Authorization: `Bearer ${authToken}` } : {}),\r\n          },\r\n        }\r\n      );\r\n      return response.data;\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.message ||\r\n        error.message ||\r\n        `HTTP error! status: ${error.response?.status}`;\r\n      throw new Error(errorMessage);\r\n    }\r\n  },\r\n\r\n  // Model endpoints\r\n  getAvailableModels: () => apiCall(\"/api/models\"),\r\n\r\n  getModelPricing: (model: string) => apiCall(`/api/models/${model}/pricing`),\r\n\r\n  calculateCost: (\r\n    model: string,\r\n    inputTokens: number,\r\n    outputTokens: number,\r\n    pricingPercentage: number\r\n  ) =>\r\n    apiCall(\"/api/calculate-cost\", {\r\n      method: \"POST\",\r\n      body: { model, inputTokens, outputTokens, pricingPercentage },\r\n    }),\r\n\r\n  // Expert statistics endpoints\r\n  getExpertStats: (expertId: string) =>\r\n    apiCall(`/api/experts/${expertId}/stats`),\r\n\r\n  // Review endpoints (added directly to avoid type issues)\r\n  createReview: (reviewData: {\r\n    expertId: number;\r\n    rating: number;\r\n    reviewText?: string;\r\n  }) =>\r\n    apiCall(\"/api/reviews\", {\r\n      method: \"POST\",\r\n      body: reviewData,\r\n    }),\r\n\r\n  updateReview: (reviewId: number, reviewData: {\r\n    rating: number;\r\n    reviewText?: string;\r\n  }) =>\r\n    apiCall(`/api/reviews/${reviewId}`, {\r\n      method: \"PUT\",\r\n      body: reviewData,\r\n    }),\r\n\r\n  getExpertReviews: (expertId: number, page: number = 1, limit: number = 10) =>\r\n    apiCall(`/api/reviews/expert/${expertId}?page=${page}&limit=${limit}`, {\r\n      skipAuth: true,\r\n    }),\r\n\r\n  getUserReviews: (page: number = 1, limit: number = 10) =>\r\n    apiCall(`/api/reviews/my?page=${page}&limit=${limit}`),\r\n\r\n  getReview: (reviewId: number) =>\r\n    apiCall(`/api/reviews/${reviewId}`, {\r\n      skipAuth: true,\r\n    }),\r\n\r\n  canUserReview: (expertId: number) =>\r\n    apiCall(`/api/reviews/expert/${expertId}/can-review`),\r\n\r\n  getExpertRatingStats: (expertId: number) =>\r\n    apiCall(`/api/reviews/expert/${expertId}/stats`, {\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Expert filtering functions\r\n  getFilteredExperts: (params: {\r\n    filter?: string;\r\n    timeline?: string;\r\n    search?: string;\r\n    page?: number;\r\n    limit?: number;\r\n  }) => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (params.filter) queryParams.append('filter', params.filter);\r\n    if (params.timeline) queryParams.append('timeline', params.timeline);\r\n    if (params.search) queryParams.append('search', params.search);\r\n    if (params.page) queryParams.append('page', params.page.toString());\r\n    if (params.limit) queryParams.append('limit', params.limit.toString());\r\n\r\n    return apiCall(`/api/experts/filtered?${queryParams.toString()}`, { skipAuth: true });\r\n  },\r\n};\r\n\r\n// Authentication API\r\nexport const authAPI = {\r\n  // Register new user\r\n  register: (userData: RegisterData) =>\r\n    apiCall(\"/api/users/register\", {\r\n      method: \"POST\",\r\n      body: userData,\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Verify OTP\r\n  verifyOTP: (data: VerifyOTPData) =>\r\n    apiCall(\"/api/users/verify-otp\", {\r\n      method: \"POST\",\r\n      body: data,\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Login user\r\n  login: (credentials: LoginData) =>\r\n    apiCall(\"/api/users/login\", {\r\n      method: \"POST\",\r\n      body: credentials,\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Get current user profile\r\n  getProfile: () => apiCall(\"/api/users/profile\"),\r\n\r\n  // Update user profile\r\n  updateProfile: (profileData: UpdateProfileData) =>\r\n    apiCall(\"/api/users/profile\", {\r\n      method: \"PUT\",\r\n      body: profileData,\r\n    }),\r\n\r\n  // Change password\r\n  changePassword: (passwordData: ChangePasswordData) =>\r\n    apiCall(\"/api/users/change-password\", {\r\n      method: \"POST\",\r\n      body: passwordData,\r\n    }),\r\n\r\n  // Resend OTP\r\n  resendOTP: (phone: string) =>\r\n    apiCall(\"/api/users/resend-otp\", {\r\n      method: \"POST\",\r\n      body: { phone },\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Forgot password\r\n  forgotPassword: (phone: string) =>\r\n    apiCall(\"/api/users/forgot-password\", {\r\n      method: \"POST\",\r\n      body: { phone },\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Reset password\r\n  resetPassword: (phone: string, code: string, newPassword: string) =>\r\n    apiCall(\"/api/users/reset-password\", {\r\n      method: \"POST\",\r\n      body: { phone, code, newPassword },\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Logout\r\n  logout: () =>\r\n    apiCall(\"/api/users/logout\", {\r\n      method: \"POST\",\r\n    }),\r\n\r\n  // Balance endpoints\r\n  getBalanceSummary: () => apiCall(\"/api/balance/summary\"),\r\n\r\n  getPointTransactions: (limit?: number) =>\r\n    apiCall(\r\n      `/api/balance/transactions/points${limit ? `?limit=${limit}` : \"\"}`\r\n    ),\r\n\r\n  getCreditTransactions: (limit?: number) =>\r\n    apiCall(\r\n      `/api/balance/transactions/credits${limit ? `?limit=${limit}` : \"\"}`\r\n    ),\r\n\r\n  checkAffordability: (amount: number) =>\r\n    apiCall(\"/api/balance/can-afford\", {\r\n      method: \"POST\",\r\n      body: { amount },\r\n    }),\r\n\r\n  addPoints: (amount: number, description: string) =>\r\n    apiCall(\"/api/balance/points/add\", {\r\n      method: \"POST\",\r\n      body: { amount, description },\r\n    }),\r\n\r\n  addCredits: (amount: number, description: string) =>\r\n    apiCall(\"/api/balance/credits/add\", {\r\n      method: \"POST\",\r\n      body: { amount, description },\r\n    }),\r\n};\r\n\r\n// Review API functions\r\nexport const reviewAPI = {\r\n  // Create a new review\r\n  createReview: (reviewData: {\r\n    expertId: number;\r\n    rating: number;\r\n    reviewText?: string;\r\n  }) =>\r\n    apiCall(\"/api/reviews\", {\r\n      method: \"POST\",\r\n      body: reviewData,\r\n    }),\r\n\r\n  // Update an existing review\r\n  updateReview: (reviewId: number, reviewData: {\r\n    rating: number;\r\n    reviewText?: string;\r\n  }) =>\r\n    apiCall(`/api/reviews/${reviewId}`, {\r\n      method: \"PUT\",\r\n      body: reviewData,\r\n    }),\r\n\r\n  // Get reviews for an expert\r\n  getExpertReviews: (expertId: number, page: number = 1, limit: number = 10) =>\r\n    apiCall(`/api/reviews/expert/${expertId}?page=${page}&limit=${limit}`, {\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Get current user's reviews\r\n  getUserReviews: (page: number = 1, limit: number = 10) =>\r\n    apiCall(`/api/reviews/my?page=${page}&limit=${limit}`),\r\n\r\n  // Get review by ID\r\n  getReview: (reviewId: number) =>\r\n    apiCall(`/api/reviews/${reviewId}`, {\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Check if user can review an expert\r\n  canUserReview: (expertId: number) =>\r\n    apiCall(`/api/reviews/expert/${expertId}/can-review`),\r\n\r\n  // Get expert rating statistics\r\n  getExpertRatingStats: (expertId: number) =>\r\n    apiCall(`/api/reviews/expert/${expertId}/stats`, {\r\n      skipAuth: true,\r\n    }),\r\n\r\n  // Admin functions\r\n  hideReview: (reviewId: number) =>\r\n    apiCall(`/api/reviews/admin/${reviewId}/hide`, {\r\n      method: \"PUT\",\r\n    }),\r\n\r\n  showReview: (reviewId: number) =>\r\n    apiCall(`/api/reviews/admin/${reviewId}/show`, {\r\n      method: \"PUT\",\r\n    }),\r\n\r\n  deleteReview: (reviewId: number) =>\r\n    apiCall(`/api/reviews/admin/${reviewId}`, {\r\n      method: \"DELETE\",\r\n    }),\r\n\r\n  getPendingReviews: (page: number = 1, limit: number = 20) =>\r\n    apiCall(`/api/reviews/admin/pending?page=${page}&limit=${limit}`),\r\n\r\n  getFilterAnalytics: (days: number = 30) =>\r\n    apiCall(`/api/experts/filter-analytics?days=${days}`),\r\n};\r\n\r\n// Review functions are now directly included in the api object above\r\n\r\nexport { axiosInstance };\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAoDnD,mDAAmD;AACnD,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACjC,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;IACA,SAAS;AACX;AAEA,8CAA8C;AAC9C,SAAS;IACP;;IAGA,OAAO;AACT;AAEA,wCAAwC;AACxC,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,MAAM,YAAY;IAElB,gBAAgB;IAChB,QAAQ,GAAG,CAAC,sBAAsB;QAChC,UAAU,OAAO,GAAG;QACpB,SAAS,GAAG,UAAU,OAAO,GAAG,EAAE;QAClC;QACA,UAAU,CAAC,CAAC;QACZ,cAAc,YAAY,UAAU,SAAS,CAAC,GAAG,KAAK,QAAQ;QAC9D,QAAQ,OAAO,MAAM,EAAE;QACvB,MAAM,OAAO,IAAI;QACjB,mCAAmC,QAAQ,GAAG,CAAC,mBAAmB;IACpE;IAEA,8CAA8C;IAC9C,IAAI,aAAa,CAAC,OAAO,QAAQ,EAAE,UAAU;QAC3C,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,WAAW;IACtD;IAEA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,iCAAiC;IAC/C,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sDAAsD;AACtD,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC;IACC,QAAQ,GAAG,CAAC,uBAAuB,SAAS,MAAM,EAAE,SAAS,UAAU;IACvE,QAAQ,GAAG,CAAC,kBAAkB,SAAS,IAAI;IAC3C,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,gBAAgB,MAAM,QAAQ,EAAE,QAAQ,MAAM,OAAO;IACnE,QAAQ,KAAK,CAAC,uBAAuB;IAErC,wBAAwB;IACxB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb,CAAC,oBAAoB,EAAE,MAAM,QAAQ,EAAE,QAAQ;IAEjD,MAAM,IAAI,MAAM;AAClB;AAOK,eAAe,QAAQ,QAAgB,EAAE,UAAsB,CAAC,CAAC;IACtE,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,WAAW,KAAK,EAAE,GAAG;IAEjE,MAAM,SAAwB;QAC5B,QAAQ,OAAO,WAAW;QAC1B,KAAK;QACL,SAAS;YACP,GAAG,OAAO;QACZ;QACA,UAAU;YAAE;QAAS;IACvB;IAEA,gCAAgC;IAChC,IAAI,QAAQ,WAAW,OAAO;QAC5B,OAAO,IAAI,GAAG;IAChB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,cAAc;QACrC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAGO,MAAM,MAAM;IACjB,uBAAuB;IACvB,KAAK,CAAC,UAAkB,UAAsB,CAAC,CAAC,GAC9C,QAAQ,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAEhD,MAAM,CAAC,UAAkB,UAAsB,CAAC,CAAC,GAC/C,QAAQ,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAO;IAEjD,KAAK,CAAC,UAAkB,UAAsB,CAAC,CAAC,GAC9C,QAAQ,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAM;IAEhD,QAAQ,CAAC,UAAkB,UAAsB,CAAC,CAAC,GACjD,QAAQ,UAAU;YAAE,GAAG,OAAO;YAAE,QAAQ;QAAS;IAEnD,mCAAmC;IACnC,QAAQ,IAAM,QAAQ;IAEtB,kCAAkC;IAClC,MAAM,CACJ,SACA,UACA,UACA,gBAEA,QAAQ,aAAa;YACnB,QAAQ;YACR,MAAM;gBAAE;gBAAS;gBAAU;gBAAU;YAAc;QACrD;IAEF,mBAAmB,CAAC,WAClB,QAAQ,CAAC,YAAY,EAAE,SAAS,SAAS,CAAC;IAE5C,oBAAoB,CAAC,WAAmB,QACtC,QACE,CAAC,mBAAmB,EAAE,UAAU,SAAS,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IACrE;IAGN,yBAAyB;IACzB,qBAAqB,CAAC,QACpB,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAE/D,cAAc,IAAM,QAAQ;IAE5B,2BAA2B,CAAC,WAC1B,QAAQ,CAAC,0BAA0B,EAAE,UAAU;IAEjD,oBAAoB,CAAC,WAAmB,QACtC,QAAQ,CAAC,mBAAmB,EAAE,UAAU,MAAM,CAAC,EAAE;YAC/C,QAAQ;YACR,MAAM;gBAAE;YAAM;QAChB;IAEF,eAAe,CAAC,YACd,QAAQ,CAAC,mBAAmB,EAAE,WAAW,EAAE;YACzC,QAAQ;QACV;IAIF,uCAAuC;IACvC,cAAc,IAAM,QAAQ,qBAAqB;YAAE,QAAQ;QAAO;IAElE,aAAa,CAAC,UAAkB,UAC9B,QAAQ,sBAAsB;YAC5B,QAAQ;YACR,MAAM;gBAAE;gBAAU;YAAQ;QAC5B;IAEF,cAAc,CAAC,WACb,QAAQ,kBAAkB;YACxB,QAAQ;YACR,MAAM;gBAAE;YAAS;QACnB;IAEF,aAAa,CAAC,WAAqB,QAAQ,CAAC,oBAAoB,EAAE,UAAU;IAE5E,oCAAoC;IACpC,cAAc,OAAO;QACnB,MAAM,YAAY;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,YAAY,CAAC,EAAE,YAAY;gBACtE,SAAS;oBACP,gBAAgB;oBAChB,GAAI,YAAY;wBAAE,eAAe,CAAC,OAAO,EAAE,WAAW;oBAAC,IAAI,CAAC,CAAC;gBAC/D;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb,CAAC,oBAAoB,EAAE,MAAM,QAAQ,EAAE,QAAQ;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,IAAM,QAAQ;IAE3B,kDAAkD;IAClD,kBAAkB,IAAM,QAAQ,uBAAuB;YAAE,UAAU;QAAK;IAExE,WAAW,CAAC,WAAqB,QAAQ,CAAC,aAAa,EAAE,UAAU;IAEnE,cAAc,OACZ,UACA,YACA,mBACA;QAEA,MAAM,YAAY;QAClB,MAAM,WAAW,IAAI;QAErB,kBAAkB;QAClB,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAC;YAC/B,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,UAAU,CAAC,IAAI,KAAK,MAAM;gBAC7D,IAAI,QAAQ,YAAY,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI,GAAG;oBACtD,SAAS,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,UAAU,CAAC,IAAI;gBACrD,OAAO;oBACL,SAAS,MAAM,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC/C;YACF;QACF;QAEA,YAAY;QACZ,IAAI,mBAAmB;YACrB,SAAS,MAAM,CAAC,QAAQ;QAC1B;QACA,IAAI,WAAW;YACb,SAAS,MAAM,CAAC,SAAS;QAC3B;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,GAAG,QAAQ,aAAa,EAAE,UAAU,EACpC,UACA;gBACE,SAAS;oBACP,gBAAgB;oBAChB,GAAI,YAAY;wBAAE,eAAe,CAAC,OAAO,EAAE,WAAW;oBAAC,IAAI,CAAC,CAAC;gBAC/D;YACF;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,WACtB,MAAM,OAAO,IACb,CAAC,oBAAoB,EAAE,MAAM,QAAQ,EAAE,QAAQ;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,kBAAkB;IAClB,oBAAoB,IAAM,QAAQ;IAElC,iBAAiB,CAAC,QAAkB,QAAQ,CAAC,YAAY,EAAE,MAAM,QAAQ,CAAC;IAE1E,eAAe,CACb,OACA,aACA,cACA,oBAEA,QAAQ,uBAAuB;YAC7B,QAAQ;YACR,MAAM;gBAAE;gBAAO;gBAAa;gBAAc;YAAkB;QAC9D;IAEF,8BAA8B;IAC9B,gBAAgB,CAAC,WACf,QAAQ,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC;IAE1C,yDAAyD;IACzD,cAAc,CAAC,aAKb,QAAQ,gBAAgB;YACtB,QAAQ;YACR,MAAM;QACR;IAEF,cAAc,CAAC,UAAkB,aAI/B,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE;YAClC,QAAQ;YACR,MAAM;QACR;IAEF,kBAAkB,CAAC,UAAkB,OAAe,CAAC,EAAE,QAAgB,EAAE,GACvE,QAAQ,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;YACrE,UAAU;QACZ;IAEF,gBAAgB,CAAC,OAAe,CAAC,EAAE,QAAgB,EAAE,GACnD,QAAQ,CAAC,qBAAqB,EAAE,KAAK,OAAO,EAAE,OAAO;IAEvD,WAAW,CAAC,WACV,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE;YAClC,UAAU;QACZ;IAEF,eAAe,CAAC,WACd,QAAQ,CAAC,oBAAoB,EAAE,SAAS,WAAW,CAAC;IAEtD,sBAAsB,CAAC,WACrB,QAAQ,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,EAAE;YAC/C,UAAU;QACZ;IAEF,6BAA6B;IAC7B,oBAAoB,CAAC;QAOnB,MAAM,cAAc,IAAI;QAExB,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,QAAQ,EAAE,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;QACnE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAChE,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAEnE,OAAO,QAAQ,CAAC,sBAAsB,EAAE,YAAY,QAAQ,IAAI,EAAE;YAAE,UAAU;QAAK;IACrF;AACF;AAGO,MAAM,UAAU;IACrB,oBAAoB;IACpB,UAAU,CAAC,WACT,QAAQ,uBAAuB;YAC7B,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IAEF,aAAa;IACb,WAAW,CAAC,OACV,QAAQ,yBAAyB;YAC/B,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IAEF,aAAa;IACb,OAAO,CAAC,cACN,QAAQ,oBAAoB;YAC1B,QAAQ;YACR,MAAM;YACN,UAAU;QACZ;IAEF,2BAA2B;IAC3B,YAAY,IAAM,QAAQ;IAE1B,sBAAsB;IACtB,eAAe,CAAC,cACd,QAAQ,sBAAsB;YAC5B,QAAQ;YACR,MAAM;QACR;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,eACf,QAAQ,8BAA8B;YACpC,QAAQ;YACR,MAAM;QACR;IAEF,aAAa;IACb,WAAW,CAAC,QACV,QAAQ,yBAAyB;YAC/B,QAAQ;YACR,MAAM;gBAAE;YAAM;YACd,UAAU;QACZ;IAEF,kBAAkB;IAClB,gBAAgB,CAAC,QACf,QAAQ,8BAA8B;YACpC,QAAQ;YACR,MAAM;gBAAE;YAAM;YACd,UAAU;QACZ;IAEF,iBAAiB;IACjB,eAAe,CAAC,OAAe,MAAc,cAC3C,QAAQ,6BAA6B;YACnC,QAAQ;YACR,MAAM;gBAAE;gBAAO;gBAAM;YAAY;YACjC,UAAU;QACZ;IAEF,SAAS;IACT,QAAQ,IACN,QAAQ,qBAAqB;YAC3B,QAAQ;QACV;IAEF,oBAAoB;IACpB,mBAAmB,IAAM,QAAQ;IAEjC,sBAAsB,CAAC,QACrB,QACE,CAAC,gCAAgC,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAGvE,uBAAuB,CAAC,QACtB,QACE,CAAC,iCAAiC,EAAE,QAAQ,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAGxE,oBAAoB,CAAC,SACnB,QAAQ,2BAA2B;YACjC,QAAQ;YACR,MAAM;gBAAE;YAAO;QACjB;IAEF,WAAW,CAAC,QAAgB,cAC1B,QAAQ,2BAA2B;YACjC,QAAQ;YACR,MAAM;gBAAE;gBAAQ;YAAY;QAC9B;IAEF,YAAY,CAAC,QAAgB,cAC3B,QAAQ,4BAA4B;YAClC,QAAQ;YACR,MAAM;gBAAE;gBAAQ;YAAY;QAC9B;AACJ;AAGO,MAAM,YAAY;IACvB,sBAAsB;IACtB,cAAc,CAAC,aAKb,QAAQ,gBAAgB;YACtB,QAAQ;YACR,MAAM;QACR;IAEF,4BAA4B;IAC5B,cAAc,CAAC,UAAkB,aAI/B,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE;YAClC,QAAQ;YACR,MAAM;QACR;IAEF,4BAA4B;IAC5B,kBAAkB,CAAC,UAAkB,OAAe,CAAC,EAAE,QAAgB,EAAE,GACvE,QAAQ,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO,EAAE;YACrE,UAAU;QACZ;IAEF,6BAA6B;IAC7B,gBAAgB,CAAC,OAAe,CAAC,EAAE,QAAgB,EAAE,GACnD,QAAQ,CAAC,qBAAqB,EAAE,KAAK,OAAO,EAAE,OAAO;IAEvD,mBAAmB;IACnB,WAAW,CAAC,WACV,QAAQ,CAAC,aAAa,EAAE,UAAU,EAAE;YAClC,UAAU;QACZ;IAEF,qCAAqC;IACrC,eAAe,CAAC,WACd,QAAQ,CAAC,oBAAoB,EAAE,SAAS,WAAW,CAAC;IAEtD,+BAA+B;IAC/B,sBAAsB,CAAC,WACrB,QAAQ,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,EAAE;YAC/C,UAAU;QACZ;IAEF,kBAAkB;IAClB,YAAY,CAAC,WACX,QAAQ,CAAC,mBAAmB,EAAE,SAAS,KAAK,CAAC,EAAE;YAC7C,QAAQ;QACV;IAEF,YAAY,CAAC,WACX,QAAQ,CAAC,mBAAmB,EAAE,SAAS,KAAK,CAAC,EAAE;YAC7C,QAAQ;QACV;IAEF,cAAc,CAAC,WACb,QAAQ,CAAC,mBAAmB,EAAE,UAAU,EAAE;YACxC,QAAQ;QACV;IAEF,mBAAmB,CAAC,OAAe,CAAC,EAAE,QAAgB,EAAE,GACtD,QAAQ,CAAC,gCAAgC,EAAE,KAAK,OAAO,EAAE,OAAO;IAElE,oBAAoB,CAAC,OAAe,EAAE,GACpC,QAAQ,CAAC,mCAAmC,EAAE,MAAM;AACxD", "debugId": null}}, {"offset": {"line": 549, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\r\nimport { authAPI, User } from '@/lib/api';\r\n\r\ninterface AuthContextType {\r\n    user: User | null;\r\n    token: string | null;\r\n    isLoading: boolean;\r\n    isAuthenticated: boolean;\r\n    login: (phone: string, password: string) => Promise<void>;\r\n    register: (userData: { name: string; email: string; phone: string; password: string; referralCode?: string }) => Promise<any>;\r\n    verifyOTP: (phone: string, code: string) => Promise<void>;\r\n    logout: () => void;\r\n    resendOTP: (phone: string) => Promise<any>;\r\n    forgotPassword: (phone: string) => Promise<any>;\r\n    resetPassword: (phone: string, code: string, newPassword: string) => Promise<void>;\r\n}\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\ninterface AuthProviderProps {\r\n    children: ReactNode;\r\n}\r\n\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n    const [user, setUser] = useState<User | null>(null);\r\n    const [token, setToken] = useState<string | null>(null);\r\n    const [isLoading, setIsLoading] = useState(true);\r\n\r\n    // Initialize auth state from localStorage\r\n    useEffect(() => {\r\n        const initAuth = async () => {\r\n            const storedToken = localStorage.getItem('token');\r\n            const storedUser = localStorage.getItem('user');\r\n\r\n            if (storedToken && storedUser) {\r\n                try {\r\n                    setToken(storedToken);\r\n                    setUser(JSON.parse(storedUser));\r\n                    \r\n                    // Verify token is still valid by fetching profile\r\n                    const profile = await authAPI.getProfile();\r\n                    setUser(profile.user);\r\n                } catch (error) {\r\n                    console.error('Token validation failed:', error);\r\n                    // Clear invalid token\r\n                    localStorage.removeItem('token');\r\n                    localStorage.removeItem('user');\r\n                    setToken(null);\r\n                    setUser(null);\r\n                }\r\n            }\r\n            setIsLoading(false);\r\n        };\r\n\r\n        initAuth();\r\n    }, []);\r\n\r\n    const login = async (phone: string, password: string) => {\r\n        try {\r\n            const response = await authAPI.login({ phone, password });\r\n            const userData = response.user; // Backend returns user data directly\r\n            const userToken = userData.token; // Token is within the user object\r\n            \r\n            // Remove token from user data for storage\r\n            const userDataForStorage = {\r\n                user_id: userData.user_id,\r\n                phone: userData.phone,\r\n                name: userData.name,\r\n                email: userData.email\r\n            };\r\n            \r\n            setUser(userDataForStorage);\r\n            setToken(userToken);\r\n            \r\n            // Store in localStorage\r\n            localStorage.setItem('token', userToken);\r\n            localStorage.setItem('user', JSON.stringify(userDataForStorage));\r\n        } catch (error: any) {\r\n            throw new Error(error.message || 'Login failed');\r\n        }\r\n    };\r\n\r\n    const register = async (userData: { name: string; email: string; phone: string; password: string; referralCode?: string }) => {\r\n        try {\r\n            const response = await authAPI.register(userData);\r\n            return response;\r\n        } catch (error: any) {\r\n            throw new Error(error.message || 'Registration failed');\r\n        }\r\n    };\r\n\r\n    const verifyOTP = async (phone: string, code: string) => {\r\n        try {\r\n            const response = await authAPI.verifyOTP({ phone, code });\r\n            const userData = response.user; // Backend returns user data directly\r\n            const userToken = userData.token; // Token is within the user object\r\n            \r\n            // Remove token from user data for storage\r\n            const userDataForStorage = {\r\n                user_id: userData.user_id,\r\n                phone: userData.phone,\r\n                name: userData.name,\r\n                email: userData.email\r\n            };\r\n            \r\n            setUser(userDataForStorage);\r\n            setToken(userToken);\r\n            \r\n            // Store in localStorage\r\n            localStorage.setItem('token', userToken);\r\n            localStorage.setItem('user', JSON.stringify(userDataForStorage));\r\n        } catch (error: any) {\r\n            throw new Error(error.message || 'OTP verification failed');\r\n        }\r\n    };\r\n\r\n    const logout = async () => {\r\n        try {\r\n            if (token) {\r\n                await authAPI.logout();\r\n            }\r\n        } catch (error) {\r\n            console.error('Logout API call failed:', error);\r\n        } finally {\r\n            // Clear state regardless of API call success\r\n            setUser(null);\r\n            setToken(null);\r\n            localStorage.removeItem('token');\r\n            localStorage.removeItem('user');\r\n        }\r\n    };\r\n\r\n    const resendOTP = async (phone: string) => {\r\n        try {\r\n            const response = await authAPI.resendOTP(phone);\r\n            return response;\r\n        } catch (error: any) {\r\n            throw new Error(error.message || 'Failed to resend OTP');\r\n        }\r\n    };\r\n\r\n    const forgotPassword = async (phone: string) => {\r\n        try {\r\n            const response = await authAPI.forgotPassword(phone);\r\n            return response;\r\n        } catch (error: any) {\r\n            throw new Error(error.message || 'Failed to request password reset');\r\n        }\r\n    };\r\n\r\n    const resetPassword = async (phone: string, code: string, newPassword: string) => {\r\n        try {\r\n            await authAPI.resetPassword(phone, code, newPassword);\r\n        } catch (error: any) {\r\n            throw new Error(error.message || 'Password reset failed');\r\n        }\r\n    };\r\n\r\n    const value: AuthContextType = {\r\n        user,\r\n        token,\r\n        isLoading,\r\n        isAuthenticated: !!user && !!token,\r\n        login,\r\n        register,\r\n        verifyOTP,\r\n        logout,\r\n        resendOTP,\r\n        forgotPassword,\r\n        resetPassword\r\n    };\r\n\r\n    return (\r\n        <AuthContext.Provider value={value}>\r\n            {children}\r\n        </AuthContext.Provider>\r\n    );\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n    const context = useContext(AuthContext);\r\n    if (context === undefined) {\r\n        throw new Error('useAuth must be used within an AuthProvider');\r\n    }\r\n    return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAMxD,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAClE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,WAAW;YACb,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,MAAM,aAAa,aAAa,OAAO,CAAC;YAExC,IAAI,eAAe,YAAY;gBAC3B,IAAI;oBACA,SAAS;oBACT,QAAQ,KAAK,KAAK,CAAC;oBAEnB,kDAAkD;oBAClD,MAAM,UAAU,MAAM,iHAAA,CAAA,UAAO,CAAC,UAAU;oBACxC,QAAQ,QAAQ,IAAI;gBACxB,EAAE,OAAO,OAAO;oBACZ,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,sBAAsB;oBACtB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,SAAS;oBACT,QAAQ;gBACZ;YACJ;YACA,aAAa;QACjB;QAEA;IACJ,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO,OAAe;QAChC,IAAI;YACA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YACvD,MAAM,WAAW,SAAS,IAAI,EAAE,qCAAqC;YACrE,MAAM,YAAY,SAAS,KAAK,EAAE,kCAAkC;YAEpE,0CAA0C;YAC1C,MAAM,qBAAqB;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;YACzB;YAEA,QAAQ;YACR,SAAS;YAET,wBAAwB;YACxB,aAAa,OAAO,CAAC,SAAS;YAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAChD,EAAE,OAAO,OAAY;YACjB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACrC;IACJ;IAEA,MAAM,WAAW,OAAO;QACpB,IAAI;YACA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;YACxC,OAAO;QACX,EAAE,OAAO,OAAY;YACjB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACrC;IACJ;IAEA,MAAM,YAAY,OAAO,OAAe;QACpC,IAAI;YACA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;gBAAE;gBAAO;YAAK;YACvD,MAAM,WAAW,SAAS,IAAI,EAAE,qCAAqC;YACrE,MAAM,YAAY,SAAS,KAAK,EAAE,kCAAkC;YAEpE,0CAA0C;YAC1C,MAAM,qBAAqB;gBACvB,SAAS,SAAS,OAAO;gBACzB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;YACzB;YAEA,QAAQ;YACR,SAAS;YAET,wBAAwB;YACxB,aAAa,OAAO,CAAC,SAAS;YAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAChD,EAAE,OAAO,OAAY;YACjB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACrC;IACJ;IAEA,MAAM,SAAS;QACX,IAAI;YACA,IAAI,OAAO;gBACP,MAAM,iHAAA,CAAA,UAAO,CAAC,MAAM;YACxB;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC7C,SAAU;YACN,6CAA6C;YAC7C,QAAQ;YACR,SAAS;YACT,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC5B;IACJ;IAEA,MAAM,YAAY,OAAO;QACrB,IAAI;YACA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,SAAS,CAAC;YACzC,OAAO;QACX,EAAE,OAAO,OAAY;YACjB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACrC;IACJ;IAEA,MAAM,iBAAiB,OAAO;QAC1B,IAAI;YACA,MAAM,WAAW,MAAM,iHAAA,CAAA,UAAO,CAAC,cAAc,CAAC;YAC9C,OAAO;QACX,EAAE,OAAO,OAAY;YACjB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACrC;IACJ;IAEA,MAAM,gBAAgB,OAAO,OAAe,MAAc;QACtD,IAAI;YACA,MAAM,iHAAA,CAAA,UAAO,CAAC,aAAa,CAAC,OAAO,MAAM;QAC7C,EAAE,OAAO,OAAY;YACjB,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;QACrC;IACJ;IAEA,MAAM,QAAyB;QAC3B;QACA;QACA;QACA,iBAAiB,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IAEA,qBACI,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBACxB;;;;;;AAGb;AAEO,MAAM,UAAU;IACnB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACvB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/Navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  ChevronDown,\r\n  User,\r\n  Wallet,\r\n  LogOut,\r\n  LogIn,\r\n  UserPlus,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\n\r\nconst Navigation = () => {\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, isAuthenticated, logout } = useAuth();\r\n  const [showExpertsDropdown, setShowExpertsDropdown] = useState(false);\r\n  const [showUserDropdown, setShowUserDropdown] = useState(false);\r\n\r\n  const navItems = [\r\n    { href: \"/ai-experts\", label: \"AI Experts\" },\r\n    // History menu only for authenticated users\r\n    ...(isAuthenticated ? [{ href: \"/history\", label: \"History\" }] : []),\r\n  ];\r\n\r\n  const expertItems = [\r\n    { href: \"/experts?view=overview\", label: \"Overview\" },\r\n    { href: \"/experts?view=manage\", label: \"Manage Expert\" },\r\n  ];\r\n\r\n  const userMenuItems = [\r\n    { href: \"/profile\", label: \"My Profile\", icon: User },\r\n    { href: \"/affiliate\", label: \"Affiliate Program\", icon: Users },\r\n    { href: \"/balance\", label: \"Saldo\", icon: Wallet },\r\n  ];\r\n\r\n  const handleLogout = async () => {\r\n    await logout();\r\n    setShowUserDropdown(false);\r\n    router.push(\"/\");\r\n  };\r\n\r\n  return (\r\n    <nav className=\"bg-white shadow-lg border-b border-gray-100\">\r\n      <div className=\"max-w-7xl mx-auto px-4\">\r\n        <div className=\"flex justify-between items-center h-16\">\r\n          <div className=\"flex items-center space-x-8\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"text-2xl font-bold\"\r\n              style={{ color: \"#1E3A8A\" }}\r\n            >\r\n              PakarAI\r\n            </Link>\r\n\r\n            <div className=\"flex space-x-6\">\r\n              {navItems.map((item) => (\r\n                <Link\r\n                  key={item.href}\r\n                  href={item.href}\r\n                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\r\n                    pathname === item.href\r\n                      ? \"text-white shadow-lg\"\r\n                      : \"text-gray-600 hover:text-white hover:shadow-md\"\r\n                  }`}\r\n                  style={\r\n                    pathname === item.href\r\n                      ? { backgroundColor: \"#1E3A8A\" }\r\n                      : { backgroundColor: \"transparent\" }\r\n                  }\r\n                  onMouseEnter={(e) => {\r\n                    if (pathname !== item.href) {\r\n                      e.currentTarget.style.backgroundColor = \"#1E3A8A\";\r\n                    }\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    if (pathname !== item.href) {\r\n                      e.currentTarget.style.backgroundColor = \"transparent\";\r\n                    }\r\n                  }}\r\n                >\r\n                  {item.label}\r\n                </Link>\r\n              ))}\r\n\r\n              {/* AI Experts Dropdown - only for authenticated users */}\r\n              {isAuthenticated && (\r\n                <div\r\n                  className=\"relative\"\r\n                  onMouseEnter={() => setShowExpertsDropdown(true)}\r\n                  onMouseLeave={() => setShowExpertsDropdown(false)}\r\n                >\r\n                  <button\r\n                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-1 ${\r\n                      pathname.startsWith(\"/experts\")\r\n                        ? \"text-white shadow-lg\"\r\n                        : \"text-gray-600 hover:text-white hover:shadow-md\"\r\n                    }`}\r\n                    style={\r\n                      pathname.startsWith(\"/experts\")\r\n                        ? { backgroundColor: \"#1E3A8A\" }\r\n                        : { backgroundColor: \"transparent\" }\r\n                    }\r\n                    onMouseEnter={(e) => {\r\n                      if (!pathname.startsWith(\"/experts\")) {\r\n                        e.currentTarget.style.backgroundColor = \"#1E3A8A\";\r\n                      }\r\n                    }}\r\n                    onMouseLeave={(e) => {\r\n                      if (!pathname.startsWith(\"/experts\")) {\r\n                        e.currentTarget.style.backgroundColor = \"transparent\";\r\n                      }\r\n                    }}\r\n                  >\r\n                    My Experts\r\n                    <ChevronDown className=\"w-4 h-4\" />\r\n                  </button>\r\n\r\n                  {showExpertsDropdown && (\r\n                    <div className=\"absolute top-full left-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\r\n                      {expertItems.map((item) => (\r\n                        <Link\r\n                          key={item.href}\r\n                          href={item.href}\r\n                          className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors\"\r\n                        >\r\n                          {item.label}\r\n                        </Link>\r\n                      ))}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* User Menu - Authenticated vs Unauthenticated */}\r\n          {isAuthenticated && user ? (\r\n            <div\r\n              className=\"relative\"\r\n              onMouseEnter={() => setShowUserDropdown(true)}\r\n              onMouseLeave={() => setShowUserDropdown(false)}\r\n            >\r\n              <button className=\"flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200\">\r\n                <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\r\n                  <User className=\"w-4 h-4 text-white\" />\r\n                </div>\r\n                <span className=\"hidden md:block\">{user.name}</span>\r\n                <ChevronDown className=\"w-4 h-4\" />\r\n              </button>\r\n\r\n              {showUserDropdown && (\r\n                <div className=\"absolute top-full right-0 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\">\r\n                  <div className=\"px-4 py-2 border-b border-gray-200\">\r\n                    <p className=\"text-sm font-medium text-gray-900\">\r\n                      {user.name}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-500\">{user.email}</p>\r\n                  </div>\r\n\r\n                  <Link\r\n                    href=\"/dashboard\"\r\n                    className=\"flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors\"\r\n                  >\r\n                    <User className=\"w-4 h-4\" />\r\n                    <span>Dashboard</span>\r\n                  </Link>\r\n\r\n                  {userMenuItems.map((item) => {\r\n                    const IconComponent = item.icon;\r\n                    return (\r\n                      <Link\r\n                        key={item.href}\r\n                        href={item.href}\r\n                        className=\"flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors\"\r\n                      >\r\n                        <IconComponent className=\"w-4 h-4\" />\r\n                        <span>{item.label}</span>\r\n                      </Link>\r\n                    );\r\n                  })}\r\n                  <hr className=\"my-1 border-gray-200\" />\r\n                  <button\r\n                    onClick={handleLogout}\r\n                    className=\"flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors w-full text-left\"\r\n                  >\r\n                    <LogOut className=\"w-4 h-4\" />\r\n                    <span>Logout</span>\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Link\r\n                href=\"/login\"\r\n                className=\"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors\"\r\n              >\r\n                <LogIn className=\"w-4 h-4\" />\r\n                <span>Login</span>\r\n              </Link>\r\n              <Link\r\n                href=\"/register\"\r\n                className=\"flex items-center space-x-2 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors\"\r\n              >\r\n                <UserPlus className=\"w-4 h-4\" />\r\n                <span>Sign Up</span>\r\n              </Link>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default Navigation;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAdA;;;;;;;AAgBA,MAAM,aAAa;IACjB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,OAAO;QAAa;QAC3C,4CAA4C;WACxC,kBAAkB;YAAC;gBAAE,MAAM;gBAAY,OAAO;YAAU;SAAE,GAAG,EAAE;KACpE;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAA0B,OAAO;QAAW;QACpD;YAAE,MAAM;YAAwB,OAAO;QAAgB;KACxD;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAY,OAAO;YAAc,MAAM,kMAAA,CAAA,OAAI;QAAC;QACpD;YAAE,MAAM;YAAc,OAAO;YAAqB,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC9D;YAAE,MAAM;YAAY,OAAO;YAAS,MAAM,sMAAA,CAAA,SAAM;QAAC;KAClD;IAED,MAAM,eAAe;QACnB,MAAM;QACN,oBAAoB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAU;0CAC3B;;;;;;0CAID,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,qEAAqE,EAC/E,aAAa,KAAK,IAAI,GAClB,yBACA,kDACJ;4CACF,OACE,aAAa,KAAK,IAAI,GAClB;gDAAE,iBAAiB;4CAAU,IAC7B;gDAAE,iBAAiB;4CAAc;4CAEvC,cAAc,CAAC;gDACb,IAAI,aAAa,KAAK,IAAI,EAAE;oDAC1B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;4CACF;4CACA,cAAc,CAAC;gDACb,IAAI,aAAa,KAAK,IAAI,EAAE;oDAC1B,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;gDAC1C;4CACF;sDAEC,KAAK,KAAK;2CAvBN,KAAK,IAAI;;;;;oCA4BjB,iCACC,8OAAC;wCACC,WAAU;wCACV,cAAc,IAAM,uBAAuB;wCAC3C,cAAc,IAAM,uBAAuB;;0DAE3C,8OAAC;gDACC,WAAW,CAAC,6FAA6F,EACvG,SAAS,UAAU,CAAC,cAChB,yBACA,kDACJ;gDACF,OACE,SAAS,UAAU,CAAC,cAChB;oDAAE,iBAAiB;gDAAU,IAC7B;oDAAE,iBAAiB;gDAAc;gDAEvC,cAAc,CAAC;oDACb,IAAI,CAAC,SAAS,UAAU,CAAC,aAAa;wDACpC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;gDACF;gDACA,cAAc,CAAC;oDACb,IAAI,CAAC,SAAS,UAAU,CAAC,aAAa;wDACpC,EAAE,aAAa,CAAC,KAAK,CAAC,eAAe,GAAG;oDAC1C;gDACF;;oDACD;kEAEC,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;4CAGxB,qCACC,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,KAAK;uDAJN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAe7B,mBAAmB,qBAClB,8OAAC;wBACC,WAAU;wBACV,cAAc,IAAM,oBAAoB;wBACxC,cAAc,IAAM,oBAAoB;;0CAExC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDAAmB,KAAK,IAAI;;;;;;kDAC5C,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;4BAGxB,kCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,KAAK;;;;;;;;;;;;kDAGlD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;oCAGP,cAAc,GAAG,CAAC,CAAC;wCAClB,MAAM,gBAAgB,KAAK,IAAI;wCAC/B,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;;8DAEV,8OAAC;oDAAc,WAAU;;;;;;8DACzB,8OAAC;8DAAM,KAAK,KAAK;;;;;;;2CALZ,KAAK,IAAI;;;;;oCAQpB;kDACA,8OAAC;wCAAG,WAAU;;;;;;kDACd,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;iFAMd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,wMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe", "debugId": null}}, {"offset": {"line": 1185, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/contexts/SocketContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useEffect, useState, useRef, useCallback } from 'react';\r\nimport { io, Socket } from 'socket.io-client';\r\nimport { useAuth } from './AuthContext';\r\n\r\ninterface SocketContextType {\r\n  socket: Socket | null;\r\n  isConnected: boolean;\r\n  connectionError: string | null;\r\n  joinChat: (expertId: string, sessionId?: string) => void;\r\n  leaveChat: () => void;\r\n  sendChatMessage: (message: string, expertId: string, sessionId?: string) => void;\r\n  currentChatRoom: string | null;\r\n  reconnect: () => void;\r\n}\r\n\r\nconst SocketContext = createContext<SocketContextType>({\r\n  socket: null,\r\n  isConnected: false,\r\n  connectionError: null,\r\n  joinChat: () => {},\r\n  leaveChat: () => {},\r\n  sendChatMessage: () => {},\r\n  currentChatRoom: null,\r\n  reconnect: () => {},\r\n});\r\n\r\nexport const useSocket = () => {\r\n  const context = useContext(SocketContext);\r\n  if (!context) {\r\n    throw new Error('useSocket must be used within a SocketProvider');\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface SocketProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {\r\n  const { token, isAuthenticated, isLoading } = useAuth();\r\n  const [socket, setSocket] = useState<Socket | null>(null);\r\n  const [isConnected, setIsConnected] = useState(false);\r\n  const [connectionError, setConnectionError] = useState<string | null>(null);\r\n  const [currentChatRoom, setCurrentChatRoom] = useState<string | null>(null);\r\n  const reconnectAttempts = useRef(0);\r\n  const maxReconnectAttempts = 5;\r\n  const socketRef = useRef<Socket | null>(null);\r\n\r\n  // Initialize socket when auth state changes\r\n  useEffect(() => {\r\n    console.log('🔌 Socket initialization check:', {\r\n      isLoading,\r\n      isAuthenticated,\r\n      hasToken: !!token,\r\n      tokenLength: token?.length,\r\n      tokenStart: token?.substring(0, 20) + '...'\r\n    });\r\n    \r\n    // Don't initialize if auth is still loading\r\n    if (isLoading) {\r\n      console.log('🔌 Auth still loading, waiting...');\r\n      return;\r\n    }\r\n    \r\n    // Clean up existing socket first\r\n    if (socketRef.current) {\r\n      console.log('🧹 Cleaning up existing socket connection');\r\n      socketRef.current.disconnect();\r\n      socketRef.current = null;\r\n      setSocket(null);\r\n      setIsConnected(false);\r\n      setCurrentChatRoom(null);\r\n    }\r\n    \r\n    if (!isAuthenticated || !token) {\r\n      console.log('🔌 User not authenticated, skipping socket connection');\r\n      setConnectionError('Please log in to use real-time chat');\r\n      return;\r\n    }\r\n\r\n    const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n    \r\n    console.log('🔌 Initializing Socket.IO connection to:', API_URL);\r\n\r\n    // Create socket connection\r\n    const newSocket = io(API_URL, {\r\n      auth: {\r\n        token: token\r\n      },\r\n      transports: ['polling', 'websocket'], // Try polling first, then upgrade to websocket\r\n      timeout: 20000,\r\n      reconnection: true,\r\n      reconnectionAttempts: maxReconnectAttempts,\r\n      reconnectionDelay: 1000,\r\n      reconnectionDelayMax: 5000,\r\n      upgrade: true, // Allow transport upgrade\r\n      rememberUpgrade: false, // Don't remember the upgrade for next time\r\n    });\r\n\r\n    // Connection event handlers\r\n    newSocket.on('connect', () => {\r\n      console.log('✅ Socket connected:', newSocket.id);\r\n      console.log('🚀 Transport used:', newSocket.io.engine.transport.name);\r\n      // console.log('🔗 Socket URL:', newSocket.io.uri); // uri is private\r\n      setIsConnected(true);\r\n      setConnectionError(null);\r\n      reconnectAttempts.current = 0;\r\n    });\r\n\r\n    newSocket.on('disconnect', (reason) => {\r\n      console.log('❌ Socket disconnected:', reason);\r\n      setIsConnected(false);\r\n      setCurrentChatRoom(null);\r\n      \r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, try to reconnect\r\n        newSocket.connect();\r\n      }\r\n    });\r\n\r\n    newSocket.on('connect_error', (error) => {\r\n      console.error('🔌 Socket connection error:', error);\r\n      console.error('🔌 Error details:', {\r\n        message: error.message,\r\n        name: error.name,\r\n        stack: error.stack\r\n      });\r\n      \r\n      setConnectionError(error.message || 'Connection failed');\r\n      setIsConnected(false);\r\n      \r\n      reconnectAttempts.current += 1;\r\n      if (reconnectAttempts.current >= maxReconnectAttempts) {\r\n        setConnectionError('Failed to connect after multiple attempts');\r\n      }\r\n    });\r\n\r\n    // Chat event handlers\r\n    newSocket.on('chat_joined', (data) => {\r\n      console.log('🏠 Joined chat room:', data);\r\n      setCurrentChatRoom(data.room);\r\n    });\r\n\r\n    newSocket.on('chat_error', (data) => {\r\n      console.error('💬 Chat error:', data);\r\n    });\r\n\r\n    newSocket.on('typing_start', (data) => {\r\n      console.log('⌨️ User started typing:', data);\r\n    });\r\n\r\n    newSocket.on('typing_stop', (data) => {\r\n      console.log('⌨️ User stopped typing:', data);\r\n    });\r\n\r\n    // Authentication error handler\r\n    newSocket.on('error', (error) => {\r\n      console.error('🔐 Socket authentication error:', error);\r\n      if (error.message && error.message.includes('token')) {\r\n        setConnectionError('Authentication failed - please log in again');\r\n        // Clear invalid token\r\n        localStorage.removeItem('token');\r\n      } else {\r\n        setConnectionError('Connection failed');\r\n      }\r\n      setIsConnected(false);\r\n    });\r\n\r\n    // Transport upgrade events\r\n    newSocket.io.engine.on('upgrade', () => {\r\n      console.log('🔄 Transport upgraded to:', newSocket.io.engine.transport.name);\r\n    });\r\n\r\n    newSocket.io.engine.on('upgradeError', (error) => {\r\n      console.error('❌ Transport upgrade failed:', error);\r\n    });\r\n\r\n    socketRef.current = newSocket;\r\n    setSocket(newSocket);\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      console.log('🧹 Cleaning up socket connection on unmount/change');\r\n      if (socketRef.current) {\r\n        socketRef.current.disconnect();\r\n        socketRef.current = null;\r\n      }\r\n    };\r\n  }, [isLoading, isAuthenticated, token]); // Remove socket from dependencies to prevent infinite loop\r\n\r\n  const joinChat = useCallback((expertId: string, sessionId?: string) => {\r\n    if (!socket || !isConnected) {\r\n      console.warn('⚠️ Cannot join chat: socket not connected');\r\n      return;\r\n    }\r\n\r\n    console.log('🏠 Joining chat:', { expertId, sessionId });\r\n    socket.emit('join_chat', { expertId, sessionId });\r\n  }, [socket, isConnected]);\r\n\r\n  const leaveChat = useCallback(() => {\r\n    if (!socket || !isConnected) {\r\n      console.warn('⚠️ Cannot leave chat: socket not connected');\r\n      return;\r\n    }\r\n\r\n    console.log('🚪 Leaving chat');\r\n    socket.emit('leave_chat');\r\n    setCurrentChatRoom(null);\r\n  }, [socket, isConnected]);\r\n\r\n  const sendChatMessage = useCallback((message: string, expertId: string, sessionId?: string) => {\r\n    if (!socket || !isConnected) {\r\n      console.warn('⚠️ Cannot send message: socket not connected');\r\n      return;\r\n    }\r\n\r\n    console.log('📤 Sending chat message:', { message: message.substring(0, 50) + '...', expertId, sessionId });\r\n    socket.emit('start_chat_stream', { message, expertId, sessionId });\r\n  }, [socket, isConnected]);\r\n\r\n  const reconnect = useCallback(() => {\r\n    console.log('🔄 Manual reconnection requested');\r\n    if (socket) {\r\n      socket.disconnect();\r\n    }\r\n    setSocket(null);\r\n    setIsConnected(false);\r\n    setConnectionError(null);\r\n    setCurrentChatRoom(null);\r\n    reconnectAttempts.current = 0;\r\n    \r\n    // Force re-initialization by temporarily clearing and restoring token\r\n    // This will trigger the useEffect to run again\r\n    const currentToken = token;\r\n    if (currentToken) {\r\n      // Small delay to ensure cleanup happens first\r\n      setTimeout(() => {\r\n        // The useEffect will automatically reinitialize when dependencies change\r\n        console.log('🔄 Reconnection will be handled by useEffect');\r\n      }, 100);\r\n    }\r\n  }, [socket, token]);\r\n\r\n  const value: SocketContextType = {\r\n    socket,\r\n    isConnected,\r\n    connectionError,\r\n    joinChat,\r\n    leaveChat,\r\n    sendChatMessage,\r\n    currentChatRoom,\r\n    reconnect,\r\n  };\r\n\r\n  return (\r\n    <SocketContext.Provider value={value}>\r\n      {children}\r\n    </SocketContext.Provider>\r\n  );\r\n};\r\n\r\nexport default SocketContext;\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAiBA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqB;IACrD,QAAQ;IACR,aAAa;IACb,iBAAiB;IACjB,UAAU,KAAO;IACjB,WAAW,KAAO;IAClB,iBAAiB,KAAO;IACxB,iBAAiB;IACjB,WAAW,KAAO;AACpB;AAEO,MAAM,YAAY;IACvB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE;IACxE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,uBAAuB;IAC7B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAExC,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,mCAAmC;YAC7C;YACA;YACA,UAAU,CAAC,CAAC;YACZ,aAAa,OAAO;YACpB,YAAY,OAAO,UAAU,GAAG,MAAM;QACxC;QAEA,4CAA4C;QAC5C,IAAI,WAAW;YACb,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,iCAAiC;QACjC,IAAI,UAAU,OAAO,EAAE;YACrB,QAAQ,GAAG,CAAC;YACZ,UAAU,OAAO,CAAC,UAAU;YAC5B,UAAU,OAAO,GAAG;YACpB,UAAU;YACV,eAAe;YACf,mBAAmB;QACrB;QAEA,IAAI,CAAC,mBAAmB,CAAC,OAAO;YAC9B,QAAQ,GAAG,CAAC;YACZ,mBAAmB;YACnB;QACF;QAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAEnD,QAAQ,GAAG,CAAC,4CAA4C;QAExD,2BAA2B;QAC3B,MAAM,YAAY,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,SAAS;YAC5B,MAAM;gBACJ,OAAO;YACT;YACA,YAAY;gBAAC;gBAAW;aAAY;YACpC,SAAS;YACT,cAAc;YACd,sBAAsB;YACtB,mBAAmB;YACnB,sBAAsB;YACtB,SAAS;YACT,iBAAiB;QACnB;QAEA,4BAA4B;QAC5B,UAAU,EAAE,CAAC,WAAW;YACtB,QAAQ,GAAG,CAAC,uBAAuB,UAAU,EAAE;YAC/C,QAAQ,GAAG,CAAC,sBAAsB,UAAU,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI;YACpE,qEAAqE;YACrE,eAAe;YACf,mBAAmB;YACnB,kBAAkB,OAAO,GAAG;QAC9B;QAEA,UAAU,EAAE,CAAC,cAAc,CAAC;YAC1B,QAAQ,GAAG,CAAC,0BAA0B;YACtC,eAAe;YACf,mBAAmB;YAEnB,IAAI,WAAW,wBAAwB;gBACrC,gDAAgD;gBAChD,UAAU,OAAO;YACnB;QACF;QAEA,UAAU,EAAE,CAAC,iBAAiB,CAAC;YAC7B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,QAAQ,KAAK,CAAC,qBAAqB;gBACjC,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,OAAO,MAAM,KAAK;YACpB;YAEA,mBAAmB,MAAM,OAAO,IAAI;YACpC,eAAe;YAEf,kBAAkB,OAAO,IAAI;YAC7B,IAAI,kBAAkB,OAAO,IAAI,sBAAsB;gBACrD,mBAAmB;YACrB;QACF;QAEA,sBAAsB;QACtB,UAAU,EAAE,CAAC,eAAe,CAAC;YAC3B,QAAQ,GAAG,CAAC,wBAAwB;YACpC,mBAAmB,KAAK,IAAI;QAC9B;QAEA,UAAU,EAAE,CAAC,cAAc,CAAC;YAC1B,QAAQ,KAAK,CAAC,kBAAkB;QAClC;QAEA,UAAU,EAAE,CAAC,gBAAgB,CAAC;YAC5B,QAAQ,GAAG,CAAC,2BAA2B;QACzC;QAEA,UAAU,EAAE,CAAC,eAAe,CAAC;YAC3B,QAAQ,GAAG,CAAC,2BAA2B;QACzC;QAEA,+BAA+B;QAC/B,UAAU,EAAE,CAAC,SAAS,CAAC;YACrB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACpD,mBAAmB;gBACnB,sBAAsB;gBACtB,aAAa,UAAU,CAAC;YAC1B,OAAO;gBACL,mBAAmB;YACrB;YACA,eAAe;QACjB;QAEA,2BAA2B;QAC3B,UAAU,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YAChC,QAAQ,GAAG,CAAC,6BAA6B,UAAU,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI;QAC7E;QAEA,UAAU,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC;YACtC,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,UAAU,OAAO,GAAG;QACpB,UAAU;QAEV,mBAAmB;QACnB,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,UAAU;gBAC5B,UAAU,OAAO,GAAG;YACtB;QACF;IACF,GAAG;QAAC;QAAW;QAAiB;KAAM,GAAG,2DAA2D;IAEpG,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QAC9C,IAAI,CAAC,UAAU,CAAC,aAAa;YAC3B,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,oBAAoB;YAAE;YAAU;QAAU;QACtD,OAAO,IAAI,CAAC,aAAa;YAAE;YAAU;QAAU;IACjD,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,CAAC,UAAU,CAAC,aAAa;YAC3B,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO,IAAI,CAAC;QACZ,mBAAmB;IACrB,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB,UAAkB;QACtE,IAAI,CAAC,UAAU,CAAC,aAAa;YAC3B,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,QAAQ,GAAG,CAAC,4BAA4B;YAAE,SAAS,QAAQ,SAAS,CAAC,GAAG,MAAM;YAAO;YAAU;QAAU;QACzG,OAAO,IAAI,CAAC,qBAAqB;YAAE;YAAS;YAAU;QAAU;IAClE,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,QAAQ,GAAG,CAAC;QACZ,IAAI,QAAQ;YACV,OAAO,UAAU;QACnB;QACA,UAAU;QACV,eAAe;QACf,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB,OAAO,GAAG;QAE5B,sEAAsE;QACtE,+CAA+C;QAC/C,MAAM,eAAe;QACrB,IAAI,cAAc;YAChB,8CAA8C;YAC9C,WAAW;gBACT,yEAAyE;gBACzE,QAAQ,GAAG,CAAC;YACd,GAAG;QACL;IACF,GAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,QAA2B;QAC/B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;uCAEe", "debugId": null}}]}