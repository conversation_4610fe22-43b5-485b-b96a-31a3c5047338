{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts"], "sourcesContent": ["/**\r\n * This file contains runtime types and functions that are shared between all\r\n * TurboPack ECMAScript runtimes.\r\n *\r\n * It will be prepended to the runtime code of each runtime.\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"./runtime-types.d.ts\" />\r\n\r\ntype EsmNamespaceObject = Record<string, any>\r\n\r\n// @ts-ignore Defined in `dev-base.ts`\r\ndeclare function getOrInstantiateModuleFromParent<M>(\r\n  id: ModuleId,\r\n  sourceModule: M\r\n): M\r\n\r\nconst REEXPORTED_OBJECTS = Symbol('reexported objects')\r\n\r\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>\r\n\r\ninterface ModuleContextEntry {\r\n  id: () => ModuleId\r\n  module: () => any\r\n}\r\n\r\ninterface ModuleContext {\r\n  // require call\r\n  (moduleId: ModuleId): Exports | EsmNamespaceObject\r\n\r\n  // async import call\r\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>\r\n\r\n  keys(): ModuleId[]\r\n\r\n  resolve(moduleId: ModuleId): ModuleId\r\n}\r\n\r\ntype GetOrInstantiateModuleFromParent<M extends Module> = (\r\n  moduleId: M['id'],\r\n  parentModule: M\r\n) => M\r\n\r\ndeclare function getOrInstantiateRuntimeModule(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath\r\n): Module\r\n\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty\r\nconst toStringTag = typeof Symbol !== 'undefined' && Symbol.toStringTag\r\n\r\nfunction defineProp(\r\n  obj: any,\r\n  name: PropertyKey,\r\n  options: PropertyDescriptor & ThisType<any>\r\n) {\r\n  if (!hasOwnProperty.call(obj, name)) Object.defineProperty(obj, name, options)\r\n}\r\n\r\nfunction getOverwrittenModule(\r\n  moduleCache: ModuleCache<Module>,\r\n  id: ModuleId\r\n): Module {\r\n  let module = moduleCache[id]\r\n  if (!module) {\r\n    // This is invoked when a module is merged into another module, thus it wasn't invoked via\r\n    // instantiateModule and the cache entry wasn't created yet.\r\n    module = {\r\n      exports: {},\r\n      error: undefined,\r\n      loaded: false,\r\n      id,\r\n      namespaceObject: undefined,\r\n    }\r\n    moduleCache[id] = module\r\n  }\r\n  return module\r\n}\r\n\r\n/**\r\n * Adds the getters to the exports object.\r\n */\r\nfunction esm(\r\n  exports: Exports,\r\n  getters: Record<string, (() => any) | [() => any, (v: any) => void]>\r\n) {\r\n  defineProp(exports, '__esModule', { value: true })\r\n  if (toStringTag) defineProp(exports, toStringTag, { value: 'Module' })\r\n  for (const key in getters) {\r\n    const item = getters[key]\r\n    if (Array.isArray(item)) {\r\n      defineProp(exports, key, {\r\n        get: item[0],\r\n        set: item[1],\r\n        enumerable: true,\r\n      })\r\n    } else {\r\n      defineProp(exports, key, { get: item, enumerable: true })\r\n    }\r\n  }\r\n  Object.seal(exports)\r\n}\r\n\r\n/**\r\n * Makes the module an ESM with exports\r\n */\r\nfunction esmExport(\r\n  module: Module,\r\n  exports: Exports,\r\n  moduleCache: ModuleCache<Module>,\r\n  getters: Record<string, () => any>,\r\n  id: ModuleId | undefined\r\n) {\r\n  if (id != null) {\r\n    module = getOverwrittenModule(moduleCache, id)\r\n    exports = module.exports\r\n  }\r\n  module.namespaceObject = module.exports\r\n  esm(exports, getters)\r\n}\r\n\r\nfunction ensureDynamicExports(module: Module, exports: Exports) {\r\n  let reexportedObjects = module[REEXPORTED_OBJECTS]\r\n\r\n  if (!reexportedObjects) {\r\n    reexportedObjects = module[REEXPORTED_OBJECTS] = []\r\n    module.exports = module.namespaceObject = new Proxy(exports, {\r\n      get(target, prop) {\r\n        if (\r\n          hasOwnProperty.call(target, prop) ||\r\n          prop === 'default' ||\r\n          prop === '__esModule'\r\n        ) {\r\n          return Reflect.get(target, prop)\r\n        }\r\n        for (const obj of reexportedObjects!) {\r\n          const value = Reflect.get(obj, prop)\r\n          if (value !== undefined) return value\r\n        }\r\n        return undefined\r\n      },\r\n      ownKeys(target) {\r\n        const keys = Reflect.ownKeys(target)\r\n        for (const obj of reexportedObjects!) {\r\n          for (const key of Reflect.ownKeys(obj)) {\r\n            if (key !== 'default' && !keys.includes(key)) keys.push(key)\r\n          }\r\n        }\r\n        return keys\r\n      },\r\n    })\r\n  }\r\n}\r\n\r\n/**\r\n * Dynamically exports properties from an object\r\n */\r\nfunction dynamicExport(\r\n  module: Module,\r\n  exports: Exports,\r\n  moduleCache: ModuleCache<Module>,\r\n  object: Record<string, any>,\r\n  id: ModuleId | undefined\r\n) {\r\n  if (id != null) {\r\n    module = getOverwrittenModule(moduleCache, id)\r\n    exports = module.exports\r\n  }\r\n  ensureDynamicExports(module, exports)\r\n\r\n  if (typeof object === 'object' && object !== null) {\r\n    module[REEXPORTED_OBJECTS]!.push(object)\r\n  }\r\n}\r\n\r\nfunction exportValue(\r\n  module: Module,\r\n  moduleCache: ModuleCache<Module>,\r\n  value: any,\r\n  id: ModuleId | undefined\r\n) {\r\n  if (id != null) {\r\n    module = getOverwrittenModule(moduleCache, id)\r\n  }\r\n  module.exports = value\r\n}\r\n\r\nfunction exportNamespace(\r\n  module: Module,\r\n  moduleCache: ModuleCache<Module>,\r\n  namespace: any,\r\n  id: ModuleId | undefined\r\n) {\r\n  if (id != null) {\r\n    module = getOverwrittenModule(moduleCache, id)\r\n  }\r\n  module.exports = module.namespaceObject = namespace\r\n}\r\n\r\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\r\n  return () => obj[key]\r\n}\r\n\r\n/**\r\n * @returns prototype of the object\r\n */\r\nconst getProto: (obj: any) => any = Object.getPrototypeOf\r\n  ? (obj) => Object.getPrototypeOf(obj)\r\n  : (obj) => obj.__proto__\r\n\r\n/** Prototypes that are not expanded for exports */\r\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)]\r\n\r\n/**\r\n * @param raw\r\n * @param ns\r\n * @param allowExportDefault\r\n *   * `false`: will have the raw module as default export\r\n *   * `true`: will have the default property as default export\r\n */\r\nfunction interopEsm(\r\n  raw: Exports,\r\n  ns: EsmNamespaceObject,\r\n  allowExportDefault?: boolean\r\n) {\r\n  const getters: { [s: string]: () => any } = Object.create(null)\r\n  for (\r\n    let current = raw;\r\n    (typeof current === 'object' || typeof current === 'function') &&\r\n    !LEAF_PROTOTYPES.includes(current);\r\n    current = getProto(current)\r\n  ) {\r\n    for (const key of Object.getOwnPropertyNames(current)) {\r\n      getters[key] = createGetter(raw, key)\r\n    }\r\n  }\r\n\r\n  // this is not really correct\r\n  // we should set the `default` getter if the imported module is a `.cjs file`\r\n  if (!(allowExportDefault && 'default' in getters)) {\r\n    getters['default'] = () => raw\r\n  }\r\n\r\n  esm(ns, getters)\r\n  return ns\r\n}\r\n\r\nfunction createNS(raw: Module['exports']): EsmNamespaceObject {\r\n  if (typeof raw === 'function') {\r\n    return function (this: any, ...args: any[]) {\r\n      return raw.apply(this, args)\r\n    }\r\n  } else {\r\n    return Object.create(null)\r\n  }\r\n}\r\n\r\nfunction esmImport(\r\n  sourceModule: Module,\r\n  id: ModuleId\r\n): Exclude<Module['namespaceObject'], undefined> {\r\n  const module = getOrInstantiateModuleFromParent(id, sourceModule)\r\n  if (module.error) throw module.error\r\n\r\n  // any ES module has to have `module.namespaceObject` defined.\r\n  if (module.namespaceObject) return module.namespaceObject\r\n\r\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\r\n  const raw = module.exports\r\n  return (module.namespaceObject = interopEsm(\r\n    raw,\r\n    createNS(raw),\r\n    raw && (raw as any).__esModule\r\n  ))\r\n}\r\n\r\n// Add a simple runtime require so that environments without one can still pass\r\n// `typeof require` CommonJS checks so that exports are correctly registered.\r\nconst runtimeRequire =\r\n  // @ts-ignore\r\n  typeof require === 'function'\r\n    ? // @ts-ignore\r\n      require\r\n    : function require() {\r\n        throw new Error('Unexpected use of runtime require')\r\n      }\r\n\r\nfunction commonJsRequire(sourceModule: Module, id: ModuleId): Exports {\r\n  const module = getOrInstantiateModuleFromParent(id, sourceModule)\r\n  if (module.error) throw module.error\r\n  return module.exports\r\n}\r\n\r\n/**\r\n * `require.context` and require/import expression runtime.\r\n */\r\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\r\n  function moduleContext(id: ModuleId): Exports {\r\n    if (hasOwnProperty.call(map, id)) {\r\n      return map[id].module()\r\n    }\r\n\r\n    const e = new Error(`Cannot find module '${id}'`)\r\n    ;(e as any).code = 'MODULE_NOT_FOUND'\r\n    throw e\r\n  }\r\n\r\n  moduleContext.keys = (): ModuleId[] => {\r\n    return Object.keys(map)\r\n  }\r\n\r\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\r\n    if (hasOwnProperty.call(map, id)) {\r\n      return map[id].id()\r\n    }\r\n\r\n    const e = new Error(`Cannot find module '${id}'`)\r\n    ;(e as any).code = 'MODULE_NOT_FOUND'\r\n    throw e\r\n  }\r\n\r\n  moduleContext.import = async (id: ModuleId) => {\r\n    return await (moduleContext(id) as Promise<Exports>)\r\n  }\r\n\r\n  return moduleContext\r\n}\r\n\r\n/**\r\n * Returns the path of a chunk defined by its data.\r\n */\r\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\r\n  return typeof chunkData === 'string' ? chunkData : chunkData.path\r\n}\r\n\r\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\r\n  return (\r\n    maybePromise != null &&\r\n    typeof maybePromise === 'object' &&\r\n    'then' in maybePromise &&\r\n    typeof maybePromise.then === 'function'\r\n  )\r\n}\r\n\r\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\r\n  return turbopackQueues in obj\r\n}\r\n\r\nfunction createPromise<T>() {\r\n  let resolve: (value: T | PromiseLike<T>) => void\r\n  let reject: (reason?: any) => void\r\n\r\n  const promise = new Promise<T>((res, rej) => {\r\n    reject = rej\r\n    resolve = res\r\n  })\r\n\r\n  return {\r\n    promise,\r\n    resolve: resolve!,\r\n    reject: reject!,\r\n  }\r\n}\r\n\r\n// everything below is adapted from webpack\r\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\r\n\r\nconst turbopackQueues = Symbol('turbopack queues')\r\nconst turbopackExports = Symbol('turbopack exports')\r\nconst turbopackError = Symbol('turbopack error')\r\n\r\nconst enum QueueStatus {\r\n  Unknown = -1,\r\n  Unresolved = 0,\r\n  Resolved = 1,\r\n}\r\n\r\ntype AsyncQueueFn = (() => void) & { queueCount: number }\r\ntype AsyncQueue = AsyncQueueFn[] & {\r\n  status: QueueStatus\r\n}\r\n\r\nfunction resolveQueue(queue?: AsyncQueue) {\r\n  if (queue && queue.status !== QueueStatus.Resolved) {\r\n    queue.status = QueueStatus.Resolved\r\n    queue.forEach((fn) => fn.queueCount--)\r\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()))\r\n  }\r\n}\r\n\r\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>\r\n\r\ntype AsyncModuleExt = {\r\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void\r\n  [turbopackExports]: Exports\r\n  [turbopackError]?: any\r\n}\r\n\r\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt\r\n\r\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\r\n  return deps.map((dep): AsyncModuleExt => {\r\n    if (dep !== null && typeof dep === 'object') {\r\n      if (isAsyncModuleExt(dep)) return dep\r\n      if (isPromise(dep)) {\r\n        const queue: AsyncQueue = Object.assign([], {\r\n          status: QueueStatus.Unresolved,\r\n        })\r\n\r\n        const obj: AsyncModuleExt = {\r\n          [turbopackExports]: {},\r\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\r\n        }\r\n\r\n        dep.then(\r\n          (res) => {\r\n            obj[turbopackExports] = res\r\n            resolveQueue(queue)\r\n          },\r\n          (err) => {\r\n            obj[turbopackError] = err\r\n            resolveQueue(queue)\r\n          }\r\n        )\r\n\r\n        return obj\r\n      }\r\n    }\r\n\r\n    return {\r\n      [turbopackExports]: dep,\r\n      [turbopackQueues]: () => {},\r\n    }\r\n  })\r\n}\r\n\r\nfunction asyncModule(\r\n  module: Module,\r\n  body: (\r\n    handleAsyncDependencies: (\r\n      deps: Dep[]\r\n    ) => Exports[] | Promise<() => Exports[]>,\r\n    asyncResult: (err?: any) => void\r\n  ) => void,\r\n  hasAwait: boolean\r\n) {\r\n  const queue: AsyncQueue | undefined = hasAwait\r\n    ? Object.assign([], { status: QueueStatus.Unknown })\r\n    : undefined\r\n\r\n  const depQueues: Set<AsyncQueue> = new Set()\r\n\r\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>()\r\n\r\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\r\n    [turbopackExports]: module.exports,\r\n    [turbopackQueues]: (fn) => {\r\n      queue && fn(queue)\r\n      depQueues.forEach(fn)\r\n      promise['catch'](() => {})\r\n    },\r\n  } satisfies AsyncModuleExt)\r\n\r\n  const attributes: PropertyDescriptor = {\r\n    get(): any {\r\n      return promise\r\n    },\r\n    set(v: any) {\r\n      // Calling `esmExport` leads to this.\r\n      if (v !== promise) {\r\n        promise[turbopackExports] = v\r\n      }\r\n    },\r\n  }\r\n\r\n  Object.defineProperty(module, 'exports', attributes)\r\n  Object.defineProperty(module, 'namespaceObject', attributes)\r\n\r\n  function handleAsyncDependencies(deps: Dep[]) {\r\n    const currentDeps = wrapDeps(deps)\r\n\r\n    const getResult = () =>\r\n      currentDeps.map((d) => {\r\n        if (d[turbopackError]) throw d[turbopackError]\r\n        return d[turbopackExports]\r\n      })\r\n\r\n    const { promise, resolve } = createPromise<() => Exports[]>()\r\n\r\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\r\n      queueCount: 0,\r\n    })\r\n\r\n    function fnQueue(q: AsyncQueue) {\r\n      if (q !== queue && !depQueues.has(q)) {\r\n        depQueues.add(q)\r\n        if (q && q.status === QueueStatus.Unresolved) {\r\n          fn.queueCount++\r\n          q.push(fn)\r\n        }\r\n      }\r\n    }\r\n\r\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue))\r\n\r\n    return fn.queueCount ? promise : getResult()\r\n  }\r\n\r\n  function asyncResult(err?: any) {\r\n    if (err) {\r\n      reject((promise[turbopackError] = err))\r\n    } else {\r\n      resolve(promise[turbopackExports])\r\n    }\r\n\r\n    resolveQueue(queue)\r\n  }\r\n\r\n  body(handleAsyncDependencies, asyncResult)\r\n\r\n  if (queue && queue.status === QueueStatus.Unknown) {\r\n    queue.status = QueueStatus.Unresolved\r\n  }\r\n}\r\n\r\n/**\r\n * A pseudo \"fake\" URL object to resolve to its relative path.\r\n *\r\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\r\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\r\n * hydration mismatch.\r\n *\r\n * This is based on webpack's existing implementation:\r\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\r\n */\r\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\r\n  const realUrl = new URL(inputUrl, 'x:/')\r\n  const values: Record<string, any> = {}\r\n  for (const key in realUrl) values[key] = (realUrl as any)[key]\r\n  values.href = inputUrl\r\n  values.pathname = inputUrl.replace(/[?#].*/, '')\r\n  values.origin = values.protocol = ''\r\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl\r\n  for (const key in values)\r\n    Object.defineProperty(this, key, {\r\n      enumerable: true,\r\n      configurable: true,\r\n      value: values[key],\r\n    })\r\n}\r\n\r\nrelativeURL.prototype = URL.prototype\r\n\r\n/**\r\n * Utility function to ensure all variants of an enum are handled.\r\n */\r\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\r\n  throw new Error(`Invariant: ${computeMessage(never)}`)\r\n}\r\n\r\n/**\r\n * A stub function to make `require` available but non-functional in ESM.\r\n */\r\nfunction requireStub(_moduleId: ModuleId): never {\r\n  throw new Error('dynamic usage of require is not supported')\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,6CAA6C;AAU7C,MAAM,qBAAqB,OAAO;AA+BlC,MAAM,iBAAiB,OAAO,SAAS,CAAC,cAAc;AACtD,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,WAAW;AAEvE,SAAS,WACP,GAAQ,EACR,IAAiB,EACjB,OAA2C;IAE3C,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,OAAO,OAAO,cAAc,CAAC,KAAK,MAAM;AACxE;AAEA,SAAS,qBACP,WAAgC,EAChC,EAAY;IAEZ,IAAI,SAAS,WAAW,CAAC,GAAG;IAC5B,IAAI,CAAC,QAAQ;QACX,0FAA0F;QAC1F,4DAA4D;QAC5D,SAAS;YACP,SAAS,CAAC;YACV,OAAO;YACP,QAAQ;YACR;YACA,iBAAiB;QACnB;QACA,WAAW,CAAC,GAAG,GAAG;IACpB;IACA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,IACP,OAAgB,EAChB,OAAoE;IAEpE,WAAW,SAAS,cAAc;QAAE,OAAO;IAAK;IAChD,IAAI,aAAa,WAAW,SAAS,aAAa;QAAE,OAAO;IAAS;IACpE,IAAK,MAAM,OAAO,QAAS;QACzB,MAAM,OAAO,OAAO,CAAC,IAAI;QACzB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,WAAW,SAAS,KAAK;gBACvB,KAAK,IAAI,CAAC,EAAE;gBACZ,KAAK,IAAI,CAAC,EAAE;gBACZ,YAAY;YACd;QACF,OAAO;YACL,WAAW,SAAS,KAAK;gBAAE,KAAK;gBAAM,YAAY;YAAK;QACzD;IACF;IACA,OAAO,IAAI,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,UACP,MAAc,EACd,OAAgB,EAChB,WAAgC,EAChC,OAAkC,EAClC,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;QAC3C,UAAU,OAAO,OAAO;IAC1B;IACA,OAAO,eAAe,GAAG,OAAO,OAAO;IACvC,IAAI,SAAS;AACf;AAEA,SAAS,qBAAqB,MAAc,EAAE,OAAgB;IAC5D,IAAI,oBAAoB,MAAM,CAAC,mBAAmB;IAElD,IAAI,CAAC,mBAAmB;QACtB,oBAAoB,MAAM,CAAC,mBAAmB,GAAG,EAAE;QACnD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG,IAAI,MAAM,SAAS;YAC3D,KAAI,MAAM,EAAE,IAAI;gBACd,IACE,eAAe,IAAI,CAAC,QAAQ,SAC5B,SAAS,aACT,SAAS,cACT;oBACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;gBAC7B;gBACA,KAAK,MAAM,OAAO,kBAAoB;oBACpC,MAAM,QAAQ,QAAQ,GAAG,CAAC,KAAK;oBAC/B,IAAI,UAAU,WAAW,OAAO;gBAClC;gBACA,OAAO;YACT;YACA,SAAQ,MAAM;gBACZ,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,MAAM,OAAO,kBAAoB;oBACpC,KAAK,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAM;wBACtC,IAAI,QAAQ,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC;oBAC1D;gBACF;gBACA,OAAO;YACT;QACF;IACF;AACF;AAEA;;CAEC,GACD,SAAS,cACP,MAAc,EACd,OAAgB,EAChB,WAAgC,EAChC,MAA2B,EAC3B,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;QAC3C,UAAU,OAAO,OAAO;IAC1B;IACA,qBAAqB,QAAQ;IAE7B,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;QACjD,MAAM,CAAC,mBAAmB,CAAE,IAAI,CAAC;IACnC;AACF;AAEA,SAAS,YACP,MAAc,EACd,WAAgC,EAChC,KAAU,EACV,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;IAC7C;IACA,OAAO,OAAO,GAAG;AACnB;AAEA,SAAS,gBACP,MAAc,EACd,WAAgC,EAChC,SAAc,EACd,EAAwB;IAExB,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,aAAa;IAC7C;IACA,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG;AAC5C;AAEA,SAAS,aAAa,GAAiC,EAAE,GAAoB;IAC3E,OAAO,IAAM,GAAG,CAAC,IAAI;AACvB;AAEA;;CAEC,GACD,MAAM,WAA8B,OAAO,cAAc,GACrD,CAAC,MAAQ,OAAO,cAAc,CAAC,OAC/B,CAAC,MAAQ,IAAI,SAAS;AAE1B,iDAAiD,GACjD,MAAM,kBAAkB;IAAC;IAAM,SAAS,CAAC;IAAI,SAAS,EAAE;IAAG,SAAS;CAAU;AAE9E;;;;;;CAMC,GACD,SAAS,WACP,GAAY,EACZ,EAAsB,EACtB,kBAA4B;IAE5B,MAAM,UAAsC,OAAO,MAAM,CAAC;IAC1D,IACE,IAAI,UAAU,KACd,CAAC,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU,KAC7D,CAAC,gBAAgB,QAAQ,CAAC,UAC1B,UAAU,SAAS,SACnB;QACA,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,SAAU;YACrD,OAAO,CAAC,IAAI,GAAG,aAAa,KAAK;QACnC;IACF;IAEA,6BAA6B;IAC7B,6EAA6E;IAC7E,IAAI,CAAC,CAAC,sBAAsB,aAAa,OAAO,GAAG;QACjD,OAAO,CAAC,UAAU,GAAG,IAAM;IAC7B;IAEA,IAAI,IAAI;IACR,OAAO;AACT;AAEA,SAAS,SAAS,GAAsB;IACtC,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,SAAqB,GAAG,IAAW;YACxC,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACzB;IACF,OAAO;QACL,OAAO,OAAO,MAAM,CAAC;IACvB;AACF;AAEA,SAAS,UACP,YAAoB,EACpB,EAAY;IAEZ,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IAEpC,8DAA8D;IAC9D,IAAI,OAAO,eAAe,EAAE,OAAO,OAAO,eAAe;IAEzD,iGAAiG;IACjG,MAAM,MAAM,OAAO,OAAO;IAC1B,OAAQ,OAAO,eAAe,GAAG,WAC/B,KACA,SAAS,MACT,OAAO,AAAC,IAAY,UAAU;AAElC;AAEA,+EAA+E;AAC/E,6EAA6E;AAC7E,MAAM,iBACJ,aAAa;AACb,OAAO,YAAY,aAEf,UACA,SAAS;IACP,MAAM,IAAI,MAAM;AAClB;AAEN,SAAS,gBAAgB,YAAoB,EAAE,EAAY;IACzD,MAAM,SAAS,iCAAiC,IAAI;IACpD,IAAI,OAAO,KAAK,EAAE,MAAM,OAAO,KAAK;IACpC,OAAO,OAAO,OAAO;AACvB;AAEA;;CAEC,GACD,SAAS,cAAc,GAAqB;IAC1C,SAAS,cAAc,EAAY;QACjC,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM;QACvB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC9C,EAAU,IAAI,GAAG;QACnB,MAAM;IACR;IAEA,cAAc,IAAI,GAAG;QACnB,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,cAAc,OAAO,GAAG,CAAC;QACvB,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;QACnB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC9C,EAAU,IAAI,GAAG;QACnB,MAAM;IACR;IAEA,cAAc,MAAM,GAAG,OAAO;QAC5B,OAAO,MAAO,cAAc;IAC9B;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,aAAa,SAAoB;IACxC,OAAO,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;AACnE;AAEA,SAAS,UAAmB,YAAiB;IAC3C,OACE,gBAAgB,QAChB,OAAO,iBAAiB,YACxB,UAAU,gBACV,OAAO,aAAa,IAAI,KAAK;AAEjC;AAEA,SAAS,iBAA+B,GAAM;IAC5C,OAAO,mBAAmB;AAC5B;AAEA,SAAS;IACP,IAAI;IACJ,IAAI;IAEJ,MAAM,UAAU,IAAI,QAAW,CAAC,KAAK;QACnC,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;QACL;QACA,SAAS;QACT,QAAQ;IACV;AACF;AAEA,2CAA2C;AAC3C,+HAA+H;AAE/H,MAAM,kBAAkB,OAAO;AAC/B,MAAM,mBAAmB,OAAO;AAChC,MAAM,iBAAiB,OAAO;AAa9B,SAAS,aAAa,KAAkB;IACtC,IAAI,SAAS,MAAM,MAAM,QAA2B;QAClD,MAAM,MAAM;QACZ,MAAM,OAAO,CAAC,CAAC,KAAO,GAAG,UAAU;QACnC,MAAM,OAAO,CAAC,CAAC,KAAQ,GAAG,UAAU,KAAK,GAAG,UAAU,KAAK;IAC7D;AACF;AAYA,SAAS,SAAS,IAAW;IAC3B,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC3C,IAAI,iBAAiB,MAAM,OAAO;YAClC,IAAI,UAAU,MAAM;gBAClB,MAAM,QAAoB,OAAO,MAAM,CAAC,EAAE,EAAE;oBAC1C,MAAM;gBACR;gBAEA,MAAM,MAAsB;oBAC1B,CAAC,iBAAiB,EAAE,CAAC;oBACrB,CAAC,gBAAgB,EAAE,CAAC,KAAoC,GAAG;gBAC7D;gBAEA,IAAI,IAAI,CACN,CAAC;oBACC,GAAG,CAAC,iBAAiB,GAAG;oBACxB,aAAa;gBACf,GACA,CAAC;oBACC,GAAG,CAAC,eAAe,GAAG;oBACtB,aAAa;gBACf;gBAGF,OAAO;YACT;QACF;QAEA,OAAO;YACL,CAAC,iBAAiB,EAAE;YACpB,CAAC,gBAAgB,EAAE,KAAO;QAC5B;IACF;AACF;AAEA,SAAS,YACP,MAAc,EACd,IAKS,EACT,QAAiB;IAEjB,MAAM,QAAgC,WAClC,OAAO,MAAM,CAAC,EAAE,EAAE;QAAE,MAAM;IAAsB,KAChD;IAEJ,MAAM,YAA6B,IAAI;IAEvC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;IAEjD,MAAM,UAA8B,OAAO,MAAM,CAAC,YAAY;QAC5D,CAAC,iBAAiB,EAAE,OAAO,OAAO;QAClC,CAAC,gBAAgB,EAAE,CAAC;YAClB,SAAS,GAAG;YACZ,UAAU,OAAO,CAAC;YAClB,OAAO,CAAC,QAAQ,CAAC,KAAO;QAC1B;IACF;IAEA,MAAM,aAAiC;QACrC;YACE,OAAO;QACT;QACA,KAAI,CAAM;YACR,qCAAqC;YACrC,IAAI,MAAM,SAAS;gBACjB,OAAO,CAAC,iBAAiB,GAAG;YAC9B;QACF;IACF;IAEA,OAAO,cAAc,CAAC,QAAQ,WAAW;IACzC,OAAO,cAAc,CAAC,QAAQ,mBAAmB;IAEjD,SAAS,wBAAwB,IAAW;QAC1C,MAAM,cAAc,SAAS;QAE7B,MAAM,YAAY,IAChB,YAAY,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,eAAe;gBAC9C,OAAO,CAAC,CAAC,iBAAiB;YAC5B;QAEF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAE7B,MAAM,KAAmB,OAAO,MAAM,CAAC,IAAM,QAAQ,YAAY;YAC/D,YAAY;QACd;QAEA,SAAS,QAAQ,CAAa;YAC5B,IAAI,MAAM,SAAS,CAAC,UAAU,GAAG,CAAC,IAAI;gBACpC,UAAU,GAAG,CAAC;gBACd,IAAI,KAAK,EAAE,MAAM,QAA6B;oBAC5C,GAAG,UAAU;oBACb,EAAE,IAAI,CAAC;gBACT;YACF;QACF;QAEA,YAAY,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,gBAAgB,CAAC;QAE9C,OAAO,GAAG,UAAU,GAAG,UAAU;IACnC;IAEA,SAAS,YAAY,GAAS;QAC5B,IAAI,KAAK;YACP,OAAQ,OAAO,CAAC,eAAe,GAAG;QACpC,OAAO;YACL,QAAQ,OAAO,CAAC,iBAAiB;QACnC;QAEA,aAAa;IACf;IAEA,KAAK,yBAAyB;IAE9B,IAAI,SAAS,MAAM,MAAM,SAA0B;QACjD,MAAM,MAAM;IACd;AACF;AAEA;;;;;;;;;CASC,GACD,MAAM,cAAc,SAAS,YAAuB,QAAgB;IAClE,MAAM,UAAU,IAAI,IAAI,UAAU;IAClC,MAAM,SAA8B,CAAC;IACrC,IAAK,MAAM,OAAO,QAAS,MAAM,CAAC,IAAI,GAAG,AAAC,OAAe,CAAC,IAAI;IAC9D,OAAO,IAAI,GAAG;IACd,OAAO,QAAQ,GAAG,SAAS,OAAO,CAAC,UAAU;IAC7C,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG;IAClC,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,CAAC,GAAG,QAAsB;IAC5D,IAAK,MAAM,OAAO,OAChB,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;QAC/B,YAAY;QACZ,cAAc;QACd,OAAO,MAAM,CAAC,IAAI;IACpB;AACJ;AAEA,YAAY,SAAS,GAAG,IAAI,SAAS;AAErC;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD;AAEA;;CAEC,GACD,SAAS,YAAY,SAAmB;IACtC,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0]}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/base-externals-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../shared/runtime-utils.ts\" />\r\n\r\n/// A 'base' utilities to support runtime can have externals.\r\n/// Currently this is for node.js / edge runtime both.\r\n/// If a fn requires node.js specific behavior, it should be placed in `node-external-utils` instead.\r\n\r\nasync function externalImport(id: DependencySpecifier) {\r\n  let raw\r\n  try {\r\n    raw = await import(id)\r\n  } catch (err) {\r\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\r\n    // an external module we don't provide a shim for (e.g. querystring, url).\r\n    // For now, we fail semi-silently, but in the future this should be a\r\n    // compilation error.\r\n    throw new Error(`Failed to load external module ${id}: ${err}`)\r\n  }\r\n\r\n  if (raw && raw.__esModule && raw.default && 'default' in raw.default) {\r\n    return interopEsm(raw.default, createNS(raw), true)\r\n  }\r\n\r\n  return raw\r\n}\r\n\r\nfunction externalRequire(\r\n  id: ModuleId,\r\n  thunk: () => any,\r\n  esm: boolean = false\r\n): Exports | EsmNamespaceObject {\r\n  let raw\r\n  try {\r\n    raw = thunk()\r\n  } catch (err) {\r\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\r\n    // an external module we don't provide a shim for (e.g. querystring, url).\r\n    // For now, we fail semi-silently, but in the future this should be a\r\n    // compilation error.\r\n    throw new Error(`Failed to load external module ${id}: ${err}`)\r\n  }\r\n\r\n  if (!esm || raw.__esModule) {\r\n    return raw\r\n  }\r\n\r\n  return interopEsm(raw, createNS(raw), true)\r\n}\r\n\r\nexternalRequire.resolve = (\r\n  id: string,\r\n  options?: {\r\n    paths?: string[]\r\n  }\r\n) => {\r\n  return require.resolve(id, options)\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AAEnD,6DAA6D;AAC7D,sDAAsD;AACtD,qGAAqG;AAErG,eAAe,eAAe,EAAuB;IACnD,IAAI;IACJ,IAAI;QACF,MAAM,MAAM,MAAM,CAAC;IACrB,EAAE,OAAO,KAAK;QACZ,0EAA0E;QAC1E,0EAA0E;QAC1E,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,GAAG,EAAE,EAAE,KAAK;IAChE;IAEA,IAAI,OAAO,IAAI,UAAU,IAAI,IAAI,OAAO,IAAI,aAAa,IAAI,OAAO,EAAE;QACpE,OAAO,WAAW,IAAI,OAAO,EAAE,SAAS,MAAM;IAChD;IAEA,OAAO;AACT;AAEA,SAAS,gBACP,EAAY,EACZ,KAAgB,EAChB,MAAe,KAAK;IAEpB,IAAI;IACJ,IAAI;QACF,MAAM;IACR,EAAE,OAAO,KAAK;QACZ,0EAA0E;QAC1E,0EAA0E;QAC1E,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,GAAG,EAAE,EAAE,KAAK;IAChE;IAEA,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;QAC1B,OAAO;IACT;IAEA,OAAO,WAAW,KAAK,SAAS,MAAM;AACxC;AAEA,gBAAgB,OAAO,GAAG,CACxB,IACA;IAIA,OAAO,QAAQ,OAAO,CAAC,IAAI;AAC7B", "ignoreList": [0]}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/node-externals-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\ndeclare var RUNTIME_PUBLIC_PATH: string\r\ndeclare var RELATIVE_ROOT_PATH: string\r\ndeclare var ASSET_PREFIX: string\r\n\r\nconst path = require('path')\r\n\r\nconst relativePathToRuntimeRoot = path.relative(RUNTIME_PUBLIC_PATH, '.')\r\n// Compute the relative path to the `distDir`.\r\nconst relativePathToDistRoot = path.join(\r\n  relativePathToRuntimeRoot,\r\n  RELATIVE_ROOT_PATH\r\n)\r\nconst RUNTIME_ROOT = path.resolve(__filename, relativePathToRuntimeRoot)\r\n// Compute the absolute path to the root, by stripping distDir from the absolute path to this file.\r\nconst ABSOLUTE_ROOT = path.resolve(__filename, relativePathToDistRoot)\r\n\r\n/**\r\n * Returns an absolute path to the given module path.\r\n * Module path should be relative, either path to a file or a directory.\r\n *\r\n * This fn allows to calculate an absolute path for some global static values, such as\r\n * `__dirname` or `import.meta.url` that <PERSON><PERSON> will not embeds in compile time.\r\n * See ImportMetaBinding::code_generation for the usage.\r\n */\r\nfunction resolveAbsolutePath(modulePath?: string): string {\r\n  if (modulePath) {\r\n    return path.join(ABSOLUTE_ROOT, modulePath)\r\n  }\r\n  return ABSOLUTE_ROOT\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GAMpD,MAAM,OAAO,QAAQ;AAErB,MAAM,4BAA4B,KAAK,QAAQ,CAAC,qBAAqB;AACrE,8CAA8C;AAC9C,MAAM,yBAAyB,KAAK,IAAI,CACtC,2BACA;AAEF,MAAM,eAAe,KAAK,OAAO,CAAC,YAAY;AAC9C,mGAAmG;AACnG,MAAM,gBAAgB,KAAK,OAAO,CAAC,YAAY;AAE/C;;;;;;;CAOC,GACD,SAAS,oBAAoB,UAAmB;IAC9C,IAAI,YAAY;QACd,OAAO,KAAK,IAAI,CAAC,eAAe;IAClC;IACA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/node-wasm-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../shared/runtime-utils.ts\" />\r\n\r\nfunction readWebAssemblyAsResponse(path: string) {\r\n  const { createReadStream } = require('fs') as typeof import('fs')\r\n  const { Readable } = require('stream') as typeof import('stream')\r\n\r\n  const stream = createReadStream(path)\r\n\r\n  // @ts-ignore unfortunately there's a slight type mismatch with the stream.\r\n  return new Response(Readable.toWeb(stream), {\r\n    headers: {\r\n      'content-type': 'application/wasm',\r\n    },\r\n  })\r\n}\r\n\r\nasync function compileWebAssemblyFromPath(\r\n  path: string\r\n): Promise<WebAssembly.Module> {\r\n  const response = readWebAssemblyAsResponse(path)\r\n\r\n  return await WebAssembly.compileStreaming(response)\r\n}\r\n\r\nasync function instantiateWebAssemblyFromPath(\r\n  path: string,\r\n  importsObj: WebAssembly.Imports\r\n): Promise<Exports> {\r\n  const response = readWebAssemblyAsResponse(path)\r\n\r\n  const { instance } = await WebAssembly.instantiateStreaming(\r\n    response,\r\n    importsObj\r\n  )\r\n\r\n  return instance.exports\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AAEnD,SAAS,0BAA0B,IAAY;IAC7C,MAAM,EAAE,gBAAgB,EAAE,GAAG,QAAQ;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ;IAE7B,MAAM,SAAS,iBAAiB;IAEhC,2EAA2E;IAC3E,OAAO,IAAI,SAAS,SAAS,KAAK,CAAC,SAAS;QAC1C,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAEA,eAAe,2BACb,IAAY;IAEZ,MAAM,WAAW,0BAA0B;IAE3C,OAAO,MAAM,YAAY,gBAAgB,CAAC;AAC5C;AAEA,eAAe,+BACb,IAAY,EACZ,UAA+B;IAE/B,MAAM,WAAW,0BAA0B;IAE3C,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,YAAY,oBAAoB,CACzD,UACA;IAGF,OAAO,SAAS,OAAO;AACzB", "ignoreList": [0]}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/nodejs/runtime.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\r\n\r\n/// <reference path=\"../shared/runtime-utils.ts\" />\r\n/// <reference path=\"../shared-node/base-externals-utils.ts\" />\r\n/// <reference path=\"../shared-node/node-externals-utils.ts\" />\r\n/// <reference path=\"../shared-node/node-wasm-utils.ts\" />\r\n\r\nenum SourceType {\r\n  /**\r\n   * The module was instantiated because it was included in an evaluated chunk's\r\n   * runtime.\r\n   */\r\n  Runtime = 0,\r\n  /**\r\n   * The module was instantiated because a parent module imported it.\r\n   */\r\n  Parent = 1,\r\n}\r\n\r\ntype SourceInfo =\r\n  | {\r\n      type: SourceType.Runtime\r\n      chunkPath: ChunkPath\r\n    }\r\n  | {\r\n      type: SourceType.Parent\r\n      parentId: ModuleId\r\n    }\r\n\r\nprocess.env.TURBOPACK = '1'\r\n\r\nfunction stringifySourceInfo(source: SourceInfo): string {\r\n  switch (source.type) {\r\n    case SourceType.Runtime:\r\n      return `runtime for chunk ${source.chunkPath}`\r\n    case SourceType.Parent:\r\n      return `parent module ${source.parentId}`\r\n    default:\r\n      invariant(source, (source) => `Unknown source type: ${source?.type}`)\r\n  }\r\n}\r\n\r\ntype ExternalRequire = (\r\n  id: ModuleId,\r\n  thunk: () => any,\r\n  esm?: boolean\r\n) => Exports | EsmNamespaceObject\r\ntype ExternalImport = (id: ModuleId) => Promise<Exports | EsmNamespaceObject>\r\n\r\ninterface TurbopackNodeBuildContext extends TurbopackBaseContext<Module> {\r\n  R: ResolvePathFromModule\r\n  x: ExternalRequire\r\n  y: ExternalImport\r\n}\r\n\r\ntype ModuleFactory = (\r\n  this: Module['exports'],\r\n  context: TurbopackNodeBuildContext\r\n) => unknown\r\n\r\nconst url = require('url') as typeof import('url')\r\nconst fs = require('fs/promises') as typeof import('fs/promises')\r\n\r\nconst moduleFactories: ModuleFactories = Object.create(null)\r\nconst moduleCache: ModuleCache<Module> = Object.create(null)\r\n\r\n/**\r\n * Returns an absolute path to the given module's id.\r\n */\r\nfunction createResolvePathFromModule(\r\n  resolver: (moduleId: string) => Exports\r\n): (moduleId: string) => string {\r\n  return function resolvePathFromModule(moduleId: string): string {\r\n    const exported = resolver(moduleId)\r\n    const exportedPath = exported?.default ?? exported\r\n    if (typeof exportedPath !== 'string') {\r\n      return exported as any\r\n    }\r\n\r\n    const strippedAssetPrefix = exportedPath.slice(ASSET_PREFIX.length)\r\n    const resolved = path.resolve(RUNTIME_ROOT, strippedAssetPrefix)\r\n\r\n    return url.pathToFileURL(resolved).href\r\n  }\r\n}\r\n\r\nfunction loadChunk(chunkData: ChunkData, source?: SourceInfo): void {\r\n  if (typeof chunkData === 'string') {\r\n    return loadChunkPath(chunkData, source)\r\n  } else {\r\n    return loadChunkPath(chunkData.path, source)\r\n  }\r\n}\r\n\r\nconst loadedChunks = new Set<ChunkPath>()\r\n\r\nfunction loadChunkPath(chunkPath: ChunkPath, source?: SourceInfo): void {\r\n  if (!isJs(chunkPath)) {\r\n    // We only support loading JS chunks in Node.js.\r\n    // This branch can be hit when trying to load a CSS chunk.\r\n    return\r\n  }\r\n\r\n  if (loadedChunks.has(chunkPath)) {\r\n    return\r\n  }\r\n\r\n  try {\r\n    const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\r\n    const chunkModules: CompressedModuleFactories = require(resolved)\r\n\r\n    for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\r\n      if (!moduleFactories[moduleId]) {\r\n        if (Array.isArray(moduleFactory)) {\r\n          let [moduleFactoryFn, otherIds] = moduleFactory\r\n          moduleFactories[moduleId] = moduleFactoryFn\r\n          for (const otherModuleId of otherIds) {\r\n            moduleFactories[otherModuleId] = moduleFactoryFn\r\n          }\r\n        } else {\r\n          moduleFactories[moduleId] = moduleFactory\r\n        }\r\n      }\r\n    }\r\n    loadedChunks.add(chunkPath)\r\n  } catch (e) {\r\n    let errorMessage = `Failed to load chunk ${chunkPath}`\r\n\r\n    if (source) {\r\n      errorMessage += ` from ${stringifySourceInfo(source)}`\r\n    }\r\n\r\n    throw new Error(errorMessage, {\r\n      cause: e,\r\n    })\r\n  }\r\n}\r\n\r\nasync function loadChunkAsync(\r\n  source: SourceInfo,\r\n  chunkData: ChunkData\r\n): Promise<any> {\r\n  const chunkPath = typeof chunkData === 'string' ? chunkData : chunkData.path\r\n  if (!isJs(chunkPath)) {\r\n    // We only support loading JS chunks in Node.js.\r\n    // This branch can be hit when trying to load a CSS chunk.\r\n    return\r\n  }\r\n\r\n  if (loadedChunks.has(chunkPath)) {\r\n    return\r\n  }\r\n\r\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\r\n\r\n  try {\r\n    const contents = await fs.readFile(resolved, 'utf-8')\r\n\r\n    const localRequire = (id: string) => {\r\n      let resolvedId = require.resolve(id, { paths: [path.dirname(resolved)] })\r\n      return require(resolvedId)\r\n    }\r\n    const module = {\r\n      exports: {},\r\n    }\r\n    // TODO: Use vm.runInThisContext once our minimal supported Node.js version includes https://github.com/nodejs/node/pull/52153\r\n    // eslint-disable-next-line no-eval -- Can't use vm.runInThisContext due to https://github.com/nodejs/node/issues/52102\r\n    ;(0, eval)(\r\n      '(function(module, exports, require, __dirname, __filename) {' +\r\n        contents +\r\n        '\\n})' +\r\n        '\\n//# sourceURL=' +\r\n        url.pathToFileURL(resolved)\r\n    )(module, module.exports, localRequire, path.dirname(resolved), resolved)\r\n\r\n    const chunkModules: CompressedModuleFactories = module.exports\r\n    for (const [moduleId, moduleFactory] of Object.entries(chunkModules)) {\r\n      if (!moduleFactories[moduleId]) {\r\n        if (Array.isArray(moduleFactory)) {\r\n          let [moduleFactoryFn, otherIds] = moduleFactory\r\n          moduleFactories[moduleId] = moduleFactoryFn\r\n          for (const otherModuleId of otherIds) {\r\n            moduleFactories[otherModuleId] = moduleFactoryFn\r\n          }\r\n        } else {\r\n          moduleFactories[moduleId] = moduleFactory\r\n        }\r\n      }\r\n    }\r\n    loadedChunks.add(chunkPath)\r\n  } catch (e) {\r\n    let errorMessage = `Failed to load chunk ${chunkPath}`\r\n\r\n    if (source) {\r\n      errorMessage += ` from ${stringifySourceInfo(source)}`\r\n    }\r\n\r\n    throw new Error(errorMessage, {\r\n      cause: e,\r\n    })\r\n  }\r\n}\r\n\r\nasync function loadChunkAsyncByUrl(source: SourceInfo, chunkUrl: string) {\r\n  const path = url.fileURLToPath(new URL(chunkUrl, RUNTIME_ROOT)) as ChunkPath\r\n  return loadChunkAsync(source, path)\r\n}\r\n\r\nfunction loadWebAssembly(\r\n  chunkPath: ChunkPath,\r\n  _edgeModule: () => WebAssembly.Module,\r\n  imports: WebAssembly.Imports\r\n) {\r\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\r\n\r\n  return instantiateWebAssemblyFromPath(resolved, imports)\r\n}\r\n\r\nfunction loadWebAssemblyModule(\r\n  chunkPath: ChunkPath,\r\n  _edgeModule: () => WebAssembly.Module\r\n) {\r\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\r\n\r\n  return compileWebAssemblyFromPath(resolved)\r\n}\r\n\r\nfunction getWorkerBlobURL(_chunks: ChunkPath[]): string {\r\n  throw new Error('Worker blobs are not implemented yet for Node.js')\r\n}\r\n\r\nfunction instantiateModule(id: ModuleId, source: SourceInfo): Module {\r\n  const moduleFactory = moduleFactories[id]\r\n  if (typeof moduleFactory !== 'function') {\r\n    // This can happen if modules incorrectly handle HMR disposes/updates,\r\n    // e.g. when they keep a `setTimeout` around which still executes old code\r\n    // and contains e.g. a `require(\"something\")` call.\r\n    let instantiationReason\r\n    switch (source.type) {\r\n      case SourceType.Runtime:\r\n        instantiationReason = `as a runtime entry of chunk ${source.chunkPath}`\r\n        break\r\n      case SourceType.Parent:\r\n        instantiationReason = `because it was required from module ${source.parentId}`\r\n        break\r\n      default:\r\n        invariant(source, (source) => `Unknown source type: ${source?.type}`)\r\n    }\r\n    throw new Error(\r\n      `Module ${id} was instantiated ${instantiationReason}, but the module factory is not available. It might have been deleted in an HMR update.`\r\n    )\r\n  }\r\n\r\n  const module: Module = {\r\n    exports: {},\r\n    error: undefined,\r\n    loaded: false,\r\n    id,\r\n    namespaceObject: undefined,\r\n  }\r\n  moduleCache[id] = module\r\n\r\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\r\n  try {\r\n    const r = commonJsRequire.bind(null, module)\r\n    moduleFactory.call(module.exports, {\r\n      a: asyncModule.bind(null, module),\r\n      e: module.exports,\r\n      r,\r\n      t: runtimeRequire,\r\n      x: externalRequire,\r\n      y: externalImport,\r\n      f: moduleContext,\r\n      i: esmImport.bind(null, module),\r\n      s: esmExport.bind(null, module, module.exports, moduleCache),\r\n      j: dynamicExport.bind(null, module, module.exports, moduleCache),\r\n      v: exportValue.bind(null, module, moduleCache),\r\n      n: exportNamespace.bind(null, module, moduleCache),\r\n      m: module,\r\n      c: moduleCache,\r\n      M: moduleFactories,\r\n      l: loadChunkAsync.bind(null, { type: SourceType.Parent, parentId: id }),\r\n      L: loadChunkAsyncByUrl.bind(null, {\r\n        type: SourceType.Parent,\r\n        parentId: id,\r\n      }),\r\n      w: loadWebAssembly,\r\n      u: loadWebAssemblyModule,\r\n      P: resolveAbsolutePath,\r\n      U: relativeURL,\r\n      R: createResolvePathFromModule(r),\r\n      b: getWorkerBlobURL,\r\n      z: requireStub,\r\n    })\r\n  } catch (error) {\r\n    module.error = error as any\r\n    throw error\r\n  }\r\n\r\n  module.loaded = true\r\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\r\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\r\n    interopEsm(module.exports, module.namespaceObject)\r\n  }\r\n\r\n  return module\r\n}\r\n\r\n/**\r\n * Retrieves a module from the cache, or instantiate it if it is not cached.\r\n */\r\n// @ts-ignore\r\nfunction getOrInstantiateModuleFromParent(\r\n  id: ModuleId,\r\n  sourceModule: Module\r\n): Module {\r\n  const module = moduleCache[id]\r\n\r\n  if (module) {\r\n    return module\r\n  }\r\n\r\n  return instantiateModule(id, {\r\n    type: SourceType.Parent,\r\n    parentId: sourceModule.id,\r\n  })\r\n}\r\n\r\n/**\r\n * Instantiates a runtime module.\r\n */\r\nfunction instantiateRuntimeModule(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath\r\n): Module {\r\n  return instantiateModule(moduleId, { type: SourceType.Runtime, chunkPath })\r\n}\r\n\r\n/**\r\n * Retrieves a module from the cache, or instantiate it as a runtime module if it is not cached.\r\n */\r\n// @ts-ignore TypeScript doesn't separate this module space from the browser runtime\r\nfunction getOrInstantiateRuntimeModule(\r\n  moduleId: ModuleId,\r\n  chunkPath: ChunkPath\r\n): Module {\r\n  const module = moduleCache[moduleId]\r\n  if (module) {\r\n    if (module.error) {\r\n      throw module.error\r\n    }\r\n    return module\r\n  }\r\n\r\n  return instantiateRuntimeModule(moduleId, chunkPath)\r\n}\r\n\r\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/\r\n/**\r\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\r\n */\r\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\r\n  return regexJsUrl.test(chunkUrlOrPath)\r\n}\r\n\r\nmodule.exports = {\r\n  getOrInstantiateRuntimeModule,\r\n  loadChunk,\r\n}\r\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AACnD,+DAA+D;AAC/D,+DAA+D;AAC/D,0DAA0D;AAE1D,IAAA,AAAK,oCAAA;IACH;;;GAGC;IAED;;GAEC;WARE;EAAA;AAsBL,QAAQ,GAAG,CAAC,SAAS,GAAG;AAExB,SAAS,oBAAoB,MAAkB;IAC7C,OAAQ,OAAO,IAAI;QACjB;YACE,OAAO,CAAC,kBAAkB,EAAE,OAAO,SAAS,EAAE;QAChD;YACE,OAAO,CAAC,cAAc,EAAE,OAAO,QAAQ,EAAE;QAC3C;YACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;IACxE;AACF;AAoBA,MAAM,MAAM,QAAQ;AACpB,MAAM,KAAK,QAAQ;AAEnB,MAAM,kBAAmC,OAAO,MAAM,CAAC;AACvD,MAAM,cAAmC,OAAO,MAAM,CAAC;AAEvD;;CAEC,GACD,SAAS,4BACP,QAAuC;IAEvC,OAAO,SAAS,sBAAsB,QAAgB;QACpD,MAAM,WAAW,SAAS;QAC1B,MAAM,eAAe,UAAU,WAAW;QAC1C,IAAI,OAAO,iBAAiB,UAAU;YACpC,OAAO;QACT;QAEA,MAAM,sBAAsB,aAAa,KAAK,CAAC,aAAa,MAAM;QAClE,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;QAE5C,OAAO,IAAI,aAAa,CAAC,UAAU,IAAI;IACzC;AACF;AAEA,SAAS,UAAU,SAAoB,EAAE,MAAmB;IAC1D,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,cAAc,WAAW;IAClC,OAAO;QACL,OAAO,cAAc,UAAU,IAAI,EAAE;IACvC;AACF;AAEA,MAAM,eAAe,IAAI;AAEzB,SAAS,cAAc,SAAoB,EAAE,MAAmB;IAC9D,IAAI,CAAC,KAAK,YAAY;QACpB,gDAAgD;QAChD,0DAA0D;QAC1D;IACF;IAEA,IAAI,aAAa,GAAG,CAAC,YAAY;QAC/B;IACF;IAEA,IAAI;QACF,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;QAC5C,MAAM,eAA0C,QAAQ;QAExD,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,cAAe;YACpE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;gBAC9B,IAAI,MAAM,OAAO,CAAC,gBAAgB;oBAChC,IAAI,CAAC,iBAAiB,SAAS,GAAG;oBAClC,eAAe,CAAC,SAAS,GAAG;oBAC5B,KAAK,MAAM,iBAAiB,SAAU;wBACpC,eAAe,CAAC,cAAc,GAAG;oBACnC;gBACF,OAAO;oBACL,eAAe,CAAC,SAAS,GAAG;gBAC9B;YACF;QACF;QACA,aAAa,GAAG,CAAC;IACnB,EAAE,OAAO,GAAG;QACV,IAAI,eAAe,CAAC,qBAAqB,EAAE,WAAW;QAEtD,IAAI,QAAQ;YACV,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,SAAS;QACxD;QAEA,MAAM,IAAI,MAAM,cAAc;YAC5B,OAAO;QACT;IACF;AACF;AAEA,eAAe,eACb,MAAkB,EAClB,SAAoB;IAEpB,MAAM,YAAY,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;IAC5E,IAAI,CAAC,KAAK,YAAY;QACpB,gDAAgD;QAChD,0DAA0D;QAC1D;IACF;IAEA,IAAI,aAAa,GAAG,CAAC,YAAY;QAC/B;IACF;IAEA,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,IAAI;QACF,MAAM,WAAW,MAAM,GAAG,QAAQ,CAAC,UAAU;QAE7C,MAAM,eAAe,CAAC;YACpB,IAAI,aAAa,QAAQ,OAAO,CAAC,IAAI;gBAAE,OAAO;oBAAC,KAAK,OAAO,CAAC;iBAAU;YAAC;YACvE,OAAO,QAAQ;QACjB;QACA,MAAM,UAAS;YACb,SAAS,CAAC;QACZ;QAGC,CAAC,GAAG,IAAI,EACP,iEACE,WACA,SACA,qBACA,IAAI,aAAa,CAAC,WACpB,SAAQ,QAAO,OAAO,EAAE,cAAc,KAAK,OAAO,CAAC,WAAW;QAEhE,MAAM,eAA0C,QAAO,OAAO;QAC9D,KAAK,MAAM,CAAC,UAAU,cAAc,IAAI,OAAO,OAAO,CAAC,cAAe;YACpE,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE;gBAC9B,IAAI,MAAM,OAAO,CAAC,gBAAgB;oBAChC,IAAI,CAAC,iBAAiB,SAAS,GAAG;oBAClC,eAAe,CAAC,SAAS,GAAG;oBAC5B,KAAK,MAAM,iBAAiB,SAAU;wBACpC,eAAe,CAAC,cAAc,GAAG;oBACnC;gBACF,OAAO;oBACL,eAAe,CAAC,SAAS,GAAG;gBAC9B;YACF;QACF;QACA,aAAa,GAAG,CAAC;IACnB,EAAE,OAAO,GAAG;QACV,IAAI,eAAe,CAAC,qBAAqB,EAAE,WAAW;QAEtD,IAAI,QAAQ;YACV,gBAAgB,CAAC,MAAM,EAAE,oBAAoB,SAAS;QACxD;QAEA,MAAM,IAAI,MAAM,cAAc;YAC5B,OAAO;QACT;IACF;AACF;AAEA,eAAe,oBAAoB,MAAkB,EAAE,QAAgB;IACrE,MAAM,QAAO,IAAI,aAAa,CAAC,IAAI,IAAI,UAAU;IACjD,OAAO,eAAe,QAAQ;AAChC;AAEA,SAAS,gBACP,SAAoB,EACpB,WAAqC,EACrC,OAA4B;IAE5B,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,+BAA+B,UAAU;AAClD;AAEA,SAAS,sBACP,SAAoB,EACpB,WAAqC;IAErC,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,2BAA2B;AACpC;AAEA,SAAS,iBAAiB,OAAoB;IAC5C,MAAM,IAAI,MAAM;AAClB;AAEA,SAAS,kBAAkB,EAAY,EAAE,MAAkB;IACzD,MAAM,gBAAgB,eAAe,CAAC,GAAG;IACzC,IAAI,OAAO,kBAAkB,YAAY;QACvC,sEAAsE;QACtE,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI;QACJ,OAAQ,OAAO,IAAI;YACjB;gBACE,sBAAsB,CAAC,4BAA4B,EAAE,OAAO,SAAS,EAAE;gBACvE;YACF;gBACE,sBAAsB,CAAC,oCAAoC,EAAE,OAAO,QAAQ,EAAE;gBAC9E;YACF;gBACE,UAAU,QAAQ,CAAC,SAAW,CAAC,qBAAqB,EAAE,QAAQ,MAAM;QACxE;QACA,MAAM,IAAI,MACR,CAAC,OAAO,EAAE,GAAG,kBAAkB,EAAE,oBAAoB,uFAAuF,CAAC;IAEjJ;IAEA,MAAM,UAAiB;QACrB,SAAS,CAAC;QACV,OAAO;QACP,QAAQ;QACR;QACA,iBAAiB;IACnB;IACA,WAAW,CAAC,GAAG,GAAG;IAElB,4EAA4E;IAC5E,IAAI;QACF,MAAM,IAAI,gBAAgB,IAAI,CAAC,MAAM;QACrC,cAAc,IAAI,CAAC,QAAO,OAAO,EAAE;YACjC,GAAG,YAAY,IAAI,CAAC,MAAM;YAC1B,GAAG,QAAO,OAAO;YACjB;YACA,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,UAAU,IAAI,CAAC,MAAM;YACxB,GAAG,UAAU,IAAI,CAAC,MAAM,SAAQ,QAAO,OAAO,EAAE;YAChD,GAAG,cAAc,IAAI,CAAC,MAAM,SAAQ,QAAO,OAAO,EAAE;YACpD,GAAG,YAAY,IAAI,CAAC,MAAM,SAAQ;YAClC,GAAG,gBAAgB,IAAI,CAAC,MAAM,SAAQ;YACtC,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,eAAe,IAAI,CAAC,MAAM;gBAAE,IAAI;gBAAqB,UAAU;YAAG;YACrE,GAAG,oBAAoB,IAAI,CAAC,MAAM;gBAChC,IAAI;gBACJ,UAAU;YACZ;YACA,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,4BAA4B;YAC/B,GAAG;YACH,GAAG;QACL;IACF,EAAE,OAAO,OAAO;QACd,QAAO,KAAK,GAAG;QACf,MAAM;IACR;IAEA,QAAO,MAAM,GAAG;IAChB,IAAI,QAAO,eAAe,IAAI,QAAO,OAAO,KAAK,QAAO,eAAe,EAAE;QACvE,yDAAyD;QACzD,WAAW,QAAO,OAAO,EAAE,QAAO,eAAe;IACnD;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,aAAa;AACb,SAAS,iCACP,EAAY,EACZ,YAAoB;IAEpB,MAAM,UAAS,WAAW,CAAC,GAAG;IAE9B,IAAI,SAAQ;QACV,OAAO;IACT;IAEA,OAAO,kBAAkB,IAAI;QAC3B,IAAI;QACJ,UAAU,aAAa,EAAE;IAC3B;AACF;AAEA;;CAEC,GACD,SAAS,yBACP,QAAkB,EAClB,SAAoB;IAEpB,OAAO,kBAAkB,UAAU;QAAE,IAAI;QAAsB;IAAU;AAC3E;AAEA;;CAEC,GACD,oFAAoF;AACpF,SAAS,8BACP,QAAkB,EAClB,SAAoB;IAEpB,MAAM,UAAS,WAAW,CAAC,SAAS;IACpC,IAAI,SAAQ;QACV,IAAI,QAAO,KAAK,EAAE;YAChB,MAAM,QAAO,KAAK;QACpB;QACA,OAAO;IACT;IAEA,OAAO,yBAAyB,UAAU;AAC5C;AAEA,MAAM,aAAa;AACnB;;CAEC,GACD,SAAS,KAAK,cAAoC;IAChD,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0]}}]}