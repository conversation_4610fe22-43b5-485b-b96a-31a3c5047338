(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[283,734],{248:(e,t,s)=>{Promise.resolve().then(s.bind(s,1984))},283:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,AuthProvider:()=>i});var a=s(5155),r=s(2115),o=s(5731);let n=(0,r.createContext)(void 0),i=e=>{let{children:t}=e,[s,i]=(0,r.useState)(null),[l,c]=(0,r.useState)(null),[d,u]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{let e=localStorage.getItem("token"),t=localStorage.getItem("user");if(e&&t)try{c(e),i(JSON.parse(t));let s=await o.R2.getProfile();i(s.user)}catch(e){console.error("Token validation failed:",e),localStorage.removeItem("token"),localStorage.removeItem("user"),c(null),i(null)}u(!1)})()},[]);let h=async(e,t)=>{try{let s=(await o.R2.login({phone:e,password:t})).user,a=s.token,r={user_id:s.user_id,phone:s.phone,name:s.name,email:s.email};i(r),c(a),localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(r))}catch(e){throw Error(e.message||"Login failed")}},m=async e=>{try{return await o.R2.register(e)}catch(e){throw Error(e.message||"Registration failed")}},p=async(e,t)=>{try{let s=(await o.R2.verifyOTP({phone:e,code:t})).user,a=s.token,r={user_id:s.user_id,phone:s.phone,name:s.name,email:s.email};i(r),c(a),localStorage.setItem("token",a),localStorage.setItem("user",JSON.stringify(r))}catch(e){throw Error(e.message||"OTP verification failed")}},g=async()=>{try{l&&await o.R2.logout()}catch(e){console.error("Logout API call failed:",e)}finally{i(null),c(null),localStorage.removeItem("token"),localStorage.removeItem("user")}},x=async e=>{try{return await o.R2.resendOTP(e)}catch(e){throw Error(e.message||"Failed to resend OTP")}},f=async e=>{try{return await o.R2.forgotPassword(e)}catch(e){throw Error(e.message||"Failed to request password reset")}},b=async(e,t,s)=>{try{await o.R2.resetPassword(e,t,s)}catch(e){throw Error(e.message||"Password reset failed")}};return(0,a.jsx)(n.Provider,{value:{user:s,token:l,isLoading:d,isAuthenticated:!!s&&!!l,login:h,register:m,verifyOTP:p,logout:g,resendOTP:x,forgotPassword:f,resetPassword:b},children:t})},l=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1984:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(5155),r=s(2115),o=s(5695),n=s(6874),i=s.n(n),l=s(283),c=s(9420),d=s(3904),u=s(2138);let h=()=>{let[e,t]=(0,r.useState)({phone:"",code:""}),[s,n]=(0,r.useState)(!1),[h,m]=(0,r.useState)(!1),[p,g]=(0,r.useState)(""),[x,f]=(0,r.useState)(""),[b,y]=(0,r.useState)(0),{verifyOTP:v,resendOTP:P}=(0,l.A)(),w=(0,o.useRouter)(),S=(0,o.useSearchParams)();(0,r.useEffect)(()=>{let e=S.get("phone");e&&t(t=>({...t,phone:e}))},[S]),(0,r.useEffect)(()=>{if(b>0){let e=setTimeout(()=>{y(b-1)},1e3);return()=>clearTimeout(e)}},[b]);let T=e=>{let{name:s,value:a}=e.target;if("code"===s){let e=a.replace(/\D/g,"").slice(0,6);t(t=>({...t,[s]:e}))}else t(e=>({...e,[s]:a}));p&&g(""),x&&f("")},j=async t=>{if(t.preventDefault(),e.phone.trim()?e.code.trim()?6===e.code.length||(g("OTP code must be 6 digits"),!1):(g("OTP code is required"),!1):(g("Phone number is required"),!1)){n(!0),g("");try{await v(e.phone.trim(),e.code),f("Phone number verified successfully! Redirecting..."),setTimeout(()=>{w.push("/dashboard")},2e3)}catch(e){g(e.message||"OTP verification failed. Please try again.")}finally{n(!1)}}},A=async()=>{if(!e.phone.trim())return void g("Please enter your phone number first");if(!(b>0)){m(!0),g("");try{await P(e.phone.trim()),f("OTP has been resent successfully!"),y(60),t(e=>({...e,code:""}))}catch(e){g(e.message||"Failed to resend OTP. Please try again.")}finally{m(!1)}}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-blue-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Verify Your Phone"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Enter the 6-digit code sent to your phone"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:p})}),x&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-green-600 text-sm",children:x})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:T,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"+6281234567890",disabled:s||h})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 mb-2",children:"OTP Code"}),(0,a.jsx)("input",{type:"text",id:"code",name:"code",value:e.code,onChange:T,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-center text-2xl font-mono tracking-widest",placeholder:"000000",maxLength:6,disabled:s||h}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1 text-center",children:"Enter the 6-digit code from WhatsApp"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"Didn't receive the code?"}),(0,a.jsx)("button",{type:"button",onClick:A,disabled:h||b>0||s,className:"inline-flex items-center text-sm text-blue-600 hover:text-blue-700 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors",children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):b>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-1"}),"Resend in ",b,"s"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-1"}),"Resend OTP"]})})]}),(0,a.jsx)("button",{type:"submit",disabled:s||h||6!==e.code.length,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:s?(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,a.jsxs)(a.Fragment,{children:["Verify Phone Number",(0,a.jsx)(u.A,{className:"ml-2 w-4 h-4"})]})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Need help?"," ",(0,a.jsx)(i(),{href:"/login",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Back to Login"})]})}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"How to get OTP:"}),(0,a.jsxs)("ol",{className:"text-xs text-blue-700 space-y-1 list-decimal list-inside",children:[(0,a.jsx)("li",{children:"Click the WhatsApp link from registration"}),(0,a.jsx)("li",{children:"Send the message to our admin"}),(0,a.jsx)("li",{children:"Enter the 6-digit code you sent here"})]})]})]})]})})};function m(){return(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(h,{})})}},2138:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},5731:(e,t,s)=>{"use strict";s.d(t,{FH:()=>c,H2:()=>l,R2:()=>d,SP:()=>n});var a=s(3464),r=s(9509);let o=r.env.NEXT_PUBLIC_API_URL||"http://localhost:3001",n=a.A.create({baseURL:o,headers:{"Content-Type":"application/json"},timeout:3e4});function i(){return localStorage.getItem("token")}async function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:s="GET",body:a,headers:r={},skipAuth:o=!1}=t,i={method:s.toLowerCase(),url:e,headers:{...r},metadata:{skipAuth:o}};a&&"GET"!==s&&(i.data=a);try{return(await n(i)).data}catch(e){throw e}}n.interceptors.request.use(e=>{var t,s;let a=i();return console.log("\uD83D\uDD0D API Call Debug:",{endpoint:e.url,fullUrl:"".concat(o).concat(e.url),API_URL:o,hasToken:!!a,tokenPreview:a?a.substring(0,3)+"***":"none",method:null==(t=e.method)?void 0:t.toUpperCase(),data:e.data,"process.env.NEXT_PUBLIC_API_URL":r.env.NEXT_PUBLIC_API_URL}),!a||(null==(s=e.metadata)?void 0:s.skipAuth)||(e.headers.Authorization="Bearer ".concat(a)),e},e=>(console.error("\uD83D\uDCA5 Request interceptor error:",e),Promise.reject(e))),n.interceptors.response.use(e=>(console.log("\uD83D\uDCE1 Response status:",e.status,e.statusText),console.log("✅ API Success:",e.data),e),e=>{var t,s,a,r;throw console.error("❌ API Error:",(null==(t=e.response)?void 0:t.data)||e.message),console.error("\uD83D\uDCA5 API call failed:",e),Error((null==(a=e.response)||null==(s=a.data)?void 0:s.message)||e.message||"HTTP error! status: ".concat(null==(r=e.response)?void 0:r.status))});let c={get:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"GET"})},post:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"POST"})},put:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"PUT"})},delete:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return l(e,{...t,method:"DELETE"})},health:()=>l("/health"),chat:(e,t,s,a)=>l("/api/chat",{method:"POST",body:{message:e,threadId:t,expertId:s,expertContext:a}}),getThreadMessages:e=>l("/api/thread/".concat(e,"/messages")),getSessionMessages:(e,t)=>l("/api/chat/sessions/".concat(e,"/messages").concat(t?"?limit=".concat(t):"")),getUserChatSessions:e=>l("/api/chat/sessions".concat(e?"?limit=".concat(e):"")),getUserStats:()=>l("/api/chat/stats"),getActiveSessionForExpert:e=>l("/api/chat/sessions/expert/".concat(e)),updateSessionTitle:(e,t)=>l("/api/chat/sessions/".concat(e,"/title"),{method:"PUT",body:{title:t}}),deleteSession:e=>l("/api/chat/sessions/".concat(e),{method:"DELETE"}),createThread:()=>l("/assistant/thread",{method:"POST"}),sendMessage:(e,t)=>l("/assistant/message",{method:"POST",body:{threadId:e,message:t}}),runAssistant:e=>l("/assistant/run",{method:"POST",body:{threadId:e}}),getMessages:e=>l("/assistant/messages/".concat(e)),createExpert:async e=>{let t=i();try{return(await a.A.post("".concat(o,"/api/experts"),e,{headers:{"Content-Type":"multipart/form-data",...t?{Authorization:"Bearer ".concat(t)}:{}}})).data}catch(e){var s,r,n;throw Error((null==(r=e.response)||null==(s=r.data)?void 0:s.message)||e.message||"HTTP error! status: ".concat(null==(n=e.response)?void 0:n.status))}},listExperts:()=>l("/api/experts"),getPublicExperts:()=>l("/api/experts/public",{skipAuth:!0}),getExpert:e=>l("/api/experts/".concat(e)),updateExpert:async(e,t,s,r)=>{let n=i(),l=new FormData;Object.keys(t).forEach(e=>{void 0!==t[e]&&null!==t[e]&&("labels"===e&&Array.isArray(t[e])?l.append(e,JSON.stringify(t[e])):l.append(e,t[e].toString()))}),s&&l.append("file",s),r&&l.append("image",r);try{return(await a.A.put("".concat(o,"/api/experts/").concat(e),l,{headers:{"Content-Type":"multipart/form-data",...n?{Authorization:"Bearer ".concat(n)}:{}}})).data}catch(e){var c,d,u;throw Error((null==(d=e.response)||null==(c=d.data)?void 0:c.message)||e.message||"HTTP error! status: ".concat(null==(u=e.response)?void 0:u.status))}},getAvailableModels:()=>l("/api/models"),getModelPricing:e=>l("/api/models/".concat(e,"/pricing")),calculateCost:(e,t,s,a)=>l("/api/calculate-cost",{method:"POST",body:{model:e,inputTokens:t,outputTokens:s,pricingPercentage:a}}),getExpertStats:e=>l("/api/experts/".concat(e,"/stats")),createReview:e=>l("/api/reviews",{method:"POST",body:e}),updateReview:(e,t)=>l("/api/reviews/".concat(e),{method:"PUT",body:t}),getExpertReviews:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return l("/api/reviews/expert/".concat(e,"?page=").concat(t,"&limit=").concat(s),{skipAuth:!0})},getUserReviews:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return l("/api/reviews/my?page=".concat(e,"&limit=").concat(t))},getReview:e=>l("/api/reviews/".concat(e),{skipAuth:!0}),canUserReview:e=>l("/api/reviews/expert/".concat(e,"/can-review")),getExpertRatingStats:e=>l("/api/reviews/expert/".concat(e,"/stats"),{skipAuth:!0}),getFilteredExperts:e=>{let t=new URLSearchParams;return e.filter&&t.append("filter",e.filter),e.timeline&&t.append("timeline",e.timeline),e.search&&t.append("search",e.search),e.page&&t.append("page",e.page.toString()),e.limit&&t.append("limit",e.limit.toString()),l("/api/experts/filtered?".concat(t.toString()),{skipAuth:!0})}},d={register:e=>l("/api/users/register",{method:"POST",body:e,skipAuth:!0}),verifyOTP:e=>l("/api/users/verify-otp",{method:"POST",body:e,skipAuth:!0}),login:e=>l("/api/users/login",{method:"POST",body:e,skipAuth:!0}),getProfile:()=>l("/api/users/profile"),updateProfile:e=>l("/api/users/profile",{method:"PUT",body:e}),changePassword:e=>l("/api/users/change-password",{method:"POST",body:e}),resendOTP:e=>l("/api/users/resend-otp",{method:"POST",body:{phone:e},skipAuth:!0}),forgotPassword:e=>l("/api/users/forgot-password",{method:"POST",body:{phone:e},skipAuth:!0}),resetPassword:(e,t,s)=>l("/api/users/reset-password",{method:"POST",body:{phone:e,code:t,newPassword:s},skipAuth:!0}),logout:()=>l("/api/users/logout",{method:"POST"}),getBalanceSummary:()=>l("/api/balance/summary"),getPointTransactions:e=>l("/api/balance/transactions/points".concat(e?"?limit=".concat(e):"")),getCreditTransactions:e=>l("/api/balance/transactions/credits".concat(e?"?limit=".concat(e):"")),checkAffordability:e=>l("/api/balance/can-afford",{method:"POST",body:{amount:e}}),addPoints:(e,t)=>l("/api/balance/points/add",{method:"POST",body:{amount:e,description:t}}),addCredits:(e,t)=>l("/api/balance/credits/add",{method:"POST",body:{amount:e,description:t}})}},9420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}},e=>{e.O(0,[445,874,441,964,358],()=>e(e.s=248)),_N_E=e.O()}]);