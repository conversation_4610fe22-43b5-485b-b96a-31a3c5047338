"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[888],{888:(e,t,r)=>{r.d(t,{UC:()=>en,B8:()=>et,bL:()=>ee,l9:()=>er});var n,i=r(2115),o=r(5185),s=r(6081);function l(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function a(e,t){var r=l(e,t,"get");return r.get?r.get.call(e):r.value}function u(e,t,r){var n=l(e,t,"set");if(n.set)n.set.call(e,r);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=r}return r}var f=r(6101),c=r(9708),h=r(5155),d=new WeakMap;function p(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=v(t),i=n>=0?n:r+n;return i<0||i>=r?-1:i}(e,t);return -1===r?void 0:e[r]}function v(e){return e!=e||0===e?0:Math.trunc(e)}n=new WeakMap,class e extends Map{set(e,t){return d.get(this)&&(this.has(e)?a(this,n)[a(this,n).indexOf(e)]=e:a(this,n).push(e)),super.set(e,t),this}insert(e,t,r){let i,o=this.has(t),s=a(this,n).length,l=v(e),u=l>=0?l:s+l,f=u<0||u>=s?-1:u;if(f===this.size||o&&f===this.size-1||-1===f)return this.set(t,r),this;let c=this.size+ +!o;l<0&&u++;let h=[...a(this,n)],d=!1;for(let e=u;e<c;e++)if(u===e){let n=h[e];h[e]===t&&(n=h[e+1]),o&&this.delete(t),i=this.get(n),this.set(t,r)}else{d||h[e-1]!==t||(d=!0);let r=h[d?e:e-1],n=i;i=this.get(r),this.delete(r),this.set(r,n)}return this}with(t,r,n){let i=new e(this);return i.insert(t,r,n),i}before(e){let t=a(this,n).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,r){let i=a(this,n).indexOf(e);return -1===i?this:this.insert(i,t,r)}after(e){let t=a(this,n).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,r){let i=a(this,n).indexOf(e);return -1===i?this:this.insert(i+1,t,r)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return u(this,n,[]),super.clear()}delete(e){let t=super.delete(e);return t&&a(this,n).splice(a(this,n).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=p(a(this,n),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=p(a(this,n),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return a(this,n).indexOf(e)}keyAt(e){return p(a(this,n),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.at(n)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let n=r+t;return n<0&&(n=0),n>=this.size&&(n=this.size-1),this.keyAt(n)}find(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return n;r++}}findIndex(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return r;r++}return -1}filter(t,r){let n=[],i=0;for(let e of this)Reflect.apply(t,r,[e,i,this])&&n.push(e),i++;return new e(n)}map(t,r){let n=[],i=0;for(let e of this)n.push([e[0],Reflect.apply(t,r,[e,i,this])]),i++;return new e(n)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i]=t,o=0,s=null!=i?i:this.at(0);for(let e of this)s=0===o&&1===t.length?e:Reflect.apply(n,this,[s,e,o,this]),o++;return s}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i]=t,o=null!=i?i:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);o=e===this.size-1&&1===t.length?r:Reflect.apply(n,this,[o,r,e,this])}return o}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),n=this.get(r);t.set(r,n)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let i=[...this.entries()];return i.splice(...r),new e(i)}slice(t,r){let n=new e,i=this.size-1;if(void 0===t)return n;t<0&&(t+=this.size),void 0!==r&&r>0&&(i=r-1);for(let e=t;e<=i;e++){let t=this.keyAt(e),r=this.get(t);n.set(t,r)}return n}every(e,t){let r=0;for(let n of this){if(!Reflect.apply(e,t,[n,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let n of this){if(Reflect.apply(e,t,[n,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,n,{writable:!0,value:void 0}),u(this,n,[...super.keys()]),d.set(this,!0)}};var m=r(1285),y=r(3655),w=r(9033),b=r(5845),g=i.createContext(void 0);function x(e){let t=i.useContext(g);return e||t||"ltr"}var R="rovingFocusGroup.onEntryFocus",A={bubbles:!1,cancelable:!0},C="RovingFocusGroup",[I,k,T]=function(e){let t=e+"CollectionProvider",[r,n]=(0,s.A)(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=i.useRef(null),s=i.useRef(new Map).current;return(0,h.jsx)(o,{scope:t,itemMap:s,collectionRef:n,children:r})};a.displayName=t;let u=e+"CollectionSlot",d=(0,c.TL)(u),p=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=l(u,r),o=(0,f.s)(t,i.collectionRef);return(0,h.jsx)(d,{ref:o,children:n})});p.displayName=u;let v=e+"CollectionItemSlot",m="data-radix-collection-item",y=(0,c.TL)(v),w=i.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,s=i.useRef(null),a=(0,f.s)(t,s),u=l(v,r);return i.useEffect(()=>(u.itemMap.set(s,{ref:s,...o}),()=>void u.itemMap.delete(s))),(0,h.jsx)(y,{...{[m]:""},ref:a,children:n})});return w.displayName=v,[{Provider:a,Slot:p,ItemSlot:w},function(t){let r=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}(C),[F,j]=(0,s.A)(C,[T]),[E,M]=F(C),D=i.forwardRef((e,t)=>(0,h.jsx)(I.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(I.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(z,{...e,ref:t})})}));D.displayName=C;var z=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:s=!1,dir:l,currentTabStopId:a,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:c,onEntryFocus:d,preventScrollOnEntryFocus:p=!1,...v}=e,m=i.useRef(null),g=(0,f.s)(t,m),I=x(l),[T,F]=(0,b.i)({prop:a,defaultProp:null!=u?u:null,onChange:c,caller:C}),[j,M]=i.useState(!1),D=(0,w.c)(d),z=k(r),S=i.useRef(!1),[O,G]=i.useState(0);return i.useEffect(()=>{let e=m.current;if(e)return e.addEventListener(R,D),()=>e.removeEventListener(R,D)},[D]),(0,h.jsx)(E,{scope:r,orientation:n,dir:I,loop:s,currentTabStopId:T,onItemFocus:i.useCallback(e=>F(e),[F]),onItemShiftTab:i.useCallback(()=>M(!0),[]),onFocusableItemAdd:i.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:i.useCallback(()=>G(e=>e-1),[]),children:(0,h.jsx)(y.sG.div,{tabIndex:j||0===O?-1:0,"data-orientation":n,...v,ref:g,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(R,A);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=z().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),p)}}S.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>M(!1))})})}),S="RovingFocusGroupItem",O=i.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:s=!1,tabStopId:l,children:a,...u}=e,f=(0,m.B)(),c=l||f,d=M(S,r),p=d.currentTabStopId===c,v=k(r),{onFocusableItemAdd:w,onFocusableItemRemove:b,currentTabStopId:g}=d;return i.useEffect(()=>{if(n)return w(),()=>b()},[n,w,b]),(0,h.jsx)(I.ItemSlot,{scope:r,id:c,focusable:n,active:s,children:(0,h.jsx)(y.sG.span,{tabIndex:p?0:-1,"data-orientation":d.orientation,...u,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{n?d.onItemFocus(c):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>d.onItemFocus(c)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void d.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return G[i]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=d.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>N(r))}}),children:"function"==typeof a?a({isCurrentTabStop:p,hasTabStop:null!=g}):a})})});O.displayName=S;var G={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var K=r(8905),L="Tabs",[P,_]=(0,s.A)(L,[j]),B=j(),[U,V]=P(L),q=i.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:o,orientation:s="horizontal",dir:l,activationMode:a="automatic",...u}=e,f=x(l),[c,d]=(0,b.i)({prop:n,onChange:i,defaultProp:null!=o?o:"",caller:L});return(0,h.jsx)(U,{scope:r,baseId:(0,m.B)(),value:c,onValueChange:d,orientation:s,dir:f,activationMode:a,children:(0,h.jsx)(y.sG.div,{dir:f,"data-orientation":s,...u,ref:t})})});q.displayName=L;var W="TabsList",H=i.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,o=V(W,r),s=B(r);return(0,h.jsx)(D,{asChild:!0,...s,orientation:o.orientation,dir:o.dir,loop:n,children:(0,h.jsx)(y.sG.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:t})})});H.displayName=W;var J="TabsTrigger",Q=i.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...s}=e,l=V(J,r),a=B(r),u=Z(l.baseId,n),f=$(l.baseId,n),c=n===l.value;return(0,h.jsx)(O,{asChild:!0,...a,focusable:!i,active:c,children:(0,h.jsx)(y.sG.button,{type:"button",role:"tab","aria-selected":c,"aria-controls":f,"data-state":c?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...s,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;c||i||!e||l.onValueChange(n)})})})});Q.displayName=J;var X="TabsContent",Y=i.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,forceMount:o,children:s,...l}=e,a=V(X,r),u=Z(a.baseId,n),f=$(a.baseId,n),c=n===a.value,d=i.useRef(c);return i.useEffect(()=>{let e=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(K.C,{present:o||c,children:r=>{let{present:n}=r;return(0,h.jsx)(y.sG.div,{"data-state":c?"active":"inactive","data-orientation":a.orientation,role:"tabpanel","aria-labelledby":u,hidden:!n,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:n&&s})}})});function Z(e,t){return"".concat(e,"-trigger-").concat(t)}function $(e,t){return"".concat(e,"-content-").concat(t)}Y.displayName=X;var ee=q,et=H,er=Q,en=Y}}]);