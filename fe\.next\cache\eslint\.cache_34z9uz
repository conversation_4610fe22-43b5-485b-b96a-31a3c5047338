[{"D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\affiliate\\page.tsx": "1", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\ai-experts\\page.tsx": "2", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\api\\chat\\route.ts": "3", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx": "4", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx": "5", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\client-provider.tsx": "6", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\page.tsx": "7", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx": "8", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx": "9", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\expert\\[id]\\page.tsx": "10", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx": "11", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\history\\page.tsx": "12", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx": "13", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\login\\page.tsx": "14", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\page.tsx": "15", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\profile\\page.tsx": "16", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\register\\page.tsx": "17", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx": "18", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\verify-otp\\page.tsx": "19", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\AffiliateDashboard.tsx": "20", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ApiExample.tsx": "21", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\BalanceComponent.tsx": "22", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ChatHistory.tsx": "23", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\CreateExpert.tsx": "24", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\EditExpert.tsx": "25", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertList.tsx": "26", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertMarketplace.tsx": "27", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertOverview.tsx": "28", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertPanel.tsx": "29", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertProfile.tsx": "30", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertStatsDashboard.tsx": "31", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\Navigation.tsx": "32", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\PricingCalculator.tsx": "33", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RatingSummary.tsx": "34", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RecommendationSection.tsx": "35", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewForm.tsx": "36", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewList.tsx": "37", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewModal.tsx": "38", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareAnalytics.tsx": "39", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareCreator.tsx": "40", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\SharedExpertLanding.tsx": "41", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareList.tsx": "42", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\SimilarExperts.tsx": "43", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\StreamingChatInterface.tsx": "44", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert-dialog.tsx": "45", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert.tsx": "46", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\avatar.tsx": "47", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\badge.tsx": "48", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\button.tsx": "49", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\card.tsx": "50", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\checkbox.tsx": "51", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\connection-status.tsx": "52", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dialog.tsx": "53", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dropdown-menu.tsx": "54", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\form.tsx": "55", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\input.tsx": "56", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\label.tsx": "57", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\progress.tsx": "58", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\real-time-balance.tsx": "59", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\select.tsx": "60", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\skeleton.tsx": "61", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\star-rating.tsx": "62", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\streaming-message.tsx": "63", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs-simple.tsx": "64", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs.tsx": "65", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\UserStatsCard.tsx": "66", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\AuthContext.tsx": "67", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx": "68", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\use-toast.ts": "69", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useRecommendations.ts": "70", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useSharing.ts": "71", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useStreamingChat.ts": "72", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\affiliateTracker.ts": "73", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\api.ts": "74", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\recommendationService.ts": "75", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\utils.ts": "76", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\common\\ErrorBoundary.tsx": "77", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\ExpertCard.tsx": "78", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\ExpertsGrid.tsx": "79", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\FilterErrorFallback.tsx": "80", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\FilterSelect.tsx": "81", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\index.ts": "82", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\MarketplaceFilters.tsx": "83", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\MarketplacePage.tsx": "84", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\TimelineSelect.tsx": "85", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\FilterProvider.tsx": "86", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useFilteredExperts.ts": "87", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useFilters.ts": "88", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\types\\filters.ts": "89", "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\utils\\searchHighlight.tsx": "90"}, {"size": 309, "mtime": 1755135974857, "results": "91", "hashOfConfig": "92"}, {"size": 777, "mtime": 1755336549999, "results": "93", "hashOfConfig": "92"}, {"size": 1538, "mtime": 1754029021449, "results": "94", "hashOfConfig": "92"}, {"size": 624, "mtime": 1754029021450, "results": "95", "hashOfConfig": "92"}, {"size": 11256, "mtime": 1755327927350, "results": "96", "hashOfConfig": "92"}, {"size": 395, "mtime": 1754029021451, "results": "97", "hashOfConfig": "92"}, {"size": 12428, "mtime": 1755136376388, "results": "98", "hashOfConfig": "92"}, {"size": 17332, "mtime": 1755159623934, "results": "99", "hashOfConfig": "92"}, {"size": 985, "mtime": 1755159649602, "results": "100", "hashOfConfig": "92"}, {"size": 530, "mtime": 1754403356454, "results": "101", "hashOfConfig": "92"}, {"size": 1437, "mtime": 1754029021454, "results": "102", "hashOfConfig": "92"}, {"size": 281, "mtime": 1754029021456, "results": "103", "hashOfConfig": "92"}, {"size": 2305, "mtime": 1755131813438, "results": "104", "hashOfConfig": "92"}, {"size": 7305, "mtime": 1754403356455, "results": "105", "hashOfConfig": "92"}, {"size": 4603, "mtime": 1754029021458, "results": "106", "hashOfConfig": "92"}, {"size": 24420, "mtime": 1755136629853, "results": "107", "hashOfConfig": "92"}, {"size": 18981, "mtime": 1755136368921, "results": "108", "hashOfConfig": "92"}, {"size": 15385, "mtime": 1755159665637, "results": "109", "hashOfConfig": "92"}, {"size": 9682, "mtime": 1755136036363, "results": "110", "hashOfConfig": "92"}, {"size": 16841, "mtime": 1755136547851, "results": "111", "hashOfConfig": "92"}, {"size": 5464, "mtime": 1754029021463, "results": "112", "hashOfConfig": "92"}, {"size": 14505, "mtime": 1755136434811, "results": "113", "hashOfConfig": "92"}, {"size": 13593, "mtime": 1754029021464, "results": "114", "hashOfConfig": "92"}, {"size": 29158, "mtime": 1755327590281, "results": "115", "hashOfConfig": "92"}, {"size": 15843, "mtime": 1755327949627, "results": "116", "hashOfConfig": "92"}, {"size": 26722, "mtime": 1755327901844, "results": "117", "hashOfConfig": "92"}, {"size": 15088, "mtime": 1755327916658, "results": "118", "hashOfConfig": "92"}, {"size": 26916, "mtime": 1755136480238, "results": "119", "hashOfConfig": "92"}, {"size": 4301, "mtime": 1755327961211, "results": "120", "hashOfConfig": "92"}, {"size": 18810, "mtime": 1755327938967, "results": "121", "hashOfConfig": "92"}, {"size": 10091, "mtime": 1755137880327, "results": "122", "hashOfConfig": "92"}, {"size": 8792, "mtime": 1755136170069, "results": "123", "hashOfConfig": "92"}, {"size": 6038, "mtime": 1755136142363, "results": "124", "hashOfConfig": "92"}, {"size": 4736, "mtime": 1755137535264, "results": "125", "hashOfConfig": "92"}, {"size": 11082, "mtime": 1755263166827, "results": "126", "hashOfConfig": "92"}, {"size": 4480, "mtime": 1755137478769, "results": "127", "hashOfConfig": "92"}, {"size": 8259, "mtime": 1755158803875, "results": "128", "hashOfConfig": "92"}, {"size": 4683, "mtime": 1755159599866, "results": "129", "hashOfConfig": "92"}, {"size": 17944, "mtime": 1755159833144, "results": "130", "hashOfConfig": "92"}, {"size": 9859, "mtime": 1755327973953, "results": "131", "hashOfConfig": "92"}, {"size": 14173, "mtime": 1755161124379, "results": "132", "hashOfConfig": "92"}, {"size": 15578, "mtime": 1755161869219, "results": "133", "hashOfConfig": "92"}, {"size": 6969, "mtime": 1755263228450, "results": "134", "hashOfConfig": "92"}, {"size": 15219, "mtime": 1755326979149, "results": "135", "hashOfConfig": "92"}, {"size": 5414, "mtime": 1755153583376, "results": "136", "hashOfConfig": "92"}, {"size": 1641, "mtime": 1755263442883, "results": "137", "hashOfConfig": "92"}, {"size": 1150, "mtime": 1754029021473, "results": "138", "hashOfConfig": "92"}, {"size": 1162, "mtime": 1755263430324, "results": "139", "hashOfConfig": "92"}, {"size": 2182, "mtime": 1754029021474, "results": "140", "hashOfConfig": "92"}, {"size": 2081, "mtime": 1754029021474, "results": "141", "hashOfConfig": "92"}, {"size": 1448, "mtime": 1755153490187, "results": "142", "hashOfConfig": "92"}, {"size": 2708, "mtime": 1755136020217, "results": "143", "hashOfConfig": "92"}, {"size": 4125, "mtime": 1754029021475, "results": "144", "hashOfConfig": "92"}, {"size": 2860, "mtime": 1755153556787, "results": "145", "hashOfConfig": "92"}, {"size": 3926, "mtime": 1754029021475, "results": "146", "hashOfConfig": "92"}, {"size": 988, "mtime": 1754029021476, "results": "147", "hashOfConfig": "92"}, {"size": 635, "mtime": 1754029021476, "results": "148", "hashOfConfig": "92"}, {"size": 851, "mtime": 1754029021477, "results": "149", "hashOfConfig": "92"}, {"size": 6448, "mtime": 1755136018428, "results": "150", "hashOfConfig": "92"}, {"size": 3469, "mtime": 1755158675757, "results": "151", "hashOfConfig": "92"}, {"size": 274, "mtime": 1755263418890, "results": "152", "hashOfConfig": "92"}, {"size": 2195, "mtime": 1755137457200, "results": "153", "hashOfConfig": "92"}, {"size": 5894, "mtime": 1755136306330, "results": "154", "hashOfConfig": "92"}, {"size": 3654, "mtime": 1754029021478, "results": "155", "hashOfConfig": "92"}, {"size": 2035, "mtime": 1754029021478, "results": "156", "hashOfConfig": "92"}, {"size": 4253, "mtime": 1754029021471, "results": "157", "hashOfConfig": "92"}, {"size": 6597, "mtime": 1754029021479, "results": "158", "hashOfConfig": "92"}, {"size": 8512, "mtime": 1755337313573, "results": "159", "hashOfConfig": "92"}, {"size": 1843, "mtime": 1755153478679, "results": "160", "hashOfConfig": "92"}, {"size": 7560, "mtime": 1755271793337, "results": "161", "hashOfConfig": "92"}, {"size": 12233, "mtime": 1755163725221, "results": "162", "hashOfConfig": "92"}, {"size": 9327, "mtime": 1755327213204, "results": "163", "hashOfConfig": "92"}, {"size": 4815, "mtime": 1754029021480, "results": "164", "hashOfConfig": "92"}, {"size": 15449, "mtime": 1755337804762, "results": "165", "hashOfConfig": "92"}, {"size": 6407, "mtime": 1755265118250, "results": "166", "hashOfConfig": "92"}, {"size": 377, "mtime": 1754403356469, "results": "167", "hashOfConfig": "92"}, {"size": 3056, "mtime": 1755334641684, "results": "168", "hashOfConfig": "92"}, {"size": 5166, "mtime": 1755336828120, "results": "169", "hashOfConfig": "92"}, {"size": 4042, "mtime": 1755333911431, "results": "170", "hashOfConfig": "92"}, {"size": 4310, "mtime": 1755336814718, "results": "171", "hashOfConfig": "92"}, {"size": 2537, "mtime": 1755337233473, "results": "172", "hashOfConfig": "92"}, {"size": 408, "mtime": 1755336737918, "results": "173", "hashOfConfig": "92"}, {"size": 5464, "mtime": 1755337130459, "results": "174", "hashOfConfig": "92"}, {"size": 4131, "mtime": 1755337144037, "results": "175", "hashOfConfig": "92"}, {"size": 2065, "mtime": 1755337263573, "results": "176", "hashOfConfig": "92"}, {"size": 4655, "mtime": 1755337161552, "results": "177", "hashOfConfig": "92"}, {"size": 3977, "mtime": 1755337781386, "results": "178", "hashOfConfig": "92"}, {"size": 480, "mtime": 1755333536979, "results": "179", "hashOfConfig": "92"}, {"size": 2735, "mtime": 1755333271621, "results": "180", "hashOfConfig": "92"}, {"size": 4428, "mtime": 1755333806785, "results": "181", "hashOfConfig": "92"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3zk8ff", {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\affiliate\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\ai-experts\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\api\\chat\\route.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\balance\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\chat\\page.tsx", [], ["452"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\client-provider.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\dashboard\\shares\\[shareToken]\\analytics\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\expert\\[id]\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\experts\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\history\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\layout.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\login\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\profile\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\register\\page.tsx", [], ["453"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\shared\\[shareToken]\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\app\\verify-otp\\page.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\AffiliateDashboard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ApiExample.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\BalanceComponent.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ChatHistory.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\CreateExpert.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\EditExpert.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertList.tsx", [], ["454"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertMarketplace.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertOverview.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertPanel.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertProfile.tsx", [], ["455"], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ExpertStatsDashboard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\Navigation.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\PricingCalculator.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RatingSummary.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\RecommendationSection.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewForm.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewList.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ReviewModal.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareAnalytics.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareCreator.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\SharedExpertLanding.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\sharing\\ShareList.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\SimilarExperts.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\StreamingChatInterface.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert-dialog.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\alert.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\avatar.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\badge.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\button.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\card.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\checkbox.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\connection-status.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dialog.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\dropdown-menu.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\form.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\input.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\label.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\progress.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\real-time-balance.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\select.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\skeleton.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\star-rating.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\streaming-message.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs-simple.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\ui\\tabs.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\UserStatsCard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\AuthContext.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\SocketContext.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\use-toast.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useRecommendations.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useSharing.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useStreamingChat.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\affiliateTracker.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\api.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\recommendationService.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\lib\\utils.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\common\\ErrorBoundary.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\ExpertCard.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\ExpertsGrid.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\FilterErrorFallback.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\FilterSelect.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\index.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\MarketplaceFilters.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\MarketplacePage.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\components\\marketplace\\TimelineSelect.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\contexts\\FilterProvider.tsx", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useFilteredExperts.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\hooks\\useFilters.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\types\\filters.ts", [], [], "D:\\Project\\Web\\pakarai\\ai-trainer\\fe\\src\\utils\\searchHighlight.tsx", [], [], {"ruleId": "456", "severity": 1, "message": "457", "line": 48, "column": 6, "nodeType": "458", "endLine": 48, "endColumn": 26, "suggestions": "459", "suppressions": "460"}, {"ruleId": "456", "severity": 1, "message": "461", "line": 48, "column": 6, "nodeType": "458", "endLine": 48, "endColumn": 20, "suggestions": "462", "suppressions": "463"}, {"ruleId": "456", "severity": 1, "message": "464", "line": 142, "column": 6, "nodeType": "458", "endLine": 142, "endColumn": 22, "suggestions": "465", "suppressions": "466"}, {"ruleId": "456", "severity": 1, "message": "467", "line": 70, "column": 6, "nodeType": "458", "endLine": 70, "endColumn": 16, "suggestions": "468", "suppressions": "469"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initializeChatWithExpert' and 'loadChatHistory'. Either include them or remove the dependency array.", "ArrayExpression", ["470"], ["471"], "React Hook useEffect has a missing dependency: 'checkAffiliateTracking'. Either include it or remove the dependency array.", ["472"], ["473"], "React Hook useEffect has a missing dependency: 'loadExperts'. Either include it or remove the dependency array.", ["474"], ["475"], "React Hook useEffect has a missing dependency: 'loadExpert'. Either include it or remove the dependency array.", ["476"], ["477"], {"desc": "478", "fix": "479"}, {"kind": "480", "justification": "481"}, {"desc": "482", "fix": "483"}, {"kind": "480", "justification": "481"}, {"desc": "484", "fix": "485"}, {"kind": "480", "justification": "481"}, {"desc": "486", "fix": "487"}, {"kind": "480", "justification": "481"}, "Update the dependencies array to be: [expertId, initializeChatWithExpert, loadChatHistory, threadId]", {"range": "488", "text": "489"}, "directive", "", "Update the dependencies array to be: [checkAffiliateTracking, searchParams]", {"range": "490", "text": "491"}, "Update the dependencies array to be: [loadExperts, refreshTrigger]", {"range": "492", "text": "493"}, "Update the dependencies array to be: [expertId, loadExpert]", {"range": "494", "text": "495"}, [1588, 1608], "[expertId, initializeChatWithExpert, loadChatHistory, threadId]", [1395, 1409], "[checkAffiliateTracking, searchParams]", [3807, 3823], "[loadExperts, refreshTrigger]", [2078, 2088], "[expertId, loadExpert]"]