(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{238:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},1586:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},4375:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>A});var t=a(5155),l=a(2115),r=a(6695),n=a(285),c=a(4944),i=a(9434);let d=l.createContext(null),x=l.forwardRef((e,s)=>{let{defaultValue:a,value:r,onValueChange:n,className:c,children:x,...m}=e,[h,o]=l.useState(a||"");return(0,t.jsx)(d.Provider,{value:{value:r||h,onValueChange:e=>{r||o(e),null==n||n(e)}},children:(0,t.jsx)("div",{ref:s,className:(0,i.cn)("w-full",c),...m,children:x})})});x.displayName="Tabs";let m=l.forwardRef((e,s)=>{let{className:a,children:l,...r}=e;return(0,t.jsx)("div",{ref:s,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500",a),...r,children:l})});m.displayName="TabsList";let h=l.forwardRef((e,s)=>{let{value:a,className:r,children:n,...c}=e,x=l.useContext(d);if(!x)throw Error("TabsTrigger must be used within a Tabs component");let m=x.value===a;return(0,t.jsx)("button",{ref:s,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",m?"bg-white text-slate-950 shadow-sm":"text-slate-500 hover:text-slate-900",r),onClick:()=>x.onValueChange(a),...c,children:n})});h.displayName="TabsTrigger";let o=l.forwardRef((e,s)=>{let{value:a,className:r,children:n,...c}=e,x=l.useContext(d);if(!x)throw Error("TabsContent must be used within a Tabs component");return x.value!==a?null:(0,t.jsx)("div",{ref:s,className:(0,i.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2",r),...c,children:n})});o.displayName="TabsContent";var u=a(5731),p=a(238),j=a(1586),N=a(5868),f=a(9946);let y=(0,f.A)("coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);var g=a(9785),v=a(4616);let b=(0,f.A)("history",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);function w(e){let{showActions:s=!0,compact:a=!1,className:i=""}=e,[d,f]=(0,l.useState)(null),[w,A]=(0,l.useState)([]),[k,D]=(0,l.useState)([]),[E,T]=(0,l.useState)(!0),[B,C]=(0,l.useState)(null);(0,l.useEffect)(()=>{M()},[]);let M=async()=>{try{T(!0);let e=await (0,u.H2)("/api/balance/summary");if(e.success)f(e.data.balance),A(e.data.recentPointTransactions),D(e.data.recentCreditTransactions);else throw Error(e.message||"Failed to fetch balance")}catch(e){C(e.message),console.error("Error fetching balance:",e)}finally{T(!1)}},_=e=>new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0,maximumFractionDigits:0}).format(e),R=e=>{switch(e){case"EARNED":case"PURCHASED":return"text-green-600";case"USED":return"text-red-600";case"ADMIN_ADDED":return"text-blue-600";case"ADMIN_DEDUCTED":return"text-orange-600";default:return"text-gray-600"}},S=e=>{switch(e){case"EARNED":return(0,t.jsx)(p.A,{className:"w-4 h-4 text-green-600"});case"PURCHASED":return(0,t.jsx)(j.A,{className:"w-4 h-4 text-blue-600"});case"USED":return(0,t.jsx)(N.A,{className:"w-4 h-4 text-red-600"});default:return(0,t.jsx)(y,{className:"w-4 h-4 text-gray-600"})}};if(E)return(0,t.jsx)("div",{className:"space-y-4 ".concat(i),children:(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})]})})})});if(B)return(0,t.jsx)(r.Zp,{className:i,children:(0,t.jsx)(r.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center text-red-600",children:[(0,t.jsxs)("p",{children:["Error loading balance: ",B]}),(0,t.jsx)(n.$,{onClick:M,variant:"outline",className:"mt-2",children:"Retry"})]})})});if(!d)return null;if(a)return(0,t.jsx)(r.Zp,{className:"bg-gradient-to-r from-blue-50 to-purple-50 ".concat(i),children:(0,t.jsx)(r.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-full",children:(0,t.jsx)(g.A,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Balance"}),(0,t.jsx)("p",{className:"text-lg font-bold text-gray-900",children:_(d.totalBalance)})]})]}),(0,t.jsxs)("div",{className:"text-right text-xs text-gray-500",children:[(0,t.jsxs)("p",{children:[_(d.pointBalance)," points"]}),(0,t.jsxs)("p",{children:[_(d.creditBalance)," credits"]})]})]})})});let P=d.totalBalance>0?d.pointBalance/d.totalBalance*100:0;return(0,t.jsxs)("div",{className:"space-y-6 ".concat(i),children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(r.Zp,{className:"bg-gradient-to-r from-blue-50 to-blue-100",children:(0,t.jsxs)(r.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-600",children:"Total Balance"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:_(d.totalBalance)})]}),(0,t.jsx)("div",{className:"p-3 bg-blue-200 rounded-full",children:(0,t.jsx)(g.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"text-blue-600",children:["Points: ",_(d.pointBalance)]}),(0,t.jsxs)("span",{className:"text-blue-600",children:[P.toFixed(1),"%"]})]}),(0,t.jsx)(c.k,{value:P,className:"h-2"}),(0,t.jsxs)("p",{className:"text-xs text-blue-500",children:["Credits: ",_(d.creditBalance)]})]})]})}),(0,t.jsx)(r.Zp,{className:"bg-gradient-to-r from-green-50 to-green-100",children:(0,t.jsxs)(r.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-green-600",children:"Points (Bonus)"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-900",children:_(d.pointBalance)})]}),(0,t.jsx)("div",{className:"p-3 bg-green-200 rounded-full",children:(0,t.jsx)(p.A,{className:"w-6 h-6 text-green-600"})})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("p",{className:"text-xs text-green-600",children:["Total earned: ",_(d.totalPointsEarned)]})})]})}),(0,t.jsx)(r.Zp,{className:"bg-gradient-to-r from-purple-50 to-purple-100",children:(0,t.jsxs)(r.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-purple-600",children:"Credits (Paid)"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-purple-900",children:_(d.creditBalance)})]}),(0,t.jsx)("div",{className:"p-3 bg-purple-200 rounded-full",children:(0,t.jsx)(j.A,{className:"w-6 h-6 text-purple-600"})})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("p",{className:"text-xs text-purple-600",children:["Total purchased: ",_(d.totalCreditsPurchased)]})})]})})]}),s&&(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Add Balance"})]}),(0,t.jsx)(r.BT,{children:"Top up your credits or learn how to earn more points"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"flex flex-wrap gap-3",children:(0,t.jsxs)(n.$,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Top Up Credits"})]})})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsxs)(r.aR,{children:[(0,t.jsxs)(r.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(b,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Recent Transactions"})]}),(0,t.jsx)(r.BT,{children:"Latest point and credit transactions"})]}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)(x,{defaultValue:"points",className:"w-full",children:[(0,t.jsxs)(m,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(h,{value:"points",className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Points"})]}),(0,t.jsxs)(h,{value:"credits",className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Credits"})]})]}),(0,t.jsx)(o,{value:"points",className:"space-y-4",children:w.length>0?(0,t.jsx)("div",{className:"space-y-3",children:w.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[S(e.transaction_type),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.description}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("id-ID")})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"font-semibold ".concat(R(e.transaction_type)),children:["USED"===e.transaction_type?"-":"+",e.formattedAmount]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Balance: ",_(e.balance_after)]})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(p.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No point transactions yet"})]})}),(0,t.jsx)(o,{value:"credits",className:"space-y-4",children:k.length>0?(0,t.jsx)("div",{className:"space-y-3",children:k.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[S(e.transaction_type),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-gray-900",children:e.description}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("id-ID")})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("p",{className:"font-semibold ".concat(R(e.transaction_type)),children:["USED"===e.transaction_type?"-":"+",e.formattedAmount]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Balance: ",_(e.balance_after)]})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(j.A,{className:"w-12 h-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No credit transactions yet"})]})})]})})]})]})}function A(){return(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Balance Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage your points and credits for AI Trainer Hub services"})]}),(0,t.jsx)(w,{})]})})}},4616:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4944:(e,s,a)=>{"use strict";a.d(s,{k:()=>n});var t=a(5155),l=a(2115),r=a(9434);let n=l.forwardRef((e,s)=>{let{className:a,value:l=0,max:n=100,...c}=e,i=Math.min(100,Math.max(0,l/n*100));return(0,t.jsx)("div",{ref:s,className:(0,r.cn)("relative h-4 w-full overflow-hidden rounded-full bg-gray-200",a),...c,children:(0,t.jsx)("div",{className:"h-full bg-blue-600 transition-all duration-300 ease-in-out",style:{width:"".concat(i,"%")}})})});n.displayName="Progress"},5868:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},8649:(e,s,a)=>{Promise.resolve().then(a.bind(a,4375))},9785:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])}},e=>{e.O(0,[445,352,573,441,964,358],()=>e(e.s=8649)),_N_E=e.O()}]);