{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function asset(path: string) {\r\n  return `${API_URL}/${path.startsWith(\"/\") ? path.replace(\"/\", \"\") : path}`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAGgB;AAHhB;AACA;;;AAEA,MAAM,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAE5C,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,MAAM,IAAY;IAChC,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAuD,OAApD,KAAK,UAAU,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,MAAM;AACtE", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/star-rating.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Star } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface StarRatingProps {\r\n  rating: number;\r\n  maxRating?: number;\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  interactive?: boolean;\r\n  onRatingChange?: (rating: number) => void;\r\n  className?: string;\r\n}\r\n\r\nconst StarRating: React.FC<StarRatingProps> = ({\r\n  rating,\r\n  maxRating = 5,\r\n  size = \"md\",\r\n  interactive = false,\r\n  onRatingChange,\r\n  className\r\n}) => {\r\n  const [hoverRating, setHoverRating] = React.useState(0);\r\n\r\n  const sizeClasses = {\r\n    sm: \"w-4 h-4\",\r\n    md: \"w-5 h-5\",\r\n    lg: \"w-6 h-6\"\r\n  };\r\n\r\n  const handleClick = (starRating: number) => {\r\n    if (interactive && onRatingChange) {\r\n      onRatingChange(starRating);\r\n    }\r\n  };\r\n\r\n  const handleMouseEnter = (starRating: number) => {\r\n    if (interactive) {\r\n      setHoverRating(starRating);\r\n    }\r\n  };\r\n\r\n  const handleMouseLeave = () => {\r\n    if (interactive) {\r\n      setHoverRating(0);\r\n    }\r\n  };\r\n\r\n  const getStarFill = (starIndex: number) => {\r\n    const currentRating = hoverRating || rating;\r\n    \r\n    if (starIndex <= currentRating) {\r\n      return \"fill-yellow-400 text-yellow-400\";\r\n    } else if (starIndex - 0.5 <= currentRating) {\r\n      return \"fill-yellow-400/50 text-yellow-400\";\r\n    } else {\r\n      return \"fill-gray-200 text-gray-200\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center gap-1\", className)}>\r\n      {Array.from({ length: maxRating }, (_, index) => {\r\n        const starRating = index + 1;\r\n        return (\r\n          <Star\r\n            key={index}\r\n            className={cn(\r\n              sizeClasses[size],\r\n              getStarFill(starRating),\r\n              interactive && \"cursor-pointer hover:scale-110 transition-transform\"\r\n            )}\r\n            onClick={() => handleClick(starRating)}\r\n            onMouseEnter={() => handleMouseEnter(starRating)}\r\n            onMouseLeave={handleMouseLeave}\r\n          />\r\n        );\r\n      })}\r\n      {rating > 0 && (\r\n        <span className=\"ml-2 text-sm text-gray-600\">\r\n          {rating.toFixed(1)}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StarRating;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAeA,MAAM,aAAwC;QAAC,EAC7C,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,cAAc,KAAK,EACnB,cAAc,EACd,SAAS,EACV;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAErD,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,eAAe,gBAAgB;YACjC,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,aAAa;YACf,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa;YACf,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,gBAAgB,eAAe;QAErC,IAAI,aAAa,eAAe;YAC9B,OAAO;QACT,OAAO,IAAI,YAAY,OAAO,eAAe;YAC3C,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;YAC3C,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAU,GAAG,CAAC,GAAG;gBACrC,MAAM,aAAa,QAAQ;gBAC3B,qBACE,6LAAC,qMAAA,CAAA,OAAI;oBAEH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB,YAAY,aACZ,eAAe;oBAEjB,SAAS,IAAM,YAAY;oBAC3B,cAAc,IAAM,iBAAiB;oBACrC,cAAc;mBART;;;;;YAWX;YACC,SAAS,mBACR,6LAAC;gBAAK,WAAU;0BACb,OAAO,OAAO,CAAC;;;;;;;;;;;;AAK1B;GAvEM;KAAA;uCAyES", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { ChevronDown } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface SelectProps {\r\n  value?: string;\r\n  onValueChange?: (value: string) => void;\r\n  children: React.ReactNode;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectTriggerProps {\r\n  className?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\ninterface SelectValueProps {\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\ninterface SelectContentProps {\r\n  className?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\ninterface SelectItemProps {\r\n  value: string;\r\n  className?: string;\r\n  children: React.ReactNode;\r\n  onSelect?: () => void;\r\n}\r\n\r\nconst SelectContext = React.createContext<{\r\n  value?: string;\r\n  onValueChange?: (value: string) => void;\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n}>({\r\n  open: false,\r\n  setOpen: () => {},\r\n});\r\n\r\nconst Select: React.FC<SelectProps> = ({ value, onValueChange, children }) => {\r\n  const [open, setOpen] = React.useState(false);\r\n\r\n  return (\r\n    <SelectContext.Provider value={{ value, onValueChange, open, setOpen }}>\r\n      <div className=\"relative\">\r\n        {children}\r\n      </div>\r\n    </SelectContext.Provider>\r\n  );\r\n};\r\n\r\nconst SelectTrigger: React.FC<SelectTriggerProps> = ({ className, children }) => {\r\n  const { open, setOpen } = React.useContext(SelectContext);\r\n\r\n  return (\r\n    <button\r\n      type=\"button\"\r\n      className={cn(\r\n        \"flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={() => setOpen(!open)}\r\n    >\r\n      {children}\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </button>\r\n  );\r\n};\r\n\r\nconst SelectValue: React.FC<SelectValueProps> = ({ placeholder, className }) => {\r\n  const { value } = React.useContext(SelectContext);\r\n\r\n  return (\r\n    <span className={cn(\"block truncate\", className)}>\r\n      {value || placeholder}\r\n    </span>\r\n  );\r\n};\r\n\r\nconst SelectContent: React.FC<SelectContentProps> = ({ className, children }) => {\r\n  const { open, setOpen } = React.useContext(SelectContext);\r\n\r\n  if (!open) return null;\r\n\r\n  return (\r\n    <>\r\n      <div\r\n        className=\"fixed inset-0 z-40\"\r\n        onClick={() => setOpen(false)}\r\n      />\r\n      <div\r\n        className={cn(\r\n          \"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md animate-in fade-in-0 zoom-in-95\",\r\n          className\r\n        )}\r\n      >\r\n        {children}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nconst SelectItem: React.FC<SelectItemProps> = ({ value, className, children, onSelect }) => {\r\n  const { onValueChange, setOpen } = React.useContext(SelectContext);\r\n\r\n  const handleSelect = () => {\r\n    onValueChange?.(value);\r\n    setOpen(false);\r\n    onSelect?.();\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleSelect}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue };"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAmCA,MAAM,8BAAgB,6JAAA,CAAA,gBAAmB,CAKtC;IACD,MAAM;IACN,SAAS,KAAO;AAClB;AAEA,MAAM,SAAgC;QAAC,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE;;IACvE,MAAM,CAAC,MAAM,QAAQ,GAAG,6JAAA,CAAA,WAAc,CAAC;IAEvC,qBACE,6LAAC,cAAc,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAe;YAAM;QAAQ;kBACnE,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;GAVM;KAAA;AAYN,MAAM,gBAA8C;QAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;;IAC1E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,6JAAA,CAAA,aAAgB,CAAC;IAE3C,qBACE,6LAAC;QACC,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kRACA;QAEF,SAAS,IAAM,QAAQ,CAAC;;YAEvB;0BACD,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;;;;;;;AAG7B;IAhBM;MAAA;AAkBN,MAAM,cAA0C;QAAC,EAAE,WAAW,EAAE,SAAS,EAAE;;IACzE,MAAM,EAAE,KAAK,EAAE,GAAG,6JAAA,CAAA,aAAgB,CAAC;IAEnC,qBACE,6LAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACnC,SAAS;;;;;;AAGhB;IARM;MAAA;AAUN,MAAM,gBAA8C;QAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;;IAC1E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,6JAAA,CAAA,aAAgB,CAAC;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,QAAQ;;;;;;0BAEzB,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qJACA;0BAGD;;;;;;;;AAIT;IArBM;MAAA;AAuBN,MAAM,aAAwC;QAAC,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE;;IACrF,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,6JAAA,CAAA,aAAgB,CAAC;IAEpD,MAAM,eAAe;QACnB,0BAAA,oCAAA,cAAgB;QAChB,QAAQ;QACR,qBAAA,+BAAA;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oNACA;QAEF,SAAS;kBAER;;;;;;AAGP;IApBM;MAAA", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/lib/recommendationService.ts"], "sourcesContent": ["import { api } from './api';\r\n\r\nexport interface Recommendation {\r\n  expert_id: number;\r\n  name: string;\r\n  description: string;\r\n  category: string;\r\n  average_rating: number;\r\n  total_chats: number;\r\n  price_per_message: number;\r\n  score: number;\r\n  reason: string;\r\n}\r\n\r\nexport interface RecommendationResponse {\r\n  recommendations: Recommendation[];\r\n  algorithm_used: string;\r\n  total_available: number;\r\n  cache_hit: boolean;\r\n  generated_at: string;\r\n  fallback?: boolean;\r\n  error?: string;\r\n}\r\n\r\nexport interface SimilarExpert {\r\n  expert_id: number;\r\n  name: string;\r\n  similarity_score: number;\r\n  category: string;\r\n  average_rating: number;\r\n}\r\n\r\nexport interface SimilarExpertsResponse {\r\n  similar_experts: SimilarExpert[];\r\n  reference_expert: {\r\n    id: number;\r\n    name: string;\r\n    category?: string;\r\n  };\r\n}\r\n\r\nexport interface InteractionData {\r\n  expert_id: number;\r\n  interaction_type: 'click' | 'dismiss' | 'favorite' | 'chat_start' | 'view';\r\n  recommendation_position?: number;\r\n  algorithm_used?: string;\r\n  metadata?: Record<string, any>;\r\n}\r\n\r\nexport interface ServiceHealth {\r\n  status: 'healthy' | 'unhealthy';\r\n  database?: {\r\n    connected: boolean;\r\n    response_time_ms: number;\r\n  };\r\n  cache?: {\r\n    connected: boolean;\r\n    response_time_ms: number;\r\n  };\r\n  statistics?: {\r\n    total_users: number;\r\n    total_experts: number;\r\n    total_interactions: number;\r\n    cache_hit_rate: number;\r\n  };\r\n  last_model_update?: string;\r\n  error?: string;\r\n}\r\n\r\nclass RecommendationService {\r\n  private baseUrl = '/api/recommendations';\r\n\r\n  /**\r\n   * Get personalized recommendations for the current user\r\n   */\r\n  async getPersonalizedRecommendations(options: {\r\n    limit?: number;\r\n    algorithm?: 'hybrid' | 'collaborative' | 'content' | 'trending';\r\n    category?: string;\r\n    exclude?: number[];\r\n  } = {}): Promise<RecommendationResponse> {\r\n    try {\r\n      const params = new URLSearchParams();\r\n\r\n      if (options.limit) params.append('limit', options.limit.toString());\r\n      if (options.algorithm) params.append('algorithm', options.algorithm);\r\n      if (options.category) params.append('category', options.category);\r\n      if (options.exclude && options.exclude.length > 0) {\r\n        params.append('exclude', options.exclude.join(','));\r\n      }\r\n\r\n      const response = await api.get(`${this.baseUrl}/personalized?${params.toString()}`);\r\n      return response.data.data;\r\n    } catch (error: any) {\r\n      console.error('Failed to get personalized recommendations:', error);\r\n\r\n      // Return fallback response\r\n      return {\r\n        recommendations: [],\r\n        algorithm_used: 'fallback',\r\n        total_available: 0,\r\n        cache_hit: false,\r\n        generated_at: new Date().toISOString(),\r\n        fallback: true,\r\n        error: error.response?.data?.message || 'Service unavailable'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get experts similar to a specific expert\r\n   */\r\n  async getSimilarExperts(expertId: number, limit: number = 10): Promise<SimilarExpertsResponse> {\r\n    try {\r\n      const response = await api.get(`${this.baseUrl}/similar/${expertId}?limit=${limit}`);\r\n      return response.data.data;\r\n    } catch (error: any) {\r\n      console.error('Failed to get similar experts:', error);\r\n\r\n      // Return empty response\r\n      return {\r\n        similar_experts: [],\r\n        reference_expert: {\r\n          id: expertId,\r\n          name: 'Unknown Expert'\r\n        }\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Refresh recommendations cache for the current user\r\n   */\r\n  async refreshRecommendations(algorithm: string = 'hybrid'): Promise<boolean> {\r\n    try {\r\n      await api.post(`${this.baseUrl}/refresh`, { body: { algorithm } });\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Failed to refresh recommendations:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Track user interaction with recommendations\r\n   */\r\n  async trackInteraction(data: InteractionData): Promise<boolean> {\r\n    try {\r\n      await api.post(`${this.baseUrl}/track`, { body: data });\r\n      return true;\r\n    } catch (error: any) {\r\n      console.error('Failed to track interaction:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get recommendation service health status\r\n   */\r\n  async getServiceHealth(): Promise<ServiceHealth> {\r\n    try {\r\n      const response = await api.get(`${this.baseUrl}/health`);\r\n      return response.data.data;\r\n    } catch (error: any) {\r\n      console.error('Failed to get service health:', error);\r\n      return {\r\n        status: 'unhealthy',\r\n        error: error.response?.data?.message || 'Service unavailable'\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Track recommendation click with position and algorithm info\r\n   */\r\n  async trackRecommendationClick(\r\n    expertId: number,\r\n    position: number,\r\n    algorithm: string,\r\n    metadata?: Record<string, any>\r\n  ): Promise<void> {\r\n    await this.trackInteraction({\r\n      expert_id: expertId,\r\n      interaction_type: 'click',\r\n      recommendation_position: position,\r\n      algorithm_used: algorithm,\r\n      metadata\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Track recommendation dismissal\r\n   */\r\n  async trackRecommendationDismiss(\r\n    expertId: number,\r\n    position: number,\r\n    algorithm: string,\r\n    reason?: string\r\n  ): Promise<void> {\r\n    await this.trackInteraction({\r\n      expert_id: expertId,\r\n      interaction_type: 'dismiss',\r\n      recommendation_position: position,\r\n      algorithm_used: algorithm,\r\n      metadata: { dismiss_reason: reason }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Track when user favorites an expert from recommendations\r\n   */\r\n  async trackRecommendationFavorite(\r\n    expertId: number,\r\n    position: number,\r\n    algorithm: string\r\n  ): Promise<void> {\r\n    await this.trackInteraction({\r\n      expert_id: expertId,\r\n      interaction_type: 'favorite',\r\n      recommendation_position: position,\r\n      algorithm_used: algorithm\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Track when user starts chat from recommendations\r\n   */\r\n  async trackRecommendationChatStart(\r\n    expertId: number,\r\n    position: number,\r\n    algorithm: string\r\n  ): Promise<void> {\r\n    await this.trackInteraction({\r\n      expert_id: expertId,\r\n      interaction_type: 'chat_start',\r\n      recommendation_position: position,\r\n      algorithm_used: algorithm\r\n    });\r\n  }\r\n}\r\n\r\nexport const recommendationService = new RecommendationService();"], "names": [], "mappings": ";;;;AAAA;;;AAqEA,MAAM;IAGJ;;GAEC,GACD,MAAM,iCAKmC;YALJ,UAAA,iEAKjC,CAAC;QACH,IAAI;YACF,MAAM,SAAS,IAAI;YAEnB,IAAI,QAAQ,KAAK,EAAE,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;YAChE,IAAI,QAAQ,SAAS,EAAE,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACnE,IAAI,QAAQ,QAAQ,EAAE,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAChE,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjD,OAAO,MAAM,CAAC,WAAW,QAAQ,OAAO,CAAC,IAAI,CAAC;YAChD;YAEA,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,AAAC,GAA+B,OAA7B,IAAI,CAAC,OAAO,EAAC,kBAAkC,OAAlB,OAAO,QAAQ;YAC9E,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAY;gBAWV,sBAAA;YAVT,QAAQ,KAAK,CAAC,+CAA+C;YAE7D,2BAA2B;YAC3B,OAAO;gBACL,iBAAiB,EAAE;gBACnB,gBAAgB;gBAChB,iBAAiB;gBACjB,WAAW;gBACX,cAAc,IAAI,OAAO,WAAW;gBACpC,UAAU;gBACV,OAAO,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YAC1C;QACF;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,QAAgB,EAAuD;YAArD,QAAA,iEAAgB;QACxD,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,AAAC,GAA0B,OAAxB,IAAI,CAAC,OAAO,EAAC,aAA6B,OAAlB,UAAS,WAAe,OAAN;YAC5E,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAEhD,wBAAwB;YACxB,OAAO;gBACL,iBAAiB,EAAE;gBACnB,kBAAkB;oBAChB,IAAI;oBACJ,MAAM;gBACR;YACF;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBAAuE;YAAhD,YAAA,iEAAoB;QAC/C,IAAI;YACF,MAAM,oHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,aAAW;gBAAE,MAAM;oBAAE;gBAAU;YAAE;YAChE,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,iBAAiB,IAAqB,EAAoB;QAC9D,IAAI;YACF,MAAM,oHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC,WAAS;gBAAE,MAAM;YAAK;YACrD,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,mBAA2C;QAC/C,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,AAAC,GAAe,OAAb,IAAI,CAAC,OAAO,EAAC;YAC/C,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAY;gBAIV,sBAAA;YAHT,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,QAAQ;gBACR,OAAO,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO,KAAI;YAC1C;QACF;IACF;IAEA;;GAEC,GACD,MAAM,yBACJ,QAAgB,EAChB,QAAgB,EAChB,SAAiB,EACjB,QAA8B,EACf;QACf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,WAAW;YACX,kBAAkB;YAClB,yBAAyB;YACzB,gBAAgB;YAChB;QACF;IACF;IAEA;;GAEC,GACD,MAAM,2BACJ,QAAgB,EAChB,QAAgB,EAChB,SAAiB,EACjB,MAAe,EACA;QACf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,WAAW;YACX,kBAAkB;YAClB,yBAAyB;YACzB,gBAAgB;YAChB,UAAU;gBAAE,gBAAgB;YAAO;QACrC;IACF;IAEA;;GAEC,GACD,MAAM,4BACJ,QAAgB,EAChB,QAAgB,EAChB,SAAiB,EACF;QACf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,WAAW;YACX,kBAAkB;YAClB,yBAAyB;YACzB,gBAAgB;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,6BACJ,QAAgB,EAChB,QAAgB,EAChB,SAAiB,EACF;QACf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,WAAW;YACX,kBAAkB;YAClB,yBAAyB;YACzB,gBAAgB;QAClB;IACF;;QAxKA,+KAAQ,WAAU;;AAyKpB;AAEO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/hooks/useRecommendations.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\r\nimport { recommendationService, Recommendation, RecommendationResponse, SimilarExpert, SimilarExpertsResponse } from '@/lib/recommendationService';\r\n\r\nexport interface UseRecommendationsOptions {\r\n  limit?: number;\r\n  algorithm?: 'hybrid' | 'collaborative' | 'content' | 'trending';\r\n  category?: string;\r\n  exclude?: number[];\r\n  autoRefresh?: boolean;\r\n  refreshInterval?: number; // in milliseconds\r\n}\r\n\r\nexport interface UseRecommendationsReturn {\r\n  recommendations: Recommendation[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  algorithmUsed: string;\r\n  totalAvailable: number;\r\n  isFallback: boolean;\r\n  lastUpdated: string | null;\r\n  refresh: () => Promise<void>;\r\n  trackClick: (expertId: number, position: number) => Promise<void>;\r\n  trackDismiss: (expertId: number, position: number, reason?: string) => Promise<void>;\r\n  trackFavorite: (expertId: number, position: number) => Promise<void>;\r\n  trackChatStart: (expertId: number, position: number) => Promise<void>;\r\n}\r\n\r\nexport function useRecommendations(options: UseRecommendationsOptions = {}): UseRecommendationsReturn {\r\n  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [algorithmUsed, setAlgorithmUsed] = useState<string>('hybrid');\r\n  const [totalAvailable, setTotalAvailable] = useState<number>(0);\r\n  const [isFallback, setIsFallback] = useState<boolean>(false);\r\n  const [lastUpdated, setLastUpdated] = useState<string | null>(null);\r\n\r\n  const fetchRecommendations = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const response: RecommendationResponse = await recommendationService.getPersonalizedRecommendations(options);\r\n      \r\n      setRecommendations(response.recommendations);\r\n      setAlgorithmUsed(response.algorithm_used);\r\n      setTotalAvailable(response.total_available);\r\n      setIsFallback(response.fallback || false);\r\n      setLastUpdated(response.generated_at);\r\n      \r\n      if (response.error) {\r\n        setError(response.error);\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to load recommendations');\r\n      setRecommendations([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [options]);\r\n\r\n  const refresh = useCallback(async () => {\r\n    await fetchRecommendations();\r\n  }, [fetchRecommendations]);\r\n\r\n  const trackClick = useCallback(async (expertId: number, position: number) => {\r\n    await recommendationService.trackRecommendationClick(expertId, position, algorithmUsed);\r\n  }, [algorithmUsed]);\r\n\r\n  const trackDismiss = useCallback(async (expertId: number, position: number, reason?: string) => {\r\n    await recommendationService.trackRecommendationDismiss(expertId, position, algorithmUsed, reason);\r\n  }, [algorithmUsed]);\r\n\r\n  const trackFavorite = useCallback(async (expertId: number, position: number) => {\r\n    await recommendationService.trackRecommendationFavorite(expertId, position, algorithmUsed);\r\n  }, [algorithmUsed]);\r\n\r\n  const trackChatStart = useCallback(async (expertId: number, position: number) => {\r\n    await recommendationService.trackRecommendationChatStart(expertId, position, algorithmUsed);\r\n  }, [algorithmUsed]);\r\n\r\n  // Initial load\r\n  useEffect(() => {\r\n    fetchRecommendations();\r\n  }, [fetchRecommendations]);\r\n\r\n  // Auto refresh\r\n  useEffect(() => {\r\n    if (options.autoRefresh && options.refreshInterval) {\r\n      const interval = setInterval(fetchRecommendations, options.refreshInterval);\r\n      return () => clearInterval(interval);\r\n    }\r\n  }, [options.autoRefresh, options.refreshInterval, fetchRecommendations]);\r\n\r\n  return {\r\n    recommendations,\r\n    loading,\r\n    error,\r\n    algorithmUsed,\r\n    totalAvailable,\r\n    isFallback,\r\n    lastUpdated,\r\n    refresh,\r\n    trackClick,\r\n    trackDismiss,\r\n    trackFavorite,\r\n    trackChatStart\r\n  };\r\n}\r\n\r\nexport interface UseSimilarExpertsOptions {\r\n  expertId: number;\r\n  limit?: number;\r\n  enabled?: boolean;\r\n}\r\n\r\nexport interface UseSimilarExpertsReturn {\r\n  similarExperts: SimilarExpert[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  referenceExpert: { id: number; name: string; category?: string } | null;\r\n  refresh: () => Promise<void>;\r\n  trackClick: (expertId: number, position: number) => Promise<void>;\r\n}\r\n\r\nexport function useSimilarExperts(options: UseSimilarExpertsOptions): UseSimilarExpertsReturn {\r\n  const [similarExperts, setSimilarExperts] = useState<SimilarExpert[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [referenceExpert, setReferenceExpert] = useState<{ id: number; name: string; category?: string } | null>(null);\r\n\r\n  const fetchSimilarExperts = useCallback(async () => {\r\n    if (!options.expertId || options.enabled === false) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const response: SimilarExpertsResponse = await recommendationService.getSimilarExperts(\r\n        options.expertId, \r\n        options.limit || 10\r\n      );\r\n      \r\n      setSimilarExperts(response.similar_experts);\r\n      setReferenceExpert(response.reference_expert);\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to load similar experts');\r\n      setSimilarExperts([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [options.expertId, options.limit, options.enabled]);\r\n\r\n  const refresh = useCallback(async () => {\r\n    await fetchSimilarExperts();\r\n  }, [fetchSimilarExperts]);\r\n\r\n  const trackClick = useCallback(async (expertId: number, position: number) => {\r\n    await recommendationService.trackInteraction({\r\n      expert_id: expertId,\r\n      interaction_type: 'click',\r\n      recommendation_position: position,\r\n      algorithm_used: 'similar',\r\n      metadata: { reference_expert_id: options.expertId }\r\n    });\r\n  }, [options.expertId]);\r\n\r\n  // Initial load\r\n  useEffect(() => {\r\n    fetchSimilarExperts();\r\n  }, [fetchSimilarExperts]);\r\n\r\n  return {\r\n    similarExperts,\r\n    loading,\r\n    error,\r\n    referenceExpert,\r\n    refresh,\r\n    trackClick\r\n  };\r\n}\r\n\r\nexport interface UseRecommendationHealthReturn {\r\n  health: {\r\n    status: 'healthy' | 'unhealthy';\r\n    database?: { connected: boolean; response_time_ms: number };\r\n    cache?: { connected: boolean; response_time_ms: number };\r\n    statistics?: {\r\n      total_users: number;\r\n      total_experts: number;\r\n      total_interactions: number;\r\n      cache_hit_rate: number;\r\n    };\r\n    last_model_update?: string;\r\n    error?: string;\r\n  };\r\n  loading: boolean;\r\n  refresh: () => Promise<void>;\r\n}\r\n\r\nexport function useRecommendationHealth(): UseRecommendationHealthReturn {\r\n  const [health, setHealth] = useState<any>({ status: 'unhealthy' });\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  const fetchHealth = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      const healthData = await recommendationService.getServiceHealth();\r\n      setHealth(healthData);\r\n    } catch (err: any) {\r\n      setHealth({ \r\n        status: 'unhealthy', \r\n        error: err.message || 'Failed to check service health' \r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const refresh = useCallback(async () => {\r\n    await fetchHealth();\r\n  }, [fetchHealth]);\r\n\r\n  useEffect(() => {\r\n    fetchHealth();\r\n  }, [fetchHealth]);\r\n\r\n  return {\r\n    health,\r\n    loading,\r\n    refresh\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AA0BO,SAAS;QAAmB,UAAA,iEAAqC,CAAC;;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE;YACvC,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,WAAmC,MAAM,sIAAA,CAAA,wBAAqB,CAAC,8BAA8B,CAAC;gBAEpG,mBAAmB,SAAS,eAAe;gBAC3C,iBAAiB,SAAS,cAAc;gBACxC,kBAAkB,SAAS,eAAe;gBAC1C,cAAc,SAAS,QAAQ,IAAI;gBACnC,eAAe,SAAS,YAAY;gBAEpC,IAAI,SAAS,KAAK,EAAE;oBAClB,SAAS,SAAS,KAAK;gBACzB;YACF,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO,IAAI;gBACxB,mBAAmB,EAAE;YACvB,SAAU;gBACR,WAAW;YACb;QACF;+DAAG;QAAC;KAAQ;IAEZ,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC1B,MAAM;QACR;kDAAG;QAAC;KAAqB;IAEzB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,OAAO,UAAkB;YACtD,MAAM,sIAAA,CAAA,wBAAqB,CAAC,wBAAwB,CAAC,UAAU,UAAU;QAC3E;qDAAG;QAAC;KAAc;IAElB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAAO,UAAkB,UAAkB;YAC1E,MAAM,sIAAA,CAAA,wBAAqB,CAAC,0BAA0B,CAAC,UAAU,UAAU,eAAe;QAC5F;uDAAG;QAAC;KAAc;IAElB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO,UAAkB;YACzD,MAAM,sIAAA,CAAA,wBAAqB,CAAC,2BAA2B,CAAC,UAAU,UAAU;QAC9E;wDAAG;QAAC;KAAc;IAElB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO,UAAkB;YAC1D,MAAM,sIAAA,CAAA,wBAAqB,CAAC,4BAA4B,CAAC,UAAU,UAAU;QAC/E;yDAAG;QAAC;KAAc;IAElB,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG;QAAC;KAAqB;IAEzB,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,QAAQ,WAAW,IAAI,QAAQ,eAAe,EAAE;gBAClD,MAAM,WAAW,YAAY,sBAAsB,QAAQ,eAAe;gBAC1E;oDAAO,IAAM,cAAc;;YAC7B;QACF;uCAAG;QAAC,QAAQ,WAAW;QAAE,QAAQ,eAAe;QAAE;KAAqB;IAEvE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAhFgB;AAiGT,SAAS,kBAAkB,OAAiC;;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0D;IAE/G,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACtC,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,OAAO,KAAK,OAAO;YAEpD,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,WAAmC,MAAM,sIAAA,CAAA,wBAAqB,CAAC,iBAAiB,CACpF,QAAQ,QAAQ,EAChB,QAAQ,KAAK,IAAI;gBAGnB,kBAAkB,SAAS,eAAe;gBAC1C,mBAAmB,SAAS,gBAAgB;YAC9C,EAAE,OAAO,KAAU;gBACjB,SAAS,IAAI,OAAO,IAAI;gBACxB,kBAAkB,EAAE;YACtB,SAAU;gBACR,WAAW;YACb;QACF;6DAAG;QAAC,QAAQ,QAAQ;QAAE,QAAQ,KAAK;QAAE,QAAQ,OAAO;KAAC;IAErD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC1B,MAAM;QACR;iDAAG;QAAC;KAAoB;IAExB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,OAAO,UAAkB;YACtD,MAAM,sIAAA,CAAA,wBAAqB,CAAC,gBAAgB,CAAC;gBAC3C,WAAW;gBACX,kBAAkB;gBAClB,yBAAyB;gBACzB,gBAAgB;gBAChB,UAAU;oBAAE,qBAAqB,QAAQ,QAAQ;gBAAC;YACpD;QACF;oDAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG;QAAC;KAAoB;IAExB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IAvDgB;AA2ET,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;QAAE,QAAQ;IAAY;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YAC9B,IAAI;gBACF,WAAW;gBACX,MAAM,aAAa,MAAM,sIAAA,CAAA,wBAAqB,CAAC,gBAAgB;gBAC/D,UAAU;YACZ,EAAE,OAAO,KAAU;gBACjB,UAAU;oBACR,QAAQ;oBACR,OAAO,IAAI,OAAO,IAAI;gBACxB;YACF,SAAU;gBACR,WAAW;YACb;QACF;2DAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAC1B,MAAM;QACR;uDAAG;QAAC;KAAY;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR;QACF;4CAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;IACF;AACF;IAhCgB", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,KAGqB;QAHrB,EAChB,SAAS,EACT,GAAG,OACkC,GAHrB;IAIhB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,6JAAA,CAAA,aAAgB,MAG5B,QAAmC;QAAlC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;yBACjC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/hooks/use-toast.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\n\nexport interface Toast {\n  id: string;\n  title?: string;\n  description?: string;\n  variant?: 'default' | 'destructive';\n  duration?: number;\n}\n\ninterface ToastState {\n  toasts: Toast[];\n}\n\nconst toastState: ToastState = {\n  toasts: []\n};\n\nlet listeners: Array<(state: ToastState) => void> = [];\n\nfunction emitChange() {\n  listeners.forEach(listener => listener(toastState));\n}\n\nfunction addToast(toast: Omit<Toast, 'id'>) {\n  const id = Math.random().toString(36).substr(2, 9);\n  const newToast: Toast = {\n    id,\n    duration: 5000,\n    ...toast\n  };\n  \n  toastState.toasts.push(newToast);\n  emitChange();\n  \n  // Auto remove toast after duration\n  if (newToast.duration && newToast.duration > 0) {\n    setTimeout(() => {\n      removeToast(id);\n    }, newToast.duration);\n  }\n  \n  return id;\n}\n\nfunction removeToast(id: string) {\n  const index = toastState.toasts.findIndex(toast => toast.id === id);\n  if (index > -1) {\n    toastState.toasts.splice(index, 1);\n    emitChange();\n  }\n}\n\nexport function useToast() {\n  const [state, setState] = useState<ToastState>(toastState);\n  \n  const subscribe = useCallback((listener: (state: ToastState) => void) => {\n    listeners.push(listener);\n    return () => {\n      listeners = listeners.filter(l => l !== listener);\n    };\n  }, []);\n  \n  const toast = useCallback((props: Omit<Toast, 'id'>) => {\n    return addToast(props);\n  }, []);\n  \n  const dismiss = useCallback((id: string) => {\n    removeToast(id);\n  }, []);\n  \n  // Subscribe to state changes\n  useState(() => {\n    const unsubscribe = subscribe(setState);\n    return unsubscribe;\n  });\n  \n  return {\n    toast,\n    dismiss,\n    toasts: state.toasts\n  };\n}\n\n// Toast function for direct usage\nexport const toast = (props: Omit<Toast, 'id'>) => {\n  return addToast(props);\n};"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AAgBA,MAAM,aAAyB;IAC7B,QAAQ,EAAE;AACZ;AAEA,IAAI,YAAgD,EAAE;AAEtD,SAAS;IACP,UAAU,OAAO,CAAC,CAAA,WAAY,SAAS;AACzC;AAEA,SAAS,SAAS,KAAwB;IACxC,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAChD,MAAM,WAAkB;QACtB;QACA,UAAU;QACV,GAAG,KAAK;IACV;IAEA,WAAW,MAAM,CAAC,IAAI,CAAC;IACvB;IAEA,mCAAmC;IACnC,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,GAAG,GAAG;QAC9C,WAAW;YACT,YAAY;QACd,GAAG,SAAS,QAAQ;IACtB;IAEA,OAAO;AACT;AAEA,SAAS,YAAY,EAAU;IAC7B,MAAM,QAAQ,WAAW,MAAM,CAAC,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAChE,IAAI,QAAQ,CAAC,GAAG;QACd,WAAW,MAAM,CAAC,MAAM,CAAC,OAAO;QAChC;IACF;AACF;AAEO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAE/C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC;YAC7B,UAAU,IAAI,CAAC;YACf;mDAAO;oBACL,YAAY,UAAU,MAAM;2DAAC,CAAA,IAAK,MAAM;;gBAC1C;;QACF;0CAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uCAAE,CAAC;YACzB,OAAO,SAAS;QAClB;sCAAG,EAAE;IAEL,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yCAAE,CAAC;YAC3B,YAAY;QACd;wCAAG,EAAE;IAEL,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;6BAAE;YACP,MAAM,cAAc,UAAU;YAC9B,OAAO;QACT;;IAEA,OAAO;QACL;QACA;QACA,QAAQ,MAAM,MAAM;IACtB;AACF;GA7BgB;AAgCT,MAAM,QAAQ,CAAC;IACpB,OAAO,SAAS;AAClB", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/RecommendationSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useRecommendations } from '@/hooks/useRecommendations';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { \r\n  Star, \r\n  MessageCircle, \r\n  RefreshCw, \r\n  X, \r\n  Heart, \r\n  Info,\r\n  TrendingUp,\r\n  Users,\r\n  Brain,\r\n  Sparkles\r\n} from 'lucide-react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useToast } from '@/hooks/use-toast';\r\n\r\ninterface RecommendationSectionProps {\r\n  limit?: number;\r\n  algorithm?: 'hybrid' | 'collaborative' | 'content' | 'trending';\r\n  category?: string;\r\n  exclude?: number[];\r\n  title?: string;\r\n  description?: string;\r\n  showAlgorithmInfo?: boolean;\r\n  showRefreshButton?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst algorithmIcons = {\r\n  hybrid: Sparkles,\r\n  collaborative: Users,\r\n  content: Brain,\r\n  trending: TrendingUp\r\n};\r\n\r\nconst algorithmDescriptions = {\r\n  hybrid: 'Combines multiple recommendation techniques for best results',\r\n  collaborative: 'Based on what similar users liked',\r\n  content: 'Based on expert characteristics and your preferences',\r\n  trending: 'Currently popular experts on the platform'\r\n};\r\n\r\nexport function RecommendationSection({\r\n  limit = 10,\r\n  algorithm = 'hybrid',\r\n  category,\r\n  exclude = [],\r\n  title = 'Recommended for You',\r\n  description = 'Experts we think you\\'ll love',\r\n  showAlgorithmInfo = true,\r\n  showRefreshButton = true,\r\n  className = ''\r\n}: RecommendationSectionProps) {\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n  const [dismissedExperts, setDismissedExperts] = useState<Set<number>>(new Set());\r\n\r\n  const {\r\n    recommendations,\r\n    loading,\r\n    error,\r\n    algorithmUsed,\r\n    totalAvailable,\r\n    isFallback,\r\n    lastUpdated,\r\n    refresh,\r\n    trackClick,\r\n    trackDismiss,\r\n    trackFavorite,\r\n    trackChatStart\r\n  } = useRecommendations({\r\n    limit,\r\n    algorithm,\r\n    category,\r\n    exclude: [...exclude, ...Array.from(dismissedExperts)]\r\n  });\r\n\r\n  const handleExpertClick = async (expertId: number, position: number) => {\r\n    await trackClick(expertId, position);\r\n    router.push(`/experts/${expertId}`);\r\n  };\r\n\r\n  const handleChatClick = async (expertId: number, position: number, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    await trackChatStart(expertId, position);\r\n    router.push(`/chat?expertId=${expertId}`);\r\n  };\r\n\r\n  const handleDismiss = async (expertId: number, position: number, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    \r\n    setDismissedExperts(prev => new Set([...prev, expertId]));\r\n    await trackDismiss(expertId, position, 'user_dismissed');\r\n    \r\n    toast({\r\n      title: 'Expert dismissed',\r\n      description: 'We\\'ll show you different recommendations.',\r\n    });\r\n  };\r\n\r\n  const handleFavorite = async (expertId: number, position: number, e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    await trackFavorite(expertId, position);\r\n    \r\n    toast({\r\n      title: 'Added to favorites',\r\n      description: 'This expert has been added to your favorites.',\r\n    });\r\n  };\r\n\r\n  const handleRefresh = async () => {\r\n    setDismissedExperts(new Set());\r\n    await refresh();\r\n    \r\n    toast({\r\n      title: 'Recommendations refreshed',\r\n      description: 'We\\'ve updated your recommendations.',\r\n    });\r\n  };\r\n\r\n  const AlgorithmIcon = algorithmIcons[algorithmUsed as keyof typeof algorithmIcons] || Sparkles;\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className={`space-y-4 ${className}`}>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <Skeleton className=\"h-6 w-48 mb-2\" />\r\n            <Skeleton className=\"h-4 w-64\" />\r\n          </div>\r\n          {showRefreshButton && <Skeleton className=\"h-9 w-24\" />}\r\n        </div>\r\n        \r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {Array.from({ length: limit }).map((_, i) => (\r\n            <Card key={i} className=\"h-48\">\r\n              <CardHeader>\r\n                <Skeleton className=\"h-5 w-3/4\" />\r\n                <Skeleton className=\"h-4 w-full\" />\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-2\">\r\n                  <Skeleton className=\"h-4 w-1/2\" />\r\n                  <Skeleton className=\"h-4 w-2/3\" />\r\n                  <Skeleton className=\"h-8 w-full\" />\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error && !isFallback) {\r\n    return (\r\n      <div className={className}>\r\n        <Alert>\r\n          <Info className=\"h-4 w-4\" />\r\n          <AlertDescription>\r\n            {error}. {showRefreshButton && (\r\n              <Button \r\n                variant=\"link\" \r\n                className=\"p-0 h-auto\" \r\n                onClick={handleRefresh}\r\n              >\r\n                Try again\r\n              </Button>\r\n            )}\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (recommendations.length === 0) {\r\n    return (\r\n      <div className={className}>\r\n        <div className=\"text-center py-8\">\r\n          <AlgorithmIcon className=\"h-12 w-12 mx-auto text-muted-foreground mb-4\" />\r\n          <h3 className=\"text-lg font-medium mb-2\">No recommendations available</h3>\r\n          <p className=\"text-muted-foreground mb-4\">\r\n            We don't have enough data to make personalized recommendations yet.\r\n          </p>\r\n          {showRefreshButton && (\r\n            <Button onClick={handleRefresh} variant=\"outline\">\r\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n              Try Again\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <h2 className=\"text-xl font-semibold\">{title}</h2>\r\n            {isFallback && (\r\n              <Badge variant=\"secondary\" className=\"text-xs\">\r\n                Fallback\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <p className=\"text-muted-foreground text-sm\">{description}</p>\r\n          \r\n          {showAlgorithmInfo && (\r\n            <div className=\"flex items-center gap-2 mt-2 text-xs text-muted-foreground\">\r\n              <AlgorithmIcon className=\"h-3 w-3\" />\r\n              <span>{algorithmDescriptions[algorithmUsed as keyof typeof algorithmDescriptions]}</span>\r\n              {totalAvailable > recommendations.length && (\r\n                <span>• {totalAvailable - recommendations.length} more available</span>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        {showRefreshButton && (\r\n          <Button \r\n            onClick={handleRefresh} \r\n            variant=\"outline\" \r\n            size=\"sm\"\r\n            disabled={loading}\r\n          >\r\n            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\r\n            Refresh\r\n          </Button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recommendations Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n        {recommendations.map((recommendation, index) => (\r\n          <Card \r\n            key={recommendation.expert_id}\r\n            className=\"cursor-pointer hover:shadow-md transition-shadow relative group\"\r\n            onClick={() => handleExpertClick(recommendation.expert_id, index)}\r\n          >\r\n            {/* Dismiss Button */}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10 h-6 w-6 p-0\"\r\n              onClick={(e) => handleDismiss(recommendation.expert_id, index, e)}\r\n            >\r\n              <X className=\"h-3 w-3\" />\r\n            </Button>\r\n\r\n            <CardHeader className=\"pb-3\">\r\n              <div className=\"flex items-start justify-between\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <CardTitle className=\"text-base line-clamp-1\">\r\n                    {recommendation.name}\r\n                  </CardTitle>\r\n                  <CardDescription className=\"line-clamp-2 text-xs mt-1\">\r\n                    {recommendation.description}\r\n                  </CardDescription>\r\n                </div>\r\n              </div>\r\n              \r\n              {/* Category and Score */}\r\n              <div className=\"flex items-center gap-2 mt-2\">\r\n                <Badge variant=\"secondary\" className=\"text-xs\">\r\n                  {recommendation.category}\r\n                </Badge>\r\n                <div className=\"text-xs text-muted-foreground\">\r\n                  {Math.round(recommendation.score * 100)}% match\r\n                </div>\r\n              </div>\r\n            </CardHeader>\r\n\r\n            <CardContent className=\"pt-0\">\r\n              {/* Stats */}\r\n              <div className=\"flex items-center gap-4 text-xs text-muted-foreground mb-3\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Star className=\"h-3 w-3 fill-current text-yellow-400\" />\r\n                  <span>{recommendation.average_rating.toFixed(1)}</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <MessageCircle className=\"h-3 w-3\" />\r\n                  <span>{recommendation.total_chats}</span>\r\n                </div>\r\n                <div className=\"text-xs\">\r\n                  {recommendation.price_per_message}% fee\r\n                </div>\r\n              </div>\r\n\r\n              {/* Reason */}\r\n              <p className=\"text-xs text-muted-foreground mb-3 line-clamp-2\">\r\n                {recommendation.reason}\r\n              </p>\r\n\r\n              {/* Actions */}\r\n              <div className=\"flex gap-2\">\r\n                <Button \r\n                  size=\"sm\" \r\n                  className=\"flex-1\"\r\n                  onClick={(e) => handleChatClick(recommendation.expert_id, index, e)}\r\n                >\r\n                  <MessageCircle className=\"h-3 w-3 mr-1\" />\r\n                  Chat\r\n                </Button>\r\n                <Button \r\n                  size=\"sm\" \r\n                  variant=\"outline\"\r\n                  onClick={(e) => handleFavorite(recommendation.expert_id, index, e)}\r\n                >\r\n                  <Heart className=\"h-3 w-3\" />\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Footer Info */}\r\n      {lastUpdated && (\r\n        <p className=\"text-xs text-muted-foreground text-center\">\r\n          Last updated: {new Date(lastUpdated).toLocaleString()}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAtBA;;;;;;;;;;;AAoCA,MAAM,iBAAiB;IACrB,QAAQ,6MAAA,CAAA,WAAQ;IAChB,eAAe,uMAAA,CAAA,QAAK;IACpB,SAAS,uMAAA,CAAA,QAAK;IACd,UAAU,qNAAA,CAAA,aAAU;AACtB;AAEA,MAAM,wBAAwB;IAC5B,QAAQ;IACR,eAAe;IACf,SAAS;IACT,UAAU;AACZ;AAEO,SAAS,sBAAsB,KAUT;QAVS,EACpC,QAAQ,EAAE,EACV,YAAY,QAAQ,EACpB,QAAQ,EACR,UAAU,EAAE,EACZ,QAAQ,qBAAqB,EAC7B,cAAc,+BAA+B,EAC7C,oBAAoB,IAAI,EACxB,oBAAoB,IAAI,EACxB,YAAY,EAAE,EACa,GAVS;;IAWpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE1E,MAAM,EACJ,eAAe,EACf,OAAO,EACP,KAAK,EACL,aAAa,EACb,cAAc,EACd,UAAU,EACV,WAAW,EACX,OAAO,EACP,UAAU,EACV,YAAY,EACZ,aAAa,EACb,cAAc,EACf,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE;QACrB;QACA;QACA;QACA,SAAS;eAAI;eAAY,MAAM,IAAI,CAAC;SAAkB;IACxD;IAEA,MAAM,oBAAoB,OAAO,UAAkB;QACjD,MAAM,WAAW,UAAU;QAC3B,OAAO,IAAI,CAAC,AAAC,YAAoB,OAAT;IAC1B;IAEA,MAAM,kBAAkB,OAAO,UAAkB,UAAkB;QACjE,EAAE,eAAe;QACjB,MAAM,eAAe,UAAU;QAC/B,OAAO,IAAI,CAAC,AAAC,kBAA0B,OAAT;IAChC;IAEA,MAAM,gBAAgB,OAAO,UAAkB,UAAkB;QAC/D,EAAE,eAAe;QAEjB,oBAAoB,CAAA,OAAQ,IAAI,IAAI;mBAAI;gBAAM;aAAS;QACvD,MAAM,aAAa,UAAU,UAAU;QAEvC,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,OAAO,UAAkB,UAAkB;QAChE,EAAE,eAAe;QACjB,MAAM,cAAc,UAAU;QAE9B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,oBAAoB,IAAI;QACxB,MAAM;QAEN,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,cAAc,CAAC,cAA6C,IAAI,6MAAA,CAAA,WAAQ;IAE9F,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,AAAC,aAAsB,OAAV;;8BAC3B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC,uIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;wBAErB,mCAAqB,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;8BAG5C,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAM,GAAG,GAAG,CAAC,CAAC,GAAG,kBACrC,6LAAC,mIAAA,CAAA,OAAI;4BAAS,WAAU;;8CACtB,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;8CAEtB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BATf;;;;;;;;;;;;;;;;IAiBrB;IAEA,IAAI,SAAS,CAAC,YAAY;QACxB,qBACE,6LAAC;YAAI,WAAW;sBACd,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC,oIAAA,CAAA,mBAAgB;;4BACd;4BAAM;4BAAG,mCACR,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;0CACV;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;QAChC,qBACE,6LAAC;YAAI,WAAW;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAc,WAAU;;;;;;kCACzB,6LAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;oBAGzC,mCACC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAe,SAAQ;;0CACtC,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyB;;;;;;oCACtC,4BACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAU;;;;;;;;;;;;0CAKnD,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;4BAE7C,mCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAc,WAAU;;;;;;kDACzB,6LAAC;kDAAM,qBAAqB,CAAC,cAAoD;;;;;;oCAChF,iBAAiB,gBAAgB,MAAM,kBACtC,6LAAC;;4CAAK;4CAAG,iBAAiB,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;oBAMxD,mCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,UAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAW,AAAC,gBAA6C,OAA9B,UAAU,iBAAiB;;;;;;4BAAQ;;;;;;;;;;;;;0BAO/E,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,gBAAgB,sBACpC,6LAAC,mIAAA,CAAA,OAAI;wBAEH,WAAU;wBACV,SAAS,IAAM,kBAAkB,eAAe,SAAS,EAAE;;0CAG3D,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC,IAAM,cAAc,eAAe,SAAS,EAAE,OAAO;0CAE/D,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAGf,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAClB,eAAe,IAAI;;;;;;8DAEtB,6LAAC,mIAAA,CAAA,kBAAe;oDAAC,WAAU;8DACxB,eAAe,WAAW;;;;;;;;;;;;;;;;;kDAMjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAClC,eAAe,QAAQ;;;;;;0DAE1B,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,KAAK,CAAC,eAAe,KAAK,GAAG;oDAAK;;;;;;;;;;;;;;;;;;;0CAK9C,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAM,eAAe,cAAc,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;kEACzB,6LAAC;kEAAM,eAAe,WAAW;;;;;;;;;;;;0DAEnC,6LAAC;gDAAI,WAAU;;oDACZ,eAAe,iBAAiB;oDAAC;;;;;;;;;;;;;kDAKtC,6LAAC;wCAAE,WAAU;kDACV,eAAe,MAAM;;;;;;kDAIxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC,IAAM,gBAAgB,eAAe,SAAS,EAAE,OAAO;;kEAEjE,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG5C,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,CAAC,IAAM,eAAe,eAAe,SAAS,EAAE,OAAO;0DAEhE,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uBAzElB,eAAe,SAAS;;;;;;;;;;YAkFlC,6BACC,6LAAC;gBAAE,WAAU;;oBAA4C;oBACxC,IAAI,KAAK,aAAa,cAAc;;;;;;;;;;;;;AAK7D;GA7RgB;;QAWC,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,WAAQ;QAgBtB,qIAAA,CAAA,qBAAkB;;;KA5BR", "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ExpertMarketplace.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { api } from \"@/lib/api\";\r\nimport { asset } from \"@/lib/utils\";\r\nimport StarRating from \"@/components/ui/star-rating\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Search } from \"lucide-react\";\r\nimport { RecommendationSection } from \"@/components/RecommendationSection\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\n\r\ninterface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  systemPrompt: string;\r\n  model: string;\r\n  assistantId: string;\r\n  imageUrl?: string;\r\n  pricingPercentage: number;\r\n  isPublic: boolean;\r\n  labels: string[];\r\n  totalChats?: number;\r\n  totalRevenue?: number;\r\n  averageRating?: number;\r\n  totalReviews?: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nconst ExpertMarketplace: React.FC = () => {\r\n  const { user } = useAuth();\r\n  const [experts, setExperts] = useState<Expert[]>([]);\r\n  const [filteredExperts, setFilteredExperts] = useState<Expert[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [sortBy, setSortBy] = useState(\"name\");\r\n  const [filterByRating, setFilterByRating] = useState(\"all\");\r\n\r\n  const loadExperts = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      const result = await api.getPublicExperts();\r\n\r\n      if (result.success) {\r\n        setExperts(result.experts);\r\n        setFilteredExperts(result.experts);\r\n      } else {\r\n        setError(result.error || \"Failed to load experts\");\r\n      }\r\n    } catch (err: any) {\r\n      setError(err.message || \"Failed to load experts\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadExperts();\r\n  }, []);\r\n\r\n  // Filter and sort experts\r\n  useEffect(() => {\r\n    let filtered = [...experts];\r\n\r\n    // Apply search filter\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(expert =>\r\n        expert.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        expert.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        expert.labels.some(label => label.toLowerCase().includes(searchTerm.toLowerCase()))\r\n      );\r\n    }\r\n\r\n    // Apply rating filter\r\n    if (filterByRating !== \"all\") {\r\n      const minRating = parseFloat(filterByRating);\r\n      filtered = filtered.filter(expert =>\r\n        expert.averageRating && expert.averageRating >= minRating\r\n      );\r\n    }\r\n\r\n    // Apply sorting\r\n    filtered.sort((a, b) => {\r\n      switch (sortBy) {\r\n        case \"rating\":\r\n          return (b.averageRating || 0) - (a.averageRating || 0);\r\n        case \"reviews\":\r\n          return (b.totalReviews || 0) - (a.totalReviews || 0);\r\n        case \"chats\":\r\n          return (b.totalChats || 0) - (a.totalChats || 0);\r\n        case \"newest\":\r\n          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\r\n        case \"name\":\r\n        default:\r\n          return a.name.localeCompare(b.name);\r\n      }\r\n    });\r\n\r\n    setFilteredExperts(filtered);\r\n  }, [experts, searchTerm, sortBy, filterByRating]);\r\n\r\n  const getExpertIcon = (labels: string[]) => {\r\n    if (labels.includes(\"business\") || labels.includes(\"marketing\"))\r\n      return \"💼\";\r\n    if (labels.includes(\"code\") || labels.includes(\"programming\")) return \"💻\";\r\n    if (labels.includes(\"creative\") || labels.includes(\"design\")) return \"🎨\";\r\n    if (labels.includes(\"education\") || labels.includes(\"learning\"))\r\n      return \"📚\";\r\n    if (labels.includes(\"health\") || labels.includes(\"medical\")) return \"🏥\";\r\n    if (labels.includes(\"finance\") || labels.includes(\"money\")) return \"💰\";\r\n    return \"🤖\";\r\n  };\r\n\r\n  const truncateDescription = (\r\n    description: string,\r\n    maxLength: number = 120\r\n  ) => {\r\n    if (description.length <= maxLength) return description;\r\n    return description.substring(0, maxLength) + \"...\";\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        {[...Array(6)].map((_, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 animate-pulse\"\r\n          >\r\n            <div className=\"flex items-center space-x-4 mb-4\">\r\n              <div className=\"w-16 h-16 bg-gray-200 rounded-full\"></div>\r\n              <div className=\"flex-1\">\r\n                <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\r\n                <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-2 mb-4\">\r\n              <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n              <div className=\"h-3 bg-gray-200 rounded\"></div>\r\n              <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\r\n            </div>\r\n            <div className=\"h-10 bg-gray-200 rounded-lg\"></div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"text-center py-12\">\r\n        <div className=\"bg-red-50 border border-red-200 rounded-xl p-6 max-w-md mx-auto\">\r\n          <div className=\"text-red-600 text-4xl mb-4\">⚠️</div>\r\n          <h3 className=\"text-lg font-semibold text-red-800 mb-2\">\r\n            Unable to Load Experts\r\n          </h3>\r\n          <p className=\"text-red-600 mb-4\">{error}</p>\r\n          <button\r\n            onClick={loadExperts}\r\n            className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\r\n          >\r\n            Try Again\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (experts.length === 0) {\r\n    return (\r\n      <div className=\"text-center py-12\">\r\n        <div className=\"text-6xl mb-4\">🔍</div>\r\n        <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\r\n          No Public Experts Available\r\n        </h3>\r\n        <p className=\"text-gray-500\">Check back soon for new AI experts!</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Recommendations Section - Only show for authenticated users */}\r\n      {user && (\r\n        <RecommendationSection\r\n          limit={6}\r\n          algorithm=\"hybrid\"\r\n          exclude={filteredExperts.map(expert => expert.id)}\r\n          title=\"Recommended for You\"\r\n          description=\"AI experts we think you'll love based on your preferences\"\r\n          showAlgorithmInfo={true}\r\n          showRefreshButton={true}\r\n          className=\"mb-8\"\r\n        />\r\n      )}\r\n\r\n      {/* Search and Filter Controls */}\r\n      <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100\">\r\n        <div className=\"flex flex-col lg:flex-row gap-4\">\r\n          {/* Search */}\r\n          <div className=\"flex-1 relative\">\r\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Search experts by name, description, or tags...\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              className=\"pl-10\"\r\n            />\r\n          </div>\r\n\r\n          {/* Sort By */}\r\n          <div className=\"w-full lg:w-48\">\r\n            <Select value={sortBy} onValueChange={setSortBy}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Sort by\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"name\">Name (A-Z)</SelectItem>\r\n                <SelectItem value=\"rating\">Highest Rated</SelectItem>\r\n                <SelectItem value=\"reviews\">Most Reviews</SelectItem>\r\n                <SelectItem value=\"chats\">Most Popular</SelectItem>\r\n                <SelectItem value=\"newest\">Newest First</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Filter by Rating */}\r\n          <div className=\"w-full lg:w-48\">\r\n            <Select value={filterByRating} onValueChange={setFilterByRating}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Filter by rating\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"all\">All Ratings</SelectItem>\r\n                <SelectItem value=\"4\">4+ Stars</SelectItem>\r\n                <SelectItem value=\"3\">3+ Stars</SelectItem>\r\n                <SelectItem value=\"2\">2+ Stars</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Results Summary */}\r\n        <div className=\"mt-4 flex items-center justify-between text-sm text-gray-600\">\r\n          <span>\r\n            Showing {filteredExperts.length} of {experts.length} experts\r\n          </span>\r\n          {(searchTerm || filterByRating !== \"all\") && (\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => {\r\n                setSearchTerm(\"\");\r\n                setFilterByRating(\"all\");\r\n              }}\r\n            >\r\n              Clear Filters\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* No Results */}\r\n      {filteredExperts.length === 0 && (\r\n        <div className=\"text-center py-12\">\r\n          <div className=\"text-6xl mb-4\">🔍</div>\r\n          <h3 className=\"text-xl font-semibold text-gray-700 mb-2\">\r\n            No Experts Found\r\n          </h3>\r\n          <p className=\"text-gray-500\">Try adjusting your search or filters</p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Expert Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n        {filteredExperts.map((expert) => (\r\n          <div\r\n            key={expert.id}\r\n            className=\"bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl hover:scale-[1.02] transition-all duration-300 group\"\r\n          >\r\n            {/* Expert Header */}\r\n            <div className=\"flex items-center space-x-4 mb-4\">\r\n              <div className=\"relative\">\r\n                {expert.imageUrl ? (\r\n                  <Image\r\n                    src={asset(expert.imageUrl)}\r\n                    alt={expert.name}\r\n                    width={64}\r\n                    height={64}\r\n                    className=\"w-16 h-16 object-cover rounded-full border-2 border-gray-100\"\r\n                  />\r\n                ) : (\r\n                  <div\r\n                    className=\"w-16 h-16 rounded-full flex items-center justify-center text-2xl text-white shadow-lg\"\r\n                    style={{ backgroundColor: \"#1E3A8A\" }}\r\n                  >\r\n                    {getExpertIcon(expert.labels)}\r\n                  </div>\r\n                )}\r\n                <div className=\"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center\">\r\n                  <div className=\"w-2 h-2 bg-white rounded-full\"></div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex-1\">\r\n                <h3 className=\"font-bold text-lg text-gray-900 group-hover:text-blue-900 transition-colors\">\r\n                  {expert.name}\r\n                </h3>\r\n                <div className=\"flex items-center space-x-2 mb-1\">\r\n                  <span className=\"text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full font-medium\">\r\n                    {expert.model}\r\n                  </span>\r\n                  <span className=\"text-xs text-green-600 font-medium\">\r\n                    ● Online\r\n                  </span>\r\n                </div>\r\n                {/* Rating Display */}\r\n                {expert.averageRating && expert.averageRating > 0 ? (\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <StarRating rating={expert.averageRating} size=\"sm\" />\r\n                    <span className=\"text-xs text-gray-500\">\r\n                      ({expert.totalReviews} review{expert.totalReviews !== 1 ? 's' : ''})\r\n                    </span>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-xs text-gray-400\">No reviews yet</div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Description */}\r\n            <p className=\"text-gray-600 text-sm mb-4 leading-relaxed\">\r\n              {truncateDescription(expert.description)}\r\n            </p>\r\n\r\n            {/* Labels */}\r\n            {expert.labels && expert.labels.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-2 mb-4\">\r\n                {expert.labels.slice(0, 3).map((label, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-block px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full font-medium hover:bg-gray-200 transition-colors\"\r\n                  >\r\n                    #{label}\r\n                  </span>\r\n                ))}\r\n                {expert.labels.length > 3 && (\r\n                  <span className=\"inline-block px-3 py-1 text-xs text-gray-500 rounded-full\">\r\n                    +{expert.labels.length - 3} more\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"space-y-3\">\r\n              <Link\r\n                href={`/expert/${expert.id}`}\r\n                className=\"block w-full py-3 px-4 text-center font-semibold rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                style={{ backgroundColor: \"#1E3A8A\" }}\r\n              >\r\n                View Profile\r\n              </Link>\r\n\r\n              <Link\r\n                href={`/chat?expertId=${expert.id}`}\r\n                className=\"block w-full py-3 px-4 text-center font-semibold rounded-xl border-2 transition-all duration-200 hover:shadow-md\"\r\n                style={{ borderColor: \"#1E3A8A\", color: \"#1E3A8A\" }}\r\n              >\r\n                ⚡ Start Chat\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Stats and Pricing Info */}\r\n            <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n              <div className=\"flex items-center justify-between text-xs text-gray-500 mb-2\">\r\n                <span>💰 {expert.pricingPercentage}% of usage</span>\r\n                <span>🕒 Instant response</span>\r\n              </div>\r\n              {expert.totalChats && expert.totalChats > 0 && (\r\n                <div className=\"text-xs text-gray-400\">\r\n                  💬 {expert.totalChats} conversation{expert.totalChats !== 1 ? 's' : ''}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpertMarketplace;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAkCA,MAAM,oBAA8B;;IAClC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,cAAc;QAClB,IAAI;YACF,aAAa;YACb,SAAS;YACT,MAAM,SAAS,MAAM,oHAAA,CAAA,MAAG,CAAC,gBAAgB;YAEzC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,OAAO;gBACzB,mBAAmB,OAAO,OAAO;YACnC,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;QACF;sCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,WAAW;mBAAI;aAAQ;YAE3B,sBAAsB;YACtB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;mDAAC,CAAA,SACzB,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAChE,OAAO,MAAM,CAAC,IAAI;2DAAC,CAAA,QAAS,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;YAEnF;YAEA,sBAAsB;YACtB,IAAI,mBAAmB,OAAO;gBAC5B,MAAM,YAAY,WAAW;gBAC7B,WAAW,SAAS,MAAM;mDAAC,CAAA,SACzB,OAAO,aAAa,IAAI,OAAO,aAAa,IAAI;;YAEpD;YAEA,gBAAgB;YAChB,SAAS,IAAI;+CAAC,CAAC,GAAG;oBAChB,OAAQ;wBACN,KAAK;4BACH,OAAO,CAAC,EAAE,aAAa,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,IAAI,CAAC;wBACvD,KAAK;4BACH,OAAO,CAAC,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,IAAI,CAAC;wBACrD,KAAK;4BACH,OAAO,CAAC,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC;wBACjD,KAAK;4BACH,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;wBACxE,KAAK;wBACL;4BACE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;oBACtC;gBACF;;YAEA,mBAAmB;QACrB;sCAAG;QAAC;QAAS;QAAY;QAAQ;KAAe;IAEhD,MAAM,gBAAgB,CAAC;QACrB,IAAI,OAAO,QAAQ,CAAC,eAAe,OAAO,QAAQ,CAAC,cACjD,OAAO;QACT,IAAI,OAAO,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,gBAAgB,OAAO;QACtE,IAAI,OAAO,QAAQ,CAAC,eAAe,OAAO,QAAQ,CAAC,WAAW,OAAO;QACrE,IAAI,OAAO,QAAQ,CAAC,gBAAgB,OAAO,QAAQ,CAAC,aAClD,OAAO;QACT,IAAI,OAAO,QAAQ,CAAC,aAAa,OAAO,QAAQ,CAAC,YAAY,OAAO;QACpE,IAAI,OAAO,QAAQ,CAAC,cAAc,OAAO,QAAQ,CAAC,UAAU,OAAO;QACnE,OAAO;IACT;IAEA,MAAM,sBAAsB,SAC1B;YACA,6EAAoB;QAEpB,IAAI,YAAY,MAAM,IAAI,WAAW,OAAO;QAC5C,OAAO,YAAY,SAAS,CAAC,GAAG,aAAa;IAC/C;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6LAAC;oBAEC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAGnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;;;;;;mBAfV;;;;;;;;;;IAoBf;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;kCAGxD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAGzD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,sBACC,6LAAC,8IAAA,CAAA,wBAAqB;gBACpB,OAAO;gBACP,WAAU;gBACV,SAAS,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,EAAE;gBAChD,OAAM;gBACN,aAAY;gBACZ,mBAAmB;gBACnB,mBAAmB;gBACnB,WAAU;;;;;;0BAKd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAQ,eAAe;;sDACpC,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAgB,eAAe;;sDAC5C,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAK;oCACK,gBAAgB,MAAM;oCAAC;oCAAK,QAAQ,MAAM;oCAAC;;;;;;;4BAErD,CAAC,cAAc,mBAAmB,KAAK,mBACtC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP,cAAc;oCACd,kBAAkB;gCACpB;0CACD;;;;;;;;;;;;;;;;;;YAQN,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;wBAEC,WAAU;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,QAAQ,iBACd,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,QAAK,AAAD,EAAE,OAAO,QAAQ;gDAC1B,KAAK,OAAO,IAAI;gDAChB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;yGAGZ,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB;gDAAU;0DAEnC,cAAc,OAAO,MAAM;;;;;;0DAGhC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,OAAO,IAAI;;;;;;0DAEd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;4CAKtD,OAAO,aAAa,IAAI,OAAO,aAAa,GAAG,kBAC9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6IAAA,CAAA,UAAU;wDAAC,QAAQ,OAAO,aAAa;wDAAE,MAAK;;;;;;kEAC/C,6LAAC;wDAAK,WAAU;;4DAAwB;4DACpC,OAAO,YAAY;4DAAC;4DAAQ,OAAO,YAAY,KAAK,IAAI,MAAM;4DAAG;;;;;;;;;;;;yGAIvE,6LAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAM7C,6LAAC;gCAAE,WAAU;0CACV,oBAAoB,OAAO,WAAW;;;;;;4BAIxC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,mBACvC,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACrC,6LAAC;4CAEC,WAAU;;gDACX;gDACG;;2CAHG;;;;;oCAMR,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,6LAAC;wCAAK,WAAU;;4CAA4D;4CACxE,OAAO,MAAM,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;0CAOnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,AAAC,WAAoB,OAAV,OAAO,EAAE;wCAC1B,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAU;kDACrC;;;;;;kDAID,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,AAAC,kBAA2B,OAAV,OAAO,EAAE;wCACjC,WAAU;wCACV,OAAO;4CAAE,aAAa;4CAAW,OAAO;wCAAU;kDACnD;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAI,OAAO,iBAAiB;oDAAC;;;;;;;0DACnC,6LAAC;0DAAK;;;;;;;;;;;;oCAEP,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG,mBACxC,6LAAC;wCAAI,WAAU;;4CAAwB;4CACjC,OAAO,UAAU;4CAAC;4CAAc,OAAO,UAAU,KAAK,IAAI,MAAM;;;;;;;;;;;;;;uBAxGrE,OAAO,EAAE;;;;;;;;;;;;;;;;AAiH1B;GA5WM;;QACa,kIAAA,CAAA,UAAO;;;KADpB;uCA8WS", "debugId": null}}]}