(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{238:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},1007:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6616:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g});var a=s(5155),t=s(2115),l=s(5695),n=s(6874),o=s.n(n),i=s(283),d=s(1007),c=s(8883),m=s(9420),u=s(2919),h=s(8749),x=s(2657),f=s(238),b=s(2138);let p=()=>{let e=(0,l.useSearchParams)(),[r,s]=(0,t.useState)({name:"",email:"",phone:"",password:"",confirmPassword:"",referralCode:""}),[n,p]=(0,t.useState)(!1),[g,y]=(0,t.useState)(!1),[j,w]=(0,t.useState)(!1),[v,N]=(0,t.useState)(""),[k,C]=(0,t.useState)(!1),[A,P]=(0,t.useState)(""),[S,E]=(0,t.useState)(""),[F,L]=(0,t.useState)(!1),{register:_}=(0,i.A)();(0,t.useEffect)(()=>{let r=e.get("ref");r?(s(e=>({...e,referralCode:r})),M(r)):q()},[e]);let q=async()=>{try{let e=await fetch("/api/affiliate/visitor-info",{credentials:"include"});if(e.ok){let r=await e.json();r.hasTracking&&r.referralCode&&(s(e=>({...e,referralCode:r.referralCode})),M(r.referralCode))}}catch(e){console.error("Error checking affiliate tracking:",e)}},M=async e=>{if(e)try{L(!0);let r=await fetch("/api/affiliate/validate/".concat(e));if(r.ok){let e=await r.json();E(e.referrer.name)}else E("")}catch(e){console.error("Error validating referral code:",e),E("")}finally{L(!1)}},O=e=>{let{name:r,value:a}=e.target;s(e=>({...e,[r]:a})),v&&N("")},I=async e=>{if(e.preventDefault(),r.name.trim()?r.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)?r.phone.trim()?/^\+?[1-9]\d{1,14}$/.test(r.phone)?r.password?r.password.length<6?(N("Password must be at least 6 characters long"),!1):r.password===r.confirmPassword||(N("Passwords do not match"),!1):(N("Password is required"),!1):(N("Please enter a valid phone number (e.g., +6281234567890)"),!1):(N("Phone number is required"),!1):(N("Please enter a valid email address"),!1):(N("Email is required"),!1):(N("Name is required"),!1)){w(!0),N("");try{let e={name:r.name.trim(),email:r.email.trim(),phone:r.phone.trim(),password:r.password};r.referralCode.trim()&&(e.referralCode=r.referralCode.trim());let s=await _(e);C(!0),P(s.verification.whatsappLink)}catch(e){N(e.message||"Registration failed. Please try again.")}finally{w(!1)}}};return k?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4",children:(0,a.jsx)("div",{className:"max-w-md w-full",children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Registration Successful!"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Your account has been created. Please verify your phone number using the WhatsApp link below."}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,a.jsxs)("p",{className:"text-sm text-blue-800 mb-3",children:[(0,a.jsx)("strong",{children:"Phone:"})," ",r.phone]}),(0,a.jsx)("p",{className:"text-sm text-blue-700 mb-4",children:"Click the button below to send your OTP code to our admin via WhatsApp:"}),(0,a.jsxs)("a",{href:A,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium",children:[(0,a.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"})}),"Send OTP via WhatsApp"]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(o(),{href:"/verify-otp",className:"block w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium text-center",children:"I've Sent the OTP - Verify Now"}),(0,a.jsx)(o(),{href:"/login",className:"block w-full text-blue-600 hover:text-blue-700 transition-colors text-center",children:"Back to Login"})]})]})})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Create Account"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Join AI Trainer Hub and start learning"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[v&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,a.jsx)("p",{className:"text-red-600 text-sm",children:v})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:r.name,onChange:O,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Enter your full name",disabled:j})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",value:r.email,onChange:O,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Enter your email",disabled:j})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:r.phone,onChange:O,className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"+6281234567890",disabled:j})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Include country code (e.g., +62 for Indonesia)"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:n?"text":"password",id:"password",name:"password",value:r.password,onChange:O,className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Create a password",disabled:j}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!n),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:j,children:n?(0,a.jsx)(h.A,{className:"w-5 h-5"}):(0,a.jsx)(x.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:g?"text":"password",id:"confirmPassword",name:"confirmPassword",value:r.confirmPassword,onChange:O,className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Confirm your password",disabled:j}),(0,a.jsx)("button",{type:"button",onClick:()=>y(!g),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:j,children:g?(0,a.jsx)(h.A,{className:"w-5 h-5"}):(0,a.jsx)(x.A,{className:"w-5 h-5"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"referralCode",className:"block text-sm font-medium text-gray-700 mb-2",children:"Referral Code (Optional)"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",id:"referralCode",name:"referralCode",value:r.referralCode,onChange:e=>{let r=e.target.value;s(e=>({...e,referralCode:r})),r?M(r):E("")},className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"Enter referral code",disabled:j}),F&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"})})]}),S&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-green-50 border border-green-200 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-700",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 inline mr-1"}),"You'll be referred by: ",(0,a.jsx)("strong",{children:S})]})}),r.referralCode&&!S&&!F&&(0,a.jsx)("p",{className:"text-xs text-red-500 mt-1",children:"Invalid referral code"})]}),(0,a.jsx)("button",{type:"submit",disabled:j,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors font-medium flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,a.jsxs)(a.Fragment,{children:["Create Account",(0,a.jsx)(b.A,{className:"ml-2 w-4 h-4"})]})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(o(),{href:"/login",className:"text-blue-600 hover:text-blue-700 font-medium",children:"Sign in"})]})})]})]})})};function g(){return(0,a.jsx)(t.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(p,{})})}},8883:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});let a=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9456:(e,r,s)=>{Promise.resolve().then(s.bind(s,6616))}},e=>{e.O(0,[445,874,319,441,964,358],()=>e(e.s=9456)),_N_E=e.O()}]);