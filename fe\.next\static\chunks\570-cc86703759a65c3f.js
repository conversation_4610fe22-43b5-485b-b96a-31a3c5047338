(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[570],{306:(t,e,s)=>{"use strict";s.d(e,{A:()=>i});let i=(0,s(9946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},2093:t=>{t.exports={style:{fontFamily:"'Geist', '<PERSON><PERSON>st Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},2318:(t,e,s)=>{"use strict";s.d(e,{A:()=>i});let i=(0,s(9946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2374:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var s in e)Object.defineProperty(t,s,{enumerable:!0,get:e[s]})}(e,{cancelIdleCallback:function(){return i},requestIdleCallback:function(){return s}});let s="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(t){let e=Date.now();return self.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-e))}})},1)},i="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(t){return clearTimeout(t)};("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},2714:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"setAttributesFromProps",{enumerable:!0,get:function(){return n}});let s={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},i=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function r(t){return["async","defer","noModule"].includes(t)}function n(t,e){for(let[n,a]of Object.entries(e)){if(!e.hasOwnProperty(n)||i.includes(n)||void 0===a)continue;let o=s[n]||n.toLowerCase();"SCRIPT"===t.tagName&&r(o)?t[o]=!!a:t.setAttribute(o,String(a)),(!1===a||"SCRIPT"===t.tagName&&r(o)&&(!a||"false"===a))&&(t.setAttribute(o,""),t.removeAttribute(o))}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},2922:(t,e,s)=>{"use strict";s.d(e,{E:()=>R});var i="undefined"==typeof window||"Deno"in globalThis;function r(){}function n(t,e){return"function"==typeof t?t(e):t}function a(t,e){let{type:s="all",exact:i,fetchStatus:r,predicate:n,queryKey:a,stale:o}=t;if(a){if(i){if(e.queryHash!==u(a,e.options))return!1}else if(!l(e.queryKey,a))return!1}if("all"!==s){let t=e.isActive();if("active"===s&&!t||"inactive"===s&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&(!r||r===e.state.fetchStatus)&&(!n||!!n(e))}function o(t,e){let{exact:s,status:i,predicate:r,mutationKey:n}=t;if(n){if(!e.options.mutationKey)return!1;if(s){if(c(e.options.mutationKey)!==c(n))return!1}else if(!l(e.options.mutationKey,n))return!1}return(!i||e.state.status===i)&&(!r||!!r(e))}function u(t,e){return(e?.queryKeyHashFn||c)(t)}function c(t){return JSON.stringify(t,(t,e)=>d(e)?Object.keys(e).sort().reduce((t,s)=>(t[s]=e[s],t),{}):e)}function l(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&Object.keys(e).every(s=>l(t[s],e[s]))}function h(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function d(t){if(!f(t))return!1;let e=t.constructor;if(void 0===e)return!0;let s=e.prototype;return!!f(s)&&!!s.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function f(t){return"[object Object]"===Object.prototype.toString.call(t)}function p(t,e,s=0){let i=[...t,e];return s&&i.length>s?i.slice(1):i}function y(t,e,s=0){let i=[e,...t];return s&&i.length>s?i.slice(0,-1):i}var m=Symbol();function v(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==m?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}var b=t=>setTimeout(t,0),g=function(){let t=[],e=0,s=t=>{t()},i=t=>{t()},r=b,n=i=>{e?t.push(i):r(()=>{s(i)})};return{batch:n=>{let a;e++;try{a=n()}finally{--e||(()=>{let e=t;t=[],e.length&&r(()=>{i(()=>{e.forEach(t=>{s(t)})})})})()}return a},batchCalls:t=>(...e)=>{n(()=>{t(...e)})},schedule:n,setNotifyFunction:t=>{s=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{r=t}}}(),O=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},C=new class extends O{#t;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}},w=new class extends O{#i=!0;#e;#s;constructor(){super(),this.#s=t=>{if(!i&&window.addEventListener){let e=()=>t(!0),s=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",s,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",s)}}}}onSubscribe(){this.#e||this.setEventListener(this.#s)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#s=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#i!==t&&(this.#i=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#i}};function S(t){return Math.min(1e3*2**t,3e4)}function q(t){return(t??"online")!=="online"||w.isOnline()}var P=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function M(t){return t instanceof P}function A(t){let e,s=!1,r=0,n=!1,a=function(){let t,e,s=new Promise((s,i)=>{t=s,e=i});function i(t){Object.assign(s,t),delete s.resolve,delete s.reject}return s.status="pending",s.catch(()=>{}),s.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},s.reject=t=>{i({status:"rejected",reason:t}),e(t)},s}(),o=()=>C.isFocused()&&("always"===t.networkMode||w.isOnline())&&t.canRun(),u=()=>q(t.networkMode)&&t.canRun(),c=s=>{n||(n=!0,t.onSuccess?.(s),e?.(),a.resolve(s))},l=s=>{n||(n=!0,t.onError?.(s),e?.(),a.reject(s))},h=()=>new Promise(s=>{e=t=>{(n||o())&&s(t)},t.onPause?.()}).then(()=>{e=void 0,n||t.onContinue?.()}),d=()=>{let e;if(n)return;let a=0===r?t.initialPromise:void 0;try{e=a??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(c).catch(e=>{if(n)return;let a=t.retry??3*!i,u=t.retryDelay??S,c="function"==typeof u?u(r,e):u,f=!0===a||"number"==typeof a&&r<a||"function"==typeof a&&a(r,e);if(s||!f)return void l(e);r++,t.onFail?.(r,e),new Promise(t=>{setTimeout(t,c)}).then(()=>o()?void 0:h()).then(()=>{s?l(e):d()})})};return{promise:a,cancel:e=>{n||(l(new P(e)),t.abort?.())},continue:()=>(e?.(),a),cancelRetry:()=>{s=!0},continueRetry:()=>{s=!1},canStart:u,start:()=>(u()?d():h().then(d),a)}}var F=class{#r;destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&(this.#r=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i?1/0:3e5))}clearGcTimeout(){this.#r&&(clearTimeout(this.#r),this.#r=void 0)}},_=class extends F{#n;#a;#o;#u;#c;#l;#h;constructor(t){super(),this.#h=!1,this.#l=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#u=t.client,this.#o=this.#u.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#n=function(t){let e="function"==typeof t.initialData?t.initialData():t.initialData,s=void 0!==e,i=s?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=t.state??this.#n,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#c?.promise}setOptions(t){this.options={...this.#l,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(t,e){var s,i;let r=(s=this.state.data,"function"==typeof(i=this.options).structuralSharing?i.structuralSharing(s,t):!1!==i.structuralSharing?function t(e,s){if(e===s)return e;let i=h(e)&&h(s);if(i||d(e)&&d(s)){let r=i?e:Object.keys(e),n=r.length,a=i?s:Object.keys(s),o=a.length,u=i?[]:{},c=new Set(r),l=0;for(let r=0;r<o;r++){let n=i?r:a[r];(!i&&c.has(n)||i)&&void 0===e[n]&&void 0===s[n]?(u[n]=void 0,l++):(u[n]=t(e[n],s[n]),u[n]===e[n]&&void 0!==e[n]&&l++)}return n===o&&l===n?e:u}return s}(s,t):t);return this.#d({data:r,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),r}setState(t,e){this.#d({type:"setState",state:t,setStateOptions:e})}cancel(t){let e=this.#c?.promise;return this.#c?.cancel(t),e?e.then(r).catch(r):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#n)}isActive(){return this.observers.some(t=>{var e;return!1!==(e=t.options.enabled,"function"==typeof e?e(this):e)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===n(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!Math.max(this.state.dataUpdatedAt+(t||0)-Date.now(),0))}onFocus(){let t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}onOnline(){let t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),this.#c?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(this.#c&&(this.#h?this.#c.cancel({revert:!0}):this.#c.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(this.#c)return this.#c.continueRetry(),this.#c.promise}if(t&&this.setOptions(t),!this.options.queryFn){let t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}let s=new AbortController,i=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(this.#h=!0,s.signal)})},r=()=>{let t=v(this.options,e),s=(()=>{let t={client:this.#u,queryKey:this.queryKey,meta:this.meta};return i(t),t})();return(this.#h=!1,this.options.persister)?this.options.persister(t,s,this):t(s)},n=(()=>{let t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:this.#u,state:this.state,fetchFn:r};return i(t),t})();this.options.behavior?.onFetch(n,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==n.fetchOptions?.meta)&&this.#d({type:"fetch",meta:n.fetchOptions?.meta});let a=t=>{M(t)&&t.silent||this.#d({type:"error",error:t}),M(t)||(this.#o.config.onError?.(t,this),this.#o.config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return this.#c=A({initialPromise:e?.initialPromise,fn:n.fetchFn,abort:s.abort.bind(s),onSuccess:t=>{if(void 0===t)return void a(Error(`${this.queryHash} data is undefined`));try{this.setData(t)}catch(t){a(t);return}this.#o.config.onSuccess?.(t,this),this.#o.config.onSettled?.(t,this.state.error,this),this.scheduleGc()},onError:a,onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:n.options.retry,retryDelay:n.options.retryDelay,networkMode:n.options.networkMode,canRun:()=>!0}),this.#c.start()}#d(t){let e=e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":var s;return{...e,...(s=e.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:q(this.options.networkMode)?"fetching":"paused",...void 0===s&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return this.#a=void 0,{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=t.error;if(M(i)&&i.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...e,error:i,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}};this.state=e(this.state),g.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:t})})}},E=class extends O{constructor(t={}){super(),this.config=t,this.#f=new Map}#f;build(t,e,s){let i=e.queryKey,r=e.queryHash??u(i,e),n=this.get(r);return n||(n=new _({client:t,queryKey:i,queryHash:r,options:t.defaultQueryOptions(e),state:s,defaultOptions:t.getQueryDefaults(i)}),this.add(n)),n}add(t){this.#f.has(t.queryHash)||(this.#f.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){let e=this.#f.get(t.queryHash);e&&(t.destroy(),e===t&&this.#f.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){g.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#f.get(t)}getAll(){return[...this.#f.values()]}find(t){let e={exact:!0,...t};return this.getAll().find(t=>a(e,t))}findAll(t={}){let e=this.getAll();return Object.keys(t).length>0?e.filter(e=>a(t,e)):e}notify(t){g.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){g.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){g.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},k=class extends F{#p;#y;#c;constructor(t){super(),this.mutationId=t.mutationId,this.#y=t.mutationCache,this.#p=[],this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#p.includes(t)||(this.#p.push(t),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#p=this.#p.filter(e=>e!==t),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#c?.continue()??this.execute(this.state.variables)}async execute(t){let e=()=>{this.#d({type:"continue"})};this.#c=A({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#d({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#d({type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let s="pending"===this.state.status,i=!this.#c.canStart();try{if(s)e();else{this.#d({type:"pending",variables:t,isPaused:i}),await this.#y.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#d({type:"pending",context:e,variables:t,isPaused:i})}let r=await this.#c.start();return await this.#y.config.onSuccess?.(r,t,this.state.context,this),await this.options.onSuccess?.(r,t,this.state.context),await this.#y.config.onSettled?.(r,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(r,null,t,this.state.context),this.#d({type:"success",data:r}),r}catch(e){try{throw await this.#y.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#y.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#d({type:"error",error:e})}}finally{this.#y.runNext(this)}}#d(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),g.batch(()=>{this.#p.forEach(e=>{e.onMutationUpdate(t)}),this.#y.notify({mutation:this,type:"updated",action:t})})}},x=class extends O{constructor(t={}){super(),this.config=t,this.#m=new Set,this.#v=new Map,this.#b=0}#m;#v;#b;build(t,e,s){let i=new k({mutationCache:this,mutationId:++this.#b,options:t.defaultMutationOptions(e),state:s});return this.add(i),i}add(t){this.#m.add(t);let e=j(t);if("string"==typeof e){let s=this.#v.get(e);s?s.push(t):this.#v.set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#m.delete(t)){let e=j(t);if("string"==typeof e){let s=this.#v.get(e);if(s)if(s.length>1){let e=s.indexOf(t);-1!==e&&s.splice(e,1)}else s[0]===t&&this.#v.delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){let e=j(t);if("string"!=typeof e)return!0;{let s=this.#v.get(e),i=s?.find(t=>"pending"===t.state.status);return!i||i===t}}runNext(t){let e=j(t);if("string"!=typeof e)return Promise.resolve();{let s=this.#v.get(e)?.find(e=>e!==t&&e.state.isPaused);return s?.continue()??Promise.resolve()}}clear(){g.batch(()=>{this.#m.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(t){let e={exact:!0,...t};return this.getAll().find(t=>o(e,t))}findAll(t={}){return this.getAll().filter(e=>o(t,e))}notify(t){g.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){let t=this.getAll().filter(t=>t.state.isPaused);return g.batch(()=>Promise.all(t.map(t=>t.continue().catch(r))))}};function j(t){return t.options.scope?.id}function D(t){return{onFetch:(e,s)=>{let i=e.options,r=e.fetchOptions?.meta?.fetchMore?.direction,n=e.state.data?.pages||[],a=e.state.data?.pageParams||[],o={pages:[],pageParams:[]},u=0,c=async()=>{let s=!1,c=v(e.options,e.fetchOptions),l=async(t,i,r)=>{if(s)return Promise.reject();if(null==i&&t.pages.length)return Promise.resolve(t);let n=(()=>{let t={client:e.client,queryKey:e.queryKey,pageParam:i,direction:r?"backward":"forward",meta:e.options.meta};return Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(e.signal.aborted?s=!0:e.signal.addEventListener("abort",()=>{s=!0}),e.signal)}),t})(),a=await c(n),{maxPages:o}=e.options,u=r?y:p;return{pages:u(t.pages,a,o),pageParams:u(t.pageParams,i,o)}};if(r&&n.length){let t="backward"===r,e={pages:n,pageParams:a},s=(t?function(t,{pages:e,pageParams:s}){return e.length>0?t.getPreviousPageParam?.(e[0],e,s[0],s):void 0}:T)(i,e);o=await l(e,s,t)}else{let e=t??n.length;do{let t=0===u?a[0]??i.initialPageParam:T(i,o);if(u>0&&null==t)break;o=await l(o,t),u++}while(u<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},s):e.fetchFn=c}}}function T(t,{pages:e,pageParams:s}){let i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,s[i],s):void 0}var R=class{#g;#y;#l;#O;#C;#w;#S;#q;constructor(t={}){this.#g=t.queryCache||new E,this.#y=t.mutationCache||new x,this.#l=t.defaultOptions||{},this.#O=new Map,this.#C=new Map,this.#w=0}mount(){this.#w++,1===this.#w&&(this.#S=C.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#g.onFocus())}),this.#q=w.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#g.onOnline())}))}unmount(){this.#w--,0===this.#w&&(this.#S?.(),this.#S=void 0,this.#q?.(),this.#q=void 0)}isFetching(t){return this.#g.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#y.findAll({...t,status:"pending"}).length}getQueryData(t){let e=this.defaultQueryOptions({queryKey:t});return this.#g.get(e.queryHash)?.state.data}ensureQueryData(t){let e=this.defaultQueryOptions(t),s=this.#g.build(this,e),i=s.state.data;return void 0===i?this.fetchQuery(t):(t.revalidateIfStale&&s.isStaleByTime(n(e.staleTime,s))&&this.prefetchQuery(e),Promise.resolve(i))}getQueriesData(t){return this.#g.findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,s){let i=this.defaultQueryOptions({queryKey:t}),r=this.#g.get(i.queryHash),n=r?.state.data,a="function"==typeof e?e(n):e;if(void 0!==a)return this.#g.build(this,i).setData(a,{...s,manual:!0})}setQueriesData(t,e,s){return g.batch(()=>this.#g.findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,s)]))}getQueryState(t){let e=this.defaultQueryOptions({queryKey:t});return this.#g.get(e.queryHash)?.state}removeQueries(t){let e=this.#g;g.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){let s=this.#g;return g.batch(()=>(s.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){let s={revert:!0,...e};return Promise.all(g.batch(()=>this.#g.findAll(t).map(t=>t.cancel(s)))).then(r).catch(r)}invalidateQueries(t,e={}){return g.batch(()=>(this.#g.findAll(t).forEach(t=>{t.invalidate()}),t?.refetchType==="none")?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e))}refetchQueries(t,e={}){let s={...e,cancelRefetch:e.cancelRefetch??!0};return Promise.all(g.batch(()=>this.#g.findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,s);return s.throwOnError||(e=e.catch(r)),"paused"===t.state.fetchStatus?Promise.resolve():e}))).then(r)}fetchQuery(t){let e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);let s=this.#g.build(this,e);return s.isStaleByTime(n(e.staleTime,s))?s.fetch(e):Promise.resolve(s.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(r).catch(r)}fetchInfiniteQuery(t){return t.behavior=D(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(r).catch(r)}ensureInfiniteQueryData(t){return t.behavior=D(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return w.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#g}getMutationCache(){return this.#y}getDefaultOptions(){return this.#l}setDefaultOptions(t){this.#l=t}setQueryDefaults(t,e){this.#O.set(c(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){let e=[...this.#O.values()],s={};return e.forEach(e=>{l(t,e.queryKey)&&Object.assign(s,e.defaultOptions)}),s}setMutationDefaults(t,e){this.#C.set(c(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){let e=[...this.#C.values()],s={};return e.forEach(e=>{l(t,e.mutationKey)&&Object.assign(s,e.defaultOptions)}),s}defaultQueryOptions(t){if(t._defaulted)return t;let e={...this.#l.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=u(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===m&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...this.#l.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#g.clear(),this.#y.clear()}}},4835:(t,e,s)=>{"use strict";s.d(e,{A:()=>i});let i=(0,s(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5695:(t,e,s)=>{"use strict";var i=s(8999);s.o(i,"useParams")&&s.d(e,{useParams:function(){return i.useParams}}),s.o(i,"usePathname")&&s.d(e,{usePathname:function(){return i.usePathname}}),s.o(i,"useRouter")&&s.d(e,{useRouter:function(){return i.useRouter}}),s.o(i,"useSearchParams")&&s.d(e,{useSearchParams:function(){return i.useSearchParams}})},6474:(t,e,s)=>{"use strict";s.d(e,{A:()=>i});let i=(0,s(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6715:(t,e,s)=>{"use strict";s.d(e,{Ht:()=>a});var i=s(2115),r=s(5155),n=i.createContext(void 0),a=t=>{let{client:e,children:s}=t;return i.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,r.jsx)(n.Provider,{value:e,children:s})}},7580:(t,e,s)=>{"use strict";s.d(e,{A:()=>i});let i=(0,s(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7735:t=>{t.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9243:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var s in e)Object.defineProperty(t,s,{enumerable:!0,get:e[s]})}(e,{default:function(){return v},handleClientScriptLoad:function(){return p},initScriptLoader:function(){return y}});let i=s(8229),r=s(6966),n=s(5155),a=i._(s(7650)),o=r._(s(2115)),u=s(2830),c=s(2714),l=s(2374),h=new Map,d=new Set,f=t=>{let{src:e,id:s,onLoad:i=()=>{},onReady:r=null,dangerouslySetInnerHTML:n,children:o="",strategy:u="afterInteractive",onError:l,stylesheets:f}=t,p=s||e;if(p&&d.has(p))return;if(h.has(e)){d.add(p),h.get(e).then(i,l);return}let y=()=>{r&&r(),d.add(p)},m=document.createElement("script"),v=new Promise((t,e)=>{m.addEventListener("load",function(e){t(),i&&i.call(this,e),y()}),m.addEventListener("error",function(t){e(t)})}).catch(function(t){l&&l(t)});n?(m.innerHTML=n.__html||"",y()):o?(m.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):"",y()):e&&(m.src=e,h.set(e,v)),(0,c.setAttributesFromProps)(m,t),"worker"===u&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",u),f&&(t=>{if(a.default.preinit)return t.forEach(t=>{a.default.preinit(t,{as:"style"})});{let e=document.head;t.forEach(t=>{let s=document.createElement("link");s.type="text/css",s.rel="stylesheet",s.href=t,e.appendChild(s)})}})(f),document.body.appendChild(m)};function p(t){let{strategy:e="afterInteractive"}=t;"lazyOnload"===e?window.addEventListener("load",()=>{(0,l.requestIdleCallback)(()=>f(t))}):f(t)}function y(t){t.forEach(p),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(t=>{let e=t.id||t.getAttribute("src");d.add(e)})}function m(t){let{id:e,src:s="",onLoad:i=()=>{},onReady:r=null,strategy:c="afterInteractive",onError:h,stylesheets:p,...y}=t,{updateScripts:m,scripts:v,getIsSsr:b,appDir:g,nonce:O}=(0,o.useContext)(u.HeadManagerContext);O=y.nonce||O;let C=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let t=e||s;C.current||(r&&t&&d.has(t)&&r(),C.current=!0)},[r,e,s]);let w=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!w.current){if("afterInteractive"===c)f(t);else"lazyOnload"===c&&("complete"===document.readyState?(0,l.requestIdleCallback)(()=>f(t)):window.addEventListener("load",()=>{(0,l.requestIdleCallback)(()=>f(t))}));w.current=!0}},[t,c]),("beforeInteractive"===c||"worker"===c)&&(m?(v[c]=(v[c]||[]).concat([{id:e,src:s,onLoad:i,onReady:r,onError:h,...y,nonce:O}]),m(v)):b&&b()?d.add(e||s):b&&!b()&&f({...t,nonce:O})),g){if(p&&p.forEach(t=>{a.default.preinit(t,{as:"style"})}),"beforeInteractive"===c)if(!s)return y.dangerouslySetInnerHTML&&(y.children=y.dangerouslySetInnerHTML.__html,delete y.dangerouslySetInnerHTML),(0,n.jsx)("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...y,id:e}])+")"}});else return a.default.preload(s,y.integrity?{as:"script",integrity:y.integrity,nonce:O,crossOrigin:y.crossOrigin}:{as:"script",nonce:O,crossOrigin:y.crossOrigin}),(0,n.jsx)("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([s,{...y,id:e}])+")"}});"afterInteractive"===c&&s&&a.default.preload(s,y.integrity?{as:"script",integrity:y.integrity,nonce:O,crossOrigin:y.crossOrigin}:{as:"script",nonce:O,crossOrigin:y.crossOrigin})}return null}Object.defineProperty(m,"__nextScript",{value:!0});let v=m;("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)}}]);