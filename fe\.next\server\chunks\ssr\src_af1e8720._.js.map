{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3001\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function asset(path: string) {\r\n  return `${API_URL}/${path.startsWith(\"/\") ? path.replace(\"/\", \"\") : path}`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAE5C,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,MAAM,IAAY;IAChC,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,UAAU,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,MAAM,MAAM;AAC5E", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { ChevronDown } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface SelectProps {\r\n  value?: string;\r\n  onValueChange?: (value: string) => void;\r\n  children: React.ReactNode;\r\n  disabled?: boolean;\r\n}\r\n\r\ninterface SelectTriggerProps {\r\n  className?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\ninterface SelectValueProps {\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\ninterface SelectContentProps {\r\n  className?: string;\r\n  children: React.ReactNode;\r\n}\r\n\r\ninterface SelectItemProps {\r\n  value: string;\r\n  className?: string;\r\n  children: React.ReactNode;\r\n  onSelect?: () => void;\r\n}\r\n\r\nconst SelectContext = React.createContext<{\r\n  value?: string;\r\n  onValueChange?: (value: string) => void;\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n}>({\r\n  open: false,\r\n  setOpen: () => {},\r\n});\r\n\r\nconst Select: React.FC<SelectProps> = ({ value, onValueChange, children }) => {\r\n  const [open, setOpen] = React.useState(false);\r\n\r\n  return (\r\n    <SelectContext.Provider value={{ value, onValueChange, open, setOpen }}>\r\n      <div className=\"relative\">\r\n        {children}\r\n      </div>\r\n    </SelectContext.Provider>\r\n  );\r\n};\r\n\r\nconst SelectTrigger: React.FC<SelectTriggerProps> = ({ className, children }) => {\r\n  const { open, setOpen } = React.useContext(SelectContext);\r\n\r\n  return (\r\n    <button\r\n      type=\"button\"\r\n      className={cn(\r\n        \"flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={() => setOpen(!open)}\r\n    >\r\n      {children}\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </button>\r\n  );\r\n};\r\n\r\nconst SelectValue: React.FC<SelectValueProps> = ({ placeholder, className }) => {\r\n  const { value } = React.useContext(SelectContext);\r\n\r\n  return (\r\n    <span className={cn(\"block truncate\", className)}>\r\n      {value || placeholder}\r\n    </span>\r\n  );\r\n};\r\n\r\nconst SelectContent: React.FC<SelectContentProps> = ({ className, children }) => {\r\n  const { open, setOpen } = React.useContext(SelectContext);\r\n\r\n  if (!open) return null;\r\n\r\n  return (\r\n    <>\r\n      <div\r\n        className=\"fixed inset-0 z-40\"\r\n        onClick={() => setOpen(false)}\r\n      />\r\n      <div\r\n        className={cn(\r\n          \"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border border-gray-200 bg-white p-1 text-gray-950 shadow-md animate-in fade-in-0 zoom-in-95\",\r\n          className\r\n        )}\r\n      >\r\n        {children}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nconst SelectItem: React.FC<SelectItemProps> = ({ value, className, children, onSelect }) => {\r\n  const { onValueChange, setOpen } = React.useContext(SelectContext);\r\n\r\n  const handleSelect = () => {\r\n    onValueChange?.(value);\r\n    setOpen(false);\r\n    onSelect?.();\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-gray-100 focus:bg-gray-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleSelect}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue };"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAmCA,MAAM,8BAAgB,qMAAA,CAAA,gBAAmB,CAKtC;IACD,MAAM;IACN,SAAS,KAAO;AAClB;AAEA,MAAM,SAAgC,CAAC,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE;IACvE,MAAM,CAAC,MAAM,QAAQ,GAAG,qMAAA,CAAA,WAAc,CAAC;IAEvC,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAe;YAAM;QAAQ;kBACnE,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAEA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;IAC1E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,qMAAA,CAAA,aAAgB,CAAC;IAE3C,qBACE,8OAAC;QACC,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kRACA;QAEF,SAAS,IAAM,QAAQ,CAAC;;YAEvB;0BACD,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;;;;;;;AAG7B;AAEA,MAAM,cAA0C,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE;IACzE,MAAM,EAAE,KAAK,EAAE,GAAG,qMAAA,CAAA,aAAgB,CAAC;IAEnC,qBACE,8OAAC;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBACnC,SAAS;;;;;;AAGhB;AAEA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;IAC1E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,qMAAA,CAAA,aAAgB,CAAC;IAE3C,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE;;0BACE,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,QAAQ;;;;;;0BAEzB,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qJACA;0BAGD;;;;;;;;AAIT;AAEA,MAAM,aAAwC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE;IACrF,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,qMAAA,CAAA,aAAgB,CAAC;IAEpD,MAAM,eAAe;QACnB,gBAAgB;QAChB,QAAQ;QACR;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oNACA;QAEF,SAAS;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/FilterSelect.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { FilterOption, FilterType } from \"@/types/filters\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface FilterSelectProps {\r\n  options: FilterOption[];\r\n  selectedValue: FilterType;\r\n  onSelect: (value: FilterType) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const FilterSelect: React.FC<FilterSelectProps> = ({\r\n  options,\r\n  selectedValue,\r\n  onSelect,\r\n  placeholder = \"Select filter\",\r\n  className = \"\",\r\n  disabled = false\r\n}) => {\r\n  const handleValueChange = (value: string) => {\r\n    onSelect(value as FilterType);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      value={selectedValue}\r\n      onValueChange={handleValueChange}\r\n      disabled={disabled}\r\n    >\r\n      <SelectTrigger\r\n        className={cn(\r\n          \"w-full sm:w-[200px] h-10 bg-white border border-gray-200 hover:border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200\",\r\n          disabled && \"opacity-50 cursor-not-allowed\",\r\n          className\r\n        )}\r\n        aria-label=\"Filter experts\"\r\n      >\r\n        <SelectValue placeholder={placeholder} />\r\n      </SelectTrigger>\r\n\r\n      <SelectContent\r\n        className=\"bg-white border border-gray-200 shadow-lg rounded-lg p-1 min-w-[200px]\"\r\n      >\r\n        {options.map((option) => (\r\n          <SelectItem\r\n            key={option.id}\r\n            value={option.id}\r\n            className={cn(\r\n              \"flex items-center gap-3 px-3 py-2.5 rounded-md cursor-pointer transition-colors duration-150\",\r\n              \"hover:bg-gray-50 focus:bg-gray-50 focus:outline-none\",\r\n              \"data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary\",\r\n              selectedValue === option.id && \"bg-primary/5\"\r\n            )}\r\n          >\r\n            <span className=\"text-base flex-shrink-0\" role=\"img\" aria-hidden=\"true\">\r\n              {option.icon}\r\n            </span>\r\n            <div className=\"flex flex-col items-start\">\r\n              <span className=\"font-medium text-gray-900 text-sm\">\r\n                {option.label}\r\n              </span>\r\n              {option.description && (\r\n                <span className=\"text-xs text-gray-500 mt-0.5\">\r\n                  {option.description}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </SelectItem>\r\n        ))}\r\n      </SelectContent>\r\n    </Select>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAgBO,MAAM,eAA4C,CAAC,EACxD,OAAO,EACP,aAAa,EACb,QAAQ,EACR,cAAc,eAAe,EAC7B,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,OAAO;QACP,eAAe;QACf,UAAU;;0BAEV,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sKACA,YAAY,iCACZ;gBAEF,cAAW;0BAEX,cAAA,8OAAC,kIAAA,CAAA,cAAW;oBAAC,aAAa;;;;;;;;;;;0BAG5B,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAU;0BAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;wBAET,OAAO,OAAO,EAAE;wBAChB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,wDACA,wEACA,kBAAkB,OAAO,EAAE,IAAI;;0CAGjC,8OAAC;gCAAK,WAAU;gCAA0B,MAAK;gCAAM,eAAY;0CAC9D,OAAO,IAAI;;;;;;0CAEd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,OAAO,KAAK;;;;;;oCAEd,OAAO,WAAW,kBACjB,8OAAC;wCAAK,WAAU;kDACb,OAAO,WAAW;;;;;;;;;;;;;uBAlBpB,OAAO,EAAE;;;;;;;;;;;;;;;;AA2B1B", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/TimelineSelect.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { TimelineOption, TimelineType } from \"@/types/filters\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface TimelineSelectProps {\r\n  options: TimelineOption[];\r\n  selectedValue: TimelineType;\r\n  onSelect: (value: TimelineType) => void;\r\n  className?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const TimelineSelect: React.FC<TimelineSelectProps> = ({\r\n  options,\r\n  selectedValue,\r\n  onSelect,\r\n  className = \"\",\r\n  disabled = false\r\n}) => {\r\n  const handleValueChange = (value: string) => {\r\n    onSelect(value as TimelineType);\r\n  };\r\n\r\n  return (\r\n    <Select\r\n      value={selectedValue}\r\n      onValueChange={handleValueChange}\r\n      disabled={disabled}\r\n    >\r\n      <SelectTrigger\r\n        className={cn(\r\n          \"w-full sm:w-[150px] h-10 bg-white border border-gray-200 hover:border-gray-300 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200\",\r\n          disabled && \"opacity-50 cursor-not-allowed\",\r\n          className\r\n        )}\r\n        aria-label=\"Select timeline\"\r\n      >\r\n        <SelectValue placeholder=\"Last 30 Days\" />\r\n      </SelectTrigger>\r\n\r\n      <SelectContent\r\n        className=\"bg-white border border-gray-200 shadow-lg rounded-lg p-1 min-w-[150px]\"\r\n      >\r\n        {options.map((option) => (\r\n          <SelectItem\r\n            key={option.id}\r\n            value={option.id}\r\n            className={cn(\r\n              \"px-3 py-2 rounded-md cursor-pointer transition-colors duration-150\",\r\n              \"hover:bg-gray-50 focus:bg-gray-50 focus:outline-none\",\r\n              \"data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary\",\r\n              selectedValue === option.id && \"bg-primary/5\"\r\n            )}\r\n          >\r\n            <span className=\"font-medium text-gray-900 text-sm\">\r\n              {option.label}\r\n            </span>\r\n          </SelectItem>\r\n        ))}\r\n      </SelectContent>\r\n    </Select>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAeO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,aAAa,EACb,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,OAAO;QACP,eAAe;QACf,UAAU;;0BAEV,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sKACA,YAAY,iCACZ;gBAEF,cAAW;0BAEX,cAAA,8OAAC,kIAAA,CAAA,cAAW;oBAAC,aAAY;;;;;;;;;;;0BAG3B,8OAAC,kIAAA,CAAA,gBAAa;gBACZ,WAAU;0BAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;wBAET,OAAO,OAAO,EAAE;wBAChB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,wDACA,wEACA,kBAAkB,OAAO,EAAE,IAAI;kCAGjC,cAAA,8OAAC;4BAAK,WAAU;sCACb,OAAO,KAAK;;;;;;uBAVV,OAAO,EAAE;;;;;;;;;;;;;;;;AAiB1B", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/utils/searchHighlight.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n/**\r\n * Highlights search terms in text with proper escaping\r\n * @param text - The text to highlight\r\n * @param searchTerm - The search term to highlight\r\n * @param className - CSS class for highlighted text\r\n * @returns JSX element with highlighted text\r\n */\r\nexport const highlightSearchTerm = (\r\n  text: string,\r\n  searchTerm: string,\r\n  className: string = \"bg-yellow-200 text-yellow-900 px-1 rounded\"\r\n): React.ReactNode => {\r\n  if (!searchTerm || !text) {\r\n    return text;\r\n  }\r\n\r\n  // Escape special regex characters in search term\r\n  const escapedSearchTerm = searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n\r\n  // Create regex for case-insensitive matching\r\n  const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\r\n\r\n  // Split text by search term while preserving the search term\r\n  const parts = text.split(regex);\r\n\r\n  return (\r\n    <>\r\n      {parts.map((part, index) => {\r\n        // Check if this part matches the search term (case-insensitive)\r\n        if (part.toLowerCase() === searchTerm.toLowerCase()) {\r\n          return (\r\n            <mark key={index} className={className}>\r\n              {part}\r\n            </mark>\r\n          );\r\n        }\r\n        return part;\r\n      })}\r\n    </>\r\n  );\r\n};\r\n\r\n/**\r\n * Highlights multiple search terms in text\r\n * @param text - The text to highlight\r\n * @param searchTerms - Array of search terms to highlight\r\n * @param className - CSS class for highlighted text\r\n * @returns JSX element with highlighted text\r\n */\r\nexport const highlightMultipleTerms = (\r\n  text: string,\r\n  searchTerms: string[],\r\n  className: string = \"bg-yellow-200 text-yellow-900 px-1 rounded\"\r\n): React.ReactNode => {\r\n  if (!searchTerms.length || !text) {\r\n    return text;\r\n  }\r\n\r\n  // Filter out empty terms and escape special regex characters\r\n  const validTerms = searchTerms\r\n    .filter(term => term.trim())\r\n    .map(term => term.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'));\r\n\r\n  if (!validTerms.length) {\r\n    return text;\r\n  }\r\n\r\n  // Create regex for case-insensitive matching of any term\r\n  const regex = new RegExp(`(${validTerms.join('|')})`, 'gi');\r\n\r\n  // Split text by search terms while preserving the search terms\r\n  const parts = text.split(regex);\r\n\r\n  return (\r\n    <>\r\n      {parts.map((part, index) => {\r\n        // Check if this part matches any search term (case-insensitive)\r\n        const isMatch = validTerms.some(term =>\r\n          part.toLowerCase() === term.toLowerCase()\r\n        );\r\n\r\n        if (isMatch) {\r\n          return (\r\n            <mark key={index} className={className}>\r\n              {part}\r\n            </mark>\r\n          );\r\n        }\r\n        return part;\r\n      })}\r\n    </>\r\n  );\r\n};\r\n\r\n/**\r\n * Truncates text and highlights search terms\r\n * @param text - The text to truncate and highlight\r\n * @param searchTerm - The search term to highlight\r\n * @param maxLength - Maximum length of text before truncation\r\n * @param className - CSS class for highlighted text\r\n * @returns JSX element with truncated and highlighted text\r\n */\r\nexport const highlightAndTruncate = (\r\n  text: string,\r\n  searchTerm: string,\r\n  maxLength: number = 150,\r\n  className: string = \"bg-yellow-200 text-yellow-900 px-1 rounded\"\r\n): React.ReactNode => {\r\n  if (!text) {\r\n    return text;\r\n  }\r\n\r\n  let truncatedText = text;\r\n\r\n  // If text is longer than maxLength, try to truncate around search term\r\n  if (text.length > maxLength && searchTerm) {\r\n    const searchIndex = text.toLowerCase().indexOf(searchTerm.toLowerCase());\r\n\r\n    if (searchIndex !== -1) {\r\n      // Calculate start position to center the search term\r\n      const start = Math.max(0, searchIndex - Math.floor((maxLength - searchTerm.length) / 2));\r\n      const end = Math.min(text.length, start + maxLength);\r\n\r\n      truncatedText = text.substring(start, end);\r\n\r\n      // Add ellipsis if truncated\r\n      if (start > 0) {\r\n        truncatedText = '...' + truncatedText;\r\n      }\r\n      if (end < text.length) {\r\n        truncatedText = truncatedText + '...';\r\n      }\r\n    } else {\r\n      // No search term found, truncate from beginning\r\n      truncatedText = text.substring(0, maxLength) + (text.length > maxLength ? '...' : '');\r\n    }\r\n  } else if (text.length > maxLength) {\r\n    // No search term, simple truncation\r\n    truncatedText = text.substring(0, maxLength) + '...';\r\n  }\r\n\r\n  return highlightSearchTerm(truncatedText, searchTerm, className);\r\n};"], "names": [], "mappings": ";;;;;;;AASO,MAAM,sBAAsB,CACjC,MACA,YACA,YAAoB,4CAA4C;IAEhE,IAAI,CAAC,cAAc,CAAC,MAAM;QACxB,OAAO;IACT;IAEA,iDAAiD;IACjD,MAAM,oBAAoB,WAAW,OAAO,CAAC,uBAAuB;IAEpE,6CAA6C;IAC7C,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,EAAE;IAEnD,6DAA6D;IAC7D,MAAM,QAAQ,KAAK,KAAK,CAAC;IAEzB,qBACE;kBACG,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,gEAAgE;YAChE,IAAI,KAAK,WAAW,OAAO,WAAW,WAAW,IAAI;gBACnD,qBACE,8OAAC;oBAAiB,WAAW;8BAC1B;mBADQ;;;;;YAIf;YACA,OAAO;QACT;;AAGN;AASO,MAAM,yBAAyB,CACpC,MACA,aACA,YAAoB,4CAA4C;IAEhE,IAAI,CAAC,YAAY,MAAM,IAAI,CAAC,MAAM;QAChC,OAAO;IACT;IAEA,6DAA6D;IAC7D,MAAM,aAAa,YAChB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,IACxB,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,CAAC,uBAAuB;IAEnD,IAAI,CAAC,WAAW,MAAM,EAAE;QACtB,OAAO;IACT;IAEA,yDAAyD;IACzD,MAAM,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,WAAW,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IAEtD,+DAA+D;IAC/D,MAAM,QAAQ,KAAK,KAAK,CAAC;IAEzB,qBACE;kBACG,MAAM,GAAG,CAAC,CAAC,MAAM;YAChB,gEAAgE;YAChE,MAAM,UAAU,WAAW,IAAI,CAAC,CAAA,OAC9B,KAAK,WAAW,OAAO,KAAK,WAAW;YAGzC,IAAI,SAAS;gBACX,qBACE,8OAAC;oBAAiB,WAAW;8BAC1B;mBADQ;;;;;YAIf;YACA,OAAO;QACT;;AAGN;AAUO,MAAM,uBAAuB,CAClC,MACA,YACA,YAAoB,GAAG,EACvB,YAAoB,4CAA4C;IAEhE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,IAAI,gBAAgB;IAEpB,uEAAuE;IACvE,IAAI,KAAK,MAAM,GAAG,aAAa,YAAY;QACzC,MAAM,cAAc,KAAK,WAAW,GAAG,OAAO,CAAC,WAAW,WAAW;QAErE,IAAI,gBAAgB,CAAC,GAAG;YACtB,qDAAqD;YACrD,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,CAAC,YAAY,WAAW,MAAM,IAAI;YACrF,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,QAAQ;YAE1C,gBAAgB,KAAK,SAAS,CAAC,OAAO;YAEtC,4BAA4B;YAC5B,IAAI,QAAQ,GAAG;gBACb,gBAAgB,QAAQ;YAC1B;YACA,IAAI,MAAM,KAAK,MAAM,EAAE;gBACrB,gBAAgB,gBAAgB;YAClC;QACF,OAAO;YACL,gDAAgD;YAChD,gBAAgB,KAAK,SAAS,CAAC,GAAG,aAAa,CAAC,KAAK,MAAM,GAAG,YAAY,QAAQ,EAAE;QACtF;IACF,OAAO,IAAI,KAAK,MAAM,GAAG,WAAW;QAClC,oCAAoC;QACpC,gBAAgB,KAAK,SAAS,CAAC,GAAG,aAAa;IACjD;IAEA,OAAO,oBAAoB,eAAe,YAAY;AACxD", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/ExpertCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader } from \"@/components/ui/card\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Expert } from \"@/types/filters\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { highlightAndTruncate, highlightSearchTerm } from \"@/utils/searchHighlight\";\r\nimport { Star, MessageCircle, DollarSign } from \"lucide-react\";\r\n\r\ninterface ExpertCardProps {\r\n  expert: Expert;\r\n  onChatClick?: (expertId: number) => void;\r\n  className?: string;\r\n  showStats?: boolean;\r\n  searchTerm?: string;\r\n}\r\n\r\nexport const ExpertCard: React.FC<ExpertCardProps> = ({\r\n  expert,\r\n  onChatClick,\r\n  className = \"\",\r\n  showStats = true,\r\n  searchTerm = \"\"\r\n}) => {\r\n  const handleChatClick = () => {\r\n    if (onChatClick) {\r\n      onChatClick(expert.id);\r\n    }\r\n  };\r\n\r\n  const formatNumber = (num: number): string => {\r\n    if (num >= 1000000) {\r\n      return `${(num / 1000000).toFixed(1)}M`;\r\n    } else if (num >= 1000) {\r\n      return `${(num / 1000).toFixed(1)}K`;\r\n    }\r\n    return num.toString();\r\n  };\r\n\r\n  const formatCurrency = (amount: number): string => {\r\n    return new Intl.NumberFormat('en-US', {\r\n      style: 'currency',\r\n      currency: 'USD',\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 0\r\n    }).format(amount);\r\n  };\r\n\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        \"group hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-gray-300 bg-white\",\r\n        \"hover:scale-[1.02] cursor-pointer\",\r\n        className\r\n      )}\r\n      onClick={handleChatClick}\r\n    >\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-start gap-3\">\r\n          <Avatar className=\"h-12 w-12 border-2 border-gray-100\">\r\n            <AvatarImage\r\n              src={expert.imageUrl}\r\n              alt={expert.name}\r\n              className=\"object-cover\"\r\n            />\r\n            <AvatarFallback className=\"bg-primary/10 text-primary font-semibold\">\r\n              {expert.name.split(' ').map(n => n[0]).join('').toUpperCase()}\r\n            </AvatarFallback>\r\n          </Avatar>\r\n\r\n          <div className=\"flex-1 min-w-0\">\r\n            <h3 className=\"font-semibold text-gray-900 text-lg leading-tight truncate\">\r\n              {searchTerm ? highlightSearchTerm(expert.name, searchTerm, \"bg-yellow-200 text-yellow-900 px-0.5 rounded\") : expert.name}\r\n            </h3>\r\n\r\n            {expert.averageRating > 0 && (\r\n              <div className=\"flex items-center gap-1 mt-1\">\r\n                <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400\" />\r\n                <span className=\"text-sm font-medium text-gray-700\">\r\n                  {expert.averageRating.toFixed(1)}\r\n                </span>\r\n                <span className=\"text-sm text-gray-500\">\r\n                  ({expert.totalReviews} reviews)\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"pt-0\">\r\n        <div className=\"text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3\">\r\n          {searchTerm ? highlightAndTruncate(expert.description, searchTerm, 120, \"bg-yellow-200 text-yellow-900 px-0.5 rounded\") : expert.description}\r\n        </div>\r\n\r\n        {/* Labels */}\r\n        {expert.labels && expert.labels.length > 0 && (\r\n          <div className=\"flex flex-wrap gap-1.5 mb-4\">\r\n            {expert.labels.slice(0, 3).map((label, index) => (\r\n              <Badge\r\n                key={index}\r\n                variant=\"secondary\"\r\n                className=\"text-xs px-2 py-1 bg-gray-100 text-gray-700 hover:bg-gray-200\"\r\n              >\r\n                {label}\r\n              </Badge>\r\n            ))}\r\n            {expert.labels.length > 3 && (\r\n              <Badge\r\n                variant=\"outline\"\r\n                className=\"text-xs px-2 py-1 text-gray-500\"\r\n              >\r\n                +{expert.labels.length - 3} more\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Stats */}\r\n        {showStats && (\r\n          <div className=\"grid grid-cols-2 gap-3 mb-4 text-sm\">\r\n            <div className=\"flex items-center gap-2 text-gray-600\">\r\n              <MessageCircle className=\"h-4 w-4\" />\r\n              <span>{formatNumber(expert.totalChats)} chats</span>\r\n            </div>\r\n\r\n            {expert.totalRevenue > 0 && (\r\n              <div className=\"flex items-center gap-2 text-gray-600\">\r\n                <DollarSign className=\"h-4 w-4\" />\r\n                <span>{formatCurrency(expert.totalRevenue)}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Action Button */}\r\n        <Button\r\n          className=\"w-full group-hover:bg-primary group-hover:text-white transition-colors duration-200\"\r\n          variant=\"outline\"\r\n          onClick={(e) => {\r\n            e.stopPropagation();\r\n            handleChatClick();\r\n          }}\r\n        >\r\n          Start Chat\r\n        </Button>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAVA;;;;;;;;;AAoBO,MAAM,aAAwC,CAAC,EACpD,MAAM,EACN,WAAW,EACX,YAAY,EAAE,EACd,YAAY,IAAI,EAChB,aAAa,EAAE,EAChB;IACC,MAAM,kBAAkB;QACtB,IAAI,aAAa;YACf,YAAY,OAAO,EAAE;QACvB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,OAAO,SAAS;YAClB,OAAO,GAAG,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,IAAI,OAAO,MAAM;YACtB,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACtC;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2GACA,qCACA;QAEF,SAAS;;0BAET,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,8OAAC,kIAAA,CAAA,cAAW;oCACV,KAAK,OAAO,QAAQ;oCACpB,KAAK,OAAO,IAAI;oCAChB,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;;;;;;;;;;;;sCAI/D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,aAAa,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,IAAI,EAAE,YAAY,kDAAkD,OAAO,IAAI;;;;;;gCAGzH,OAAO,aAAa,GAAG,mBACtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDACb,OAAO,aAAa,CAAC,OAAO,CAAC;;;;;;sDAEhC,8OAAC;4CAAK,WAAU;;gDAAwB;gDACpC,OAAO,YAAY;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACZ,aAAa,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,WAAW,EAAE,YAAY,KAAK,kDAAkD,OAAO,WAAW;;;;;;oBAI7I,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,mBACvC,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACrC,8OAAC,iIAAA,CAAA,QAAK;oCAEJ,SAAQ;oCACR,WAAU;8CAET;mCAJI;;;;;4BAOR,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;;oCACX;oCACG,OAAO,MAAM,CAAC,MAAM,GAAG;oCAAE;;;;;;;;;;;;;oBAOlC,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;;4CAAM,aAAa,OAAO,UAAU;4CAAE;;;;;;;;;;;;;4BAGxC,OAAO,YAAY,GAAG,mBACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAM,eAAe,OAAO,YAAY;;;;;;;;;;;;;;;;;;kCAOjD,8OAAC,kIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAQ;wBACR,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;kCACD;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/contexts/FilterProvider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useReducer, useEffect, ReactNode } from \"react\";\r\nimport { FilterState, FilterContextType, FilterType, TimelineType } from \"@/types/filters\";\r\n\r\n// Initial state\r\nconst initialState: FilterState = {\r\n  selectedFilter: 'recommended',\r\n  selectedTimeline: 'last-30-days',\r\n  searchQuery: '',\r\n  isLoading: false\r\n};\r\n\r\n// Action types\r\ntype FilterAction =\r\n  | { type: 'SET_FILTER'; payload: FilterType }\r\n  | { type: 'SET_TIMELINE'; payload: TimelineType }\r\n  | { type: 'SET_SEARCH_QUERY'; payload: string }\r\n  | { type: 'SET_LOADING'; payload: boolean }\r\n  | { type: 'RESET_FILTERS' }\r\n  | { type: 'LOAD_FROM_STORAGE'; payload: Partial<FilterState> };\r\n\r\n// Reducer\r\nconst filterReducer = (state: FilterState, action: FilterAction): FilterState => {\r\n  switch (action.type) {\r\n    case 'SET_FILTER':\r\n      return { ...state, selectedFilter: action.payload };\r\n    case 'SET_TIMELINE':\r\n      return { ...state, selectedTimeline: action.payload };\r\n    case 'SET_SEARCH_QUERY':\r\n      return { ...state, searchQuery: action.payload };\r\n    case 'SET_LOADING':\r\n      return { ...state, isLoading: action.payload };\r\n    case 'RESET_FILTERS':\r\n      return { ...initialState };\r\n    case 'LOAD_FROM_STORAGE':\r\n      return { ...state, ...action.payload };\r\n    default:\r\n      return state;\r\n  }\r\n};\r\n\r\n// Context\r\nexport const FilterContext = createContext<FilterContextType | undefined>(undefined);\r\n\r\n// Storage keys\r\nconst STORAGE_KEYS = {\r\n  FILTER: 'marketplace_filter',\r\n  TIMELINE: 'marketplace_timeline',\r\n  SEARCH: 'marketplace_search'\r\n};\r\n\r\n// Provider component\r\ninterface FilterProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const FilterProvider: React.FC<FilterProviderProps> = ({ children }) => {\r\n  const [state, dispatch] = useReducer(filterReducer, initialState);\r\n\r\n  // Load from session storage on mount\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      try {\r\n        const savedFilter = sessionStorage.getItem(STORAGE_KEYS.FILTER) as FilterType;\r\n        const savedTimeline = sessionStorage.getItem(STORAGE_KEYS.TIMELINE) as TimelineType;\r\n        const savedSearch = sessionStorage.getItem(STORAGE_KEYS.SEARCH);\r\n\r\n        const savedState: Partial<FilterState> = {};\r\n\r\n        if (savedFilter && ['trending', 'most-popular', 'top-rated', 'recommended', 'newest'].includes(savedFilter)) {\r\n          savedState.selectedFilter = savedFilter;\r\n        }\r\n\r\n        if (savedTimeline && ['last-7-days', 'last-30-days', 'all-time'].includes(savedTimeline)) {\r\n          savedState.selectedTimeline = savedTimeline;\r\n        }\r\n\r\n        if (savedSearch) {\r\n          savedState.searchQuery = savedSearch;\r\n        }\r\n\r\n        if (Object.keys(savedState).length > 0) {\r\n          dispatch({ type: 'LOAD_FROM_STORAGE', payload: savedState });\r\n        }\r\n      } catch (error) {\r\n        console.warn('Failed to load filter state from session storage:', error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Save to session storage when state changes\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      try {\r\n        sessionStorage.setItem(STORAGE_KEYS.FILTER, state.selectedFilter);\r\n        sessionStorage.setItem(STORAGE_KEYS.TIMELINE, state.selectedTimeline);\r\n        sessionStorage.setItem(STORAGE_KEYS.SEARCH, state.searchQuery);\r\n      } catch (error) {\r\n        console.warn('Failed to save filter state to session storage:', error);\r\n      }\r\n    }\r\n  }, [state.selectedFilter, state.selectedTimeline, state.searchQuery]);\r\n\r\n  // Actions\r\n  const setFilter = (filter: FilterType) => {\r\n    dispatch({ type: 'SET_FILTER', payload: filter });\r\n  };\r\n\r\n  const setTimeline = (timeline: TimelineType) => {\r\n    dispatch({ type: 'SET_TIMELINE', payload: timeline });\r\n  };\r\n\r\n  const setSearchQuery = (query: string) => {\r\n    dispatch({ type: 'SET_SEARCH_QUERY', payload: query });\r\n  };\r\n\r\n  const resetFilters = () => {\r\n    dispatch({ type: 'RESET_FILTERS' });\r\n    // Clear session storage\r\n    if (typeof window !== 'undefined') {\r\n      try {\r\n        sessionStorage.removeItem(STORAGE_KEYS.FILTER);\r\n        sessionStorage.removeItem(STORAGE_KEYS.TIMELINE);\r\n        sessionStorage.removeItem(STORAGE_KEYS.SEARCH);\r\n      } catch (error) {\r\n        console.warn('Failed to clear filter state from session storage:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  const contextValue: FilterContextType = {\r\n    state,\r\n    actions: {\r\n      setFilter,\r\n      setTimeline,\r\n      setSearchQuery,\r\n      resetFilters\r\n    }\r\n  };\r\n\r\n  return (\r\n    <FilterContext.Provider value={contextValue}>\r\n      {children}\r\n    </FilterContext.Provider>\r\n  );\r\n};"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKA,gBAAgB;AAChB,MAAM,eAA4B;IAChC,gBAAgB;IAChB,kBAAkB;IAClB,aAAa;IACb,WAAW;AACb;AAWA,UAAU;AACV,MAAM,gBAAgB,CAAC,OAAoB;IACzC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,gBAAgB,OAAO,OAAO;YAAC;QACpD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,kBAAkB,OAAO,OAAO;YAAC;QACtD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,aAAa,OAAO,OAAO;YAAC;QACjD,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,OAAO;gBAAE,GAAG,YAAY;YAAC;QAC3B,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,GAAG,OAAO,OAAO;YAAC;QACvC;YACE,OAAO;IACX;AACF;AAGO,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE1E,eAAe;AACf,MAAM,eAAe;IACnB,QAAQ;IACR,UAAU;IACV,QAAQ;AACV;AAOO,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,eAAe;IAEpD,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IA2BF,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;;IASF,GAAG;QAAC,MAAM,cAAc;QAAE,MAAM,gBAAgB;QAAE,MAAM,WAAW;KAAC;IAEpE,UAAU;IACV,MAAM,YAAY,CAAC;QACjB,SAAS;YAAE,MAAM;YAAc,SAAS;QAAO;IACjD;IAEA,MAAM,cAAc,CAAC;QACnB,SAAS;YAAE,MAAM;YAAgB,SAAS;QAAS;IACrD;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAoB,SAAS;QAAM;IACtD;IAEA,MAAM,eAAe;QACnB,SAAS;YAAE,MAAM;QAAgB;QACjC,wBAAwB;QACxB;;IASF;IAEA,MAAM,eAAkC;QACtC;QACA,SAAS;YACP;YACA;YACA;YACA;QACF;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/hooks/useFilters.ts"], "sourcesContent": ["import { useContext } from \"react\";\r\nimport { FilterContext } from \"@/contexts/FilterProvider\";\r\nimport { FilterContextType } from \"@/types/filters\";\r\n\r\n/**\r\n * Custom hook to access filter context\r\n * Must be used within a FilterProvider\r\n */\r\nexport const useFilters = (): FilterContextType => {\r\n  const context = useContext(FilterContext);\r\n\r\n  if (context === undefined) {\r\n    throw new Error('useFilters must be used within a FilterProvider');\r\n  }\r\n\r\n  return context;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA;;;AAOO,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,kIAAA,CAAA,gBAAa;IAExC,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/types/filters.ts"], "sourcesContent": ["// Filter types for marketplace filtering system\r\n\r\nexport type FilterType = 'trending' | 'most-popular' | 'top-rated' | 'recommended' | 'newest';\r\n\r\nexport type TimelineType = 'last-7-days' | 'last-30-days' | 'all-time';\r\n\r\nexport interface FilterOption {\r\n  id: FilterType;\r\n  label: string;\r\n  hasTimeline: boolean;\r\n  icon: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface TimelineOption {\r\n  id: TimelineType;\r\n  label: string;\r\n  days?: number;\r\n}\r\n\r\nexport interface FilterState {\r\n  selectedFilter: FilterType;\r\n  selectedTimeline: TimelineType;\r\n  searchQuery: string;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport interface FilterContextType {\r\n  state: FilterState;\r\n  actions: {\r\n    setFilter: (filter: FilterType) => void;\r\n    setTimeline: (timeline: TimelineType) => void;\r\n    setSearchQuery: (query: string) => void;\r\n    resetFilters: () => void;\r\n  };\r\n}\r\n\r\nexport interface FilterParams {\r\n  filter: FilterType;\r\n  timeline?: TimelineType;\r\n  search?: string;\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\nexport interface Expert {\r\n  id: number;\r\n  name: string;\r\n  description: string;\r\n  imageUrl?: string;\r\n  labels: string[];\r\n  totalChats: number;\r\n  totalRevenue: number;\r\n  averageRating: number;\r\n  totalReviews: number;\r\n  createdAt: string;\r\n  pricingPercentage: number;\r\n}\r\n\r\nexport interface FilteredExpertsResponse {\r\n  experts: Expert[];\r\n  pagination: {\r\n    page: number;\r\n    limit: number;\r\n    total: number;\r\n    totalPages: number;\r\n  };\r\n  appliedFilters: {\r\n    filter: FilterType;\r\n    timeline?: TimelineType;\r\n    search?: string;\r\n  };\r\n}\r\n\r\nexport interface FilterAnalytics {\r\n  filterUsage: Array<{\r\n    filter_type: string;\r\n    timeline_filter?: string;\r\n    usage_count: number;\r\n    avg_results: number;\r\n    unique_users: number;\r\n    unique_sessions: number;\r\n  }>;\r\n  searchQueries: Array<{\r\n    search_query: string;\r\n    usage_count: number;\r\n    avg_results: number;\r\n  }>;\r\n  dailyTrends: Array<{\r\n    date: string;\r\n    total_usage: number;\r\n    unique_users: number;\r\n  }>;\r\n  period: string;\r\n}\r\n\r\n// Constants\r\nexport const FILTER_OPTIONS: FilterOption[] = [\r\n  { id: 'recommended', label: 'Recommended for You', hasTimeline: false, icon: '💡' },\r\n  { id: 'trending', label: 'Trending', hasTimeline: false, icon: '📈' },\r\n  { id: 'most-popular', label: 'Most Popular', hasTimeline: true, icon: '🔥' },\r\n  { id: 'top-rated', label: 'Top Rated', hasTimeline: true, icon: '⭐' },\r\n  { id: 'newest', label: 'Newest', hasTimeline: false, icon: '🆕' }\r\n];\r\n\r\nexport const TIMELINE_OPTIONS: TimelineOption[] = [\r\n  { id: 'last-30-days', label: 'Last 30 Days', days: 30 },\r\n  { id: 'last-7-days', label: 'Last 7 Days', days: 7 },\r\n  { id: 'all-time', label: 'All Time' }\r\n];"], "names": [], "mappings": "AAAA,gDAAgD;;;;;AAiGzC,MAAM,iBAAiC;IAC5C;QAAE,IAAI;QAAe,OAAO;QAAuB,aAAa;QAAO,MAAM;IAAK;IAClF;QAAE,IAAI;QAAY,OAAO;QAAY,aAAa;QAAO,MAAM;IAAK;IACpE;QAAE,IAAI;QAAgB,OAAO;QAAgB,aAAa;QAAM,MAAM;IAAK;IAC3E;QAAE,IAAI;QAAa,OAAO;QAAa,aAAa;QAAM,MAAM;IAAI;IACpE;QAAE,IAAI;QAAU,OAAO;QAAU,aAAa;QAAO,MAAM;IAAK;CACjE;AAEM,MAAM,mBAAqC;IAChD;QAAE,IAAI;QAAgB,OAAO;QAAgB,MAAM;IAAG;IACtD;QAAE,IAAI;QAAe,OAAO;QAAe,MAAM;IAAE;IACnD;QAAE,IAAI;QAAY,OAAO;IAAW;CACrC", "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/MarketplaceFilters.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { FilterSelect } from \"./FilterSelect\";\r\nimport { TimelineSelect } from \"./TimelineSelect\";\r\nimport { useFilters } from \"@/hooks/useFilters\";\r\nimport { FILTER_OPTIONS, TIMELINE_OPTIONS } from \"@/types/filters\";\r\nimport { Search, X, RotateCcw } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface MarketplaceFiltersProps {\r\n  className?: string;\r\n  onFiltersChange?: () => void;\r\n}\r\n\r\nexport const MarketplaceFilters: React.FC<MarketplaceFiltersProps> = ({\r\n  className = \"\",\r\n  onFiltersChange\r\n}) => {\r\n  const { state, actions } = useFilters();\r\n  const [searchInput, setSearchInput] = useState(state.searchQuery);\r\n  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);\r\n\r\n  // Get the selected filter option to determine if timeline should be shown\r\n  const selectedFilterOption = FILTER_OPTIONS.find(option => option.id === state.selectedFilter);\r\n  const showTimeline = selectedFilterOption?.hasTimeline || false;\r\n\r\n  // Debounced search handling\r\n  useEffect(() => {\r\n    if (searchTimeout) {\r\n      clearTimeout(searchTimeout);\r\n    }\r\n\r\n    const timeout = setTimeout(() => {\r\n      actions.setSearchQuery(searchInput);\r\n    }, 300); // 300ms debounce\r\n\r\n    setSearchTimeout(timeout);\r\n\r\n    return () => {\r\n      if (timeout) {\r\n        clearTimeout(timeout);\r\n      }\r\n    };\r\n  }, [searchInput, actions, searchTimeout]);\r\n\r\n  // Notify parent when filters change\r\n  useEffect(() => {\r\n    if (onFiltersChange) {\r\n      onFiltersChange();\r\n    }\r\n  }, [state.selectedFilter, state.selectedTimeline, state.searchQuery, onFiltersChange]);\r\n\r\n  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchInput(e.target.value);\r\n  };\r\n\r\n  const clearSearch = () => {\r\n    setSearchInput('');\r\n    actions.setSearchQuery('');\r\n  };\r\n\r\n  const resetAllFilters = () => {\r\n    setSearchInput('');\r\n    actions.resetFilters();\r\n  };\r\n\r\n  const hasActiveFilters = state.searchQuery ||\r\n    state.selectedFilter !== 'recommended' ||\r\n    state.selectedTimeline !== 'last-30-days';\r\n\r\n  return (\r\n    <div className={cn(\"space-y-4\", className)}>\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <h2 className=\"text-xl font-semibold text-gray-900\">\r\n          Find AI Experts\r\n        </h2>\r\n\r\n        {hasActiveFilters && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={resetAllFilters}\r\n            className=\"text-gray-500 hover:text-gray-700\"\r\n          >\r\n            <RotateCcw className=\"h-4 w-4 mr-2\" />\r\n            Reset Filters\r\n          </Button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Search Bar */}\r\n      <div className=\"relative\">\r\n        <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\r\n        <Input\r\n          type=\"text\"\r\n          placeholder=\"Search experts by name, description, or skills...\"\r\n          value={searchInput}\r\n          onChange={handleSearchInputChange}\r\n          className=\"pl-10 pr-10 h-12 bg-white border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20\"\r\n        />\r\n        {searchInput && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={clearSearch}\r\n            className=\"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-gray-100\"\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Filter Controls */}\r\n      <div className=\"flex flex-col sm:flex-row gap-3 sm:items-center\">\r\n        <div className=\"flex-1\">\r\n          <FilterSelect\r\n            options={FILTER_OPTIONS}\r\n            selectedValue={state.selectedFilter}\r\n            onSelect={actions.setFilter}\r\n            disabled={state.isLoading}\r\n            className=\"w-full\"\r\n          />\r\n        </div>\r\n\r\n        {showTimeline && (\r\n          <div className=\"sm:w-auto\">\r\n            <TimelineSelect\r\n              options={TIMELINE_OPTIONS}\r\n              selectedValue={state.selectedTimeline}\r\n              onSelect={actions.setTimeline}\r\n              disabled={state.isLoading}\r\n              className=\"w-full sm:w-[150px]\"\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Active Filters Summary */}\r\n      {hasActiveFilters && (\r\n        <div className=\"flex flex-wrap gap-2 text-sm text-gray-600\">\r\n          <span className=\"font-medium\">Active filters:</span>\r\n\r\n          {state.selectedFilter !== 'recommended' && (\r\n            <span className=\"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md\">\r\n              {selectedFilterOption?.label}\r\n            </span>\r\n          )}\r\n\r\n          {showTimeline && state.selectedTimeline !== 'last-30-days' && (\r\n            <span className=\"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md\">\r\n              {TIMELINE_OPTIONS.find(opt => opt.id === state.selectedTimeline)?.label}\r\n            </span>\r\n          )}\r\n\r\n          {state.searchQuery && (\r\n            <span className=\"inline-flex items-center px-2 py-1 bg-primary/10 text-primary rounded-md\">\r\n              Search: \"{state.searchQuery}\"\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;;AAiBO,MAAM,qBAAwD,CAAC,EACpE,YAAY,EAAE,EACd,eAAe,EAChB;IACC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,WAAW;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAE1E,0EAA0E;IAC1E,MAAM,uBAAuB,uHAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,MAAM,cAAc;IAC7F,MAAM,eAAe,sBAAsB,eAAe;IAE1D,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,aAAa;QACf;QAEA,MAAM,UAAU,WAAW;YACzB,QAAQ,cAAc,CAAC;QACzB,GAAG,MAAM,iBAAiB;QAE1B,iBAAiB;QAEjB,OAAO;YACL,IAAI,SAAS;gBACX,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAa;QAAS;KAAc;IAExC,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC,MAAM,cAAc;QAAE,MAAM,gBAAgB;QAAE,MAAM,WAAW;QAAE;KAAgB;IAErF,MAAM,0BAA0B,CAAC;QAC/B,eAAe,EAAE,MAAM,CAAC,KAAK;IAC/B;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,QAAQ,cAAc,CAAC;IACzB;IAEA,MAAM,kBAAkB;QACtB,eAAe;QACf,QAAQ,YAAY;IACtB;IAEA,MAAM,mBAAmB,MAAM,WAAW,IACxC,MAAM,cAAc,KAAK,iBACzB,MAAM,gBAAgB,KAAK;IAE7B,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;oBAInD,kCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAO5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC,iIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU;wBACV,WAAU;;;;;;oBAEX,6BACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAMnB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iJAAA,CAAA,eAAY;4BACX,SAAS,uHAAA,CAAA,iBAAc;4BACvB,eAAe,MAAM,cAAc;4BACnC,UAAU,QAAQ,SAAS;4BAC3B,UAAU,MAAM,SAAS;4BACzB,WAAU;;;;;;;;;;;oBAIb,8BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,mJAAA,CAAA,iBAAc;4BACb,SAAS,uHAAA,CAAA,mBAAgB;4BACzB,eAAe,MAAM,gBAAgB;4BACrC,UAAU,QAAQ,WAAW;4BAC7B,UAAU,MAAM,SAAS;4BACzB,WAAU;;;;;;;;;;;;;;;;;YAOjB,kCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAc;;;;;;oBAE7B,MAAM,cAAc,KAAK,+BACxB,8OAAC;wBAAK,WAAU;kCACb,sBAAsB;;;;;;oBAI1B,gBAAgB,MAAM,gBAAgB,KAAK,gCAC1C,8OAAC;wBAAK,WAAU;kCACb,uHAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,gBAAgB,GAAG;;;;;;oBAIrE,MAAM,WAAW,kBAChB,8OAAC;wBAAK,WAAU;;4BAA2E;4BAC/E,MAAM,WAAW;4BAAC;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/ExpertsGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { ExpertCard } from \"./ExpertCard\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON>pert } from \"@/types/filters\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Loader2, AlertCircle, Search } from \"lucide-react\";\r\n\r\ninterface ExpertsGridProps {\r\n  experts: Expert[];\r\n  loading?: boolean;\r\n  error?: string | null;\r\n  onChatClick?: (expertId: number) => void;\r\n  onLoadMore?: () => void;\r\n  hasMore?: boolean;\r\n  loadingMore?: boolean;\r\n  className?: string;\r\n  emptyStateMessage?: string;\r\n  searchTerm?: string;\r\n}\r\n\r\nexport const ExpertsGrid: React.FC<ExpertsGridProps> = ({\r\n  experts,\r\n  loading = false,\r\n  error = null,\r\n  onChatClick,\r\n  onLoadMore,\r\n  hasMore = false,\r\n  loadingMore = false,\r\n  className = \"\",\r\n  emptyStateMessage = \"No experts found matching your criteria.\",\r\n  searchTerm = \"\"\r\n}) => {\r\n  // Loading state\r\n  if (loading && experts.length === 0) {\r\n    return (\r\n      <div className={cn(\"flex items-center justify-center py-12\", className)}>\r\n        <div className=\"text-center\">\r\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary mx-auto mb-4\" />\r\n          <p className=\"text-gray-600\">Loading experts...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <div className={cn(\"flex items-center justify-center py-12\", className)}>\r\n        <div className=\"text-center max-w-md\">\r\n          <AlertCircle className=\"h-8 w-8 text-red-500 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\r\n            Something went wrong\r\n          </h3>\r\n          <p className=\"text-gray-600 mb-4\">{error}</p>\r\n          <Button\r\n            onClick={() => window.location.reload()}\r\n            variant=\"outline\"\r\n          >\r\n            Try Again\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Empty state\r\n  if (!loading && experts.length === 0) {\r\n    return (\r\n      <div className={cn(\"flex items-center justify-center py-12\", className)}>\r\n        <div className=\"text-center max-w-md\">\r\n          <Search className=\"h-8 w-8 text-gray-400 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\r\n            No experts found\r\n          </h3>\r\n          <p className=\"text-gray-600\">{emptyStateMessage}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn(\"space-y-6\", className)}>\r\n      {/* Results count */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <p className=\"text-sm text-gray-600\">\r\n          {experts.length} expert{experts.length !== 1 ? 's' : ''} found\r\n        </p>\r\n      </div>\r\n\r\n      {/* Grid */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\r\n        {experts.map((expert) => (\r\n          <ExpertCard\r\n            key={expert.id}\r\n            expert={expert}\r\n            onChatClick={onChatClick}\r\n            className=\"h-full\"\r\n            searchTerm={searchTerm}\r\n          />\r\n        ))}\r\n      </div>\r\n\r\n      {/* Load More Button */}\r\n      {hasMore && onLoadMore && (\r\n        <div className=\"flex justify-center pt-6\">\r\n          <Button\r\n            onClick={onLoadMore}\r\n            disabled={loadingMore}\r\n            variant=\"outline\"\r\n            size=\"lg\"\r\n            className=\"min-w-[120px]\"\r\n          >\r\n            {loadingMore ? (\r\n              <>\r\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                Loading...\r\n              </>\r\n            ) : (\r\n              'Load More'\r\n            )}\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Loading more indicator */}\r\n      {loadingMore && (\r\n        <div className=\"flex justify-center py-4\">\r\n          <div className=\"flex items-center gap-2 text-gray-600\">\r\n            <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n            <span className=\"text-sm\">Loading more experts...</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AAAA;AAAA;AAPA;;;;;;AAsBO,MAAM,cAA0C,CAAC,EACtD,OAAO,EACP,UAAU,KAAK,EACf,QAAQ,IAAI,EACZ,WAAW,EACX,UAAU,EACV,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,YAAY,EAAE,EACd,oBAAoB,0CAA0C,EAC9D,aAAa,EAAE,EAChB;IACC,gBAAgB;IAChB,IAAI,WAAW,QAAQ,MAAM,KAAK,GAAG;QACnC,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;sBAC3D,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;sBAC3D,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,SAAQ;kCACT;;;;;;;;;;;;;;;;;IAMT;IAEA,cAAc;IACd,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;sBAC3D,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,8OAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;;;;;;IAItC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBACV,QAAQ,MAAM;wBAAC;wBAAQ,QAAQ,MAAM,KAAK,IAAI,MAAM;wBAAG;;;;;;;;;;;;0BAK5D,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,+IAAA,CAAA,aAAU;wBAET,QAAQ;wBACR,aAAa;wBACb,WAAU;wBACV,YAAY;uBAJP,OAAO,EAAE;;;;;;;;;;YAUnB,WAAW,4BACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS;oBACT,UAAU;oBACV,SAAQ;oBACR,MAAK;oBACL,WAAU;8BAET,4BACC;;0CACE,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAA8B;;uCAInD;;;;;;;;;;;YAOP,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 1662, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,qMAAA,CAAA,aAAgB,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/FilterErrorFallback.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { AlertTriangle, RefreshCw, Filter, TrendingUp } from \"lucide-react\";\r\nimport { useFilters } from \"@/hooks/useFilters\";\r\n\r\ninterface FilterErrorFallbackProps {\r\n  error?: Error;\r\n  onRetry?: () => void;\r\n  showTrendingFallback?: boolean;\r\n}\r\n\r\nexport const FilterErrorFallback: React.FC<FilterErrorFallbackProps> = ({\r\n  error,\r\n  onRetry,\r\n  showTrendingFallback = true\r\n}) => {\r\n  const { actions } = useFilters();\r\n\r\n  const handleFallbackToTrending = () => {\r\n    actions.setFilter('trending');\r\n    actions.setTimeline('last-30-days');\r\n    actions.setSearchQuery('');\r\n  };\r\n\r\n  const handleRetry = () => {\r\n    if (onRetry) {\r\n      onRetry();\r\n    } else {\r\n      window.location.reload();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Error Alert */}\r\n      <Alert variant=\"destructive\">\r\n        <AlertTriangle className=\"h-4 w-4\" />\r\n        <AlertTitle>Filter Error</AlertTitle>\r\n        <AlertDescription>\r\n          {error?.message || \"We're having trouble loading the filtered results. This might be due to a temporary server issue.\"}\r\n        </AlertDescription>\r\n      </Alert>\r\n\r\n      {/* Action Cards */}\r\n      <div className=\"grid gap-4 md:grid-cols-2\">\r\n        {/* Retry Card */}\r\n        <Card>\r\n          <CardContent className=\"p-6\">\r\n            <div className=\"flex items-center space-x-2 mb-3\">\r\n              <RefreshCw className=\"h-5 w-5 text-blue-600\" />\r\n              <h3 className=\"font-semibold\">Try Again</h3>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              Retry the current filter selection. This often resolves temporary connection issues.\r\n            </p>\r\n            <Button onClick={handleRetry} className=\"w-full\">\r\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n              Retry Current Filter\r\n            </Button>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Trending Fallback Card */}\r\n        {showTrendingFallback && (\r\n          <Card>\r\n            <CardContent className=\"p-6\">\r\n              <div className=\"flex items-center space-x-2 mb-3\">\r\n                <TrendingUp className=\"h-5 w-5 text-green-600\" />\r\n                <h3 className=\"font-semibold\">View Trending</h3>\r\n              </div>\r\n              <p className=\"text-sm text-gray-600 mb-4\">\r\n                Switch to trending experts while we resolve the issue with your current filter.\r\n              </p>\r\n              <Button\r\n                onClick={handleFallbackToTrending}\r\n                variant=\"outline\"\r\n                className=\"w-full\"\r\n              >\r\n                <TrendingUp className=\"h-4 w-4 mr-2\" />\r\n                Show Trending Experts\r\n              </Button>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n      </div>\r\n\r\n      {/* Additional Help */}\r\n      <Card className=\"bg-gray-50\">\r\n        <CardContent className=\"p-4\">\r\n          <div className=\"flex items-start space-x-3\">\r\n            <Filter className=\"h-5 w-5 text-gray-500 mt-0.5\" />\r\n            <div>\r\n              <h4 className=\"font-medium text-sm mb-1\">Having persistent issues?</h4>\r\n              <p className=\"text-xs text-gray-600 mb-2\">\r\n                Try clearing your browser cache or switching to a different filter option.\r\n                If the problem continues, please contact our support team.\r\n              </p>\r\n              <div className=\"flex gap-2\">\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"ghost\"\r\n                  onClick={() => localStorage.clear()}\r\n                  className=\"text-xs h-7\"\r\n                >\r\n                  Clear Cache\r\n                </Button>\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"ghost\"\r\n                  onClick={() => window.location.href = '/support'}\r\n                  className=\"text-xs h-7\"\r\n                >\r\n                  Contact Support\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;AAeO,MAAM,sBAA0D,CAAC,EACtE,KAAK,EACL,OAAO,EACP,uBAAuB,IAAI,EAC5B;IACC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;IAE7B,MAAM,2BAA2B;QAC/B,QAAQ,SAAS,CAAC;QAClB,QAAQ,WAAW,CAAC;QACpB,QAAQ,cAAc,CAAC;IACzB;IAEA,MAAM,cAAc;QAClB,IAAI,SAAS;YACX;QACF,OAAO;YACL,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC,iIAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,8OAAC,iIAAA,CAAA,mBAAgB;kCACd,OAAO,WAAW;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAa,WAAU;;sDACtC,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;oBAO3C,sCACC,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;;;;;;;8CAEhC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,SAAQ;oCACR,WAAU;;sDAEV,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAI1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa,KAAK;gDACjC,WAAU;0DACX;;;;;;0DAGD,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gDACtC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 2026, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/hooks/useFilteredExperts.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from \"react\";\r\nimport { FilterParams, FilteredExpertsResponse } from \"@/types/filters\";\r\nimport { api } from \"@/lib/api\";\r\n\r\ninterface UseFilteredExpertsResult {\r\n  data: FilteredExpertsResponse | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  retryCount: number;\r\n  isRetrying: boolean;\r\n  refetch: () => void;\r\n  retry: () => void;\r\n  clearError: () => void;\r\n}\r\n\r\n/**\r\n * Custom hook for fetching filtered experts\r\n */\r\nexport const useFilteredExperts = (params: FilterParams): UseFilteredExpertsResult => {\r\n  const [data, setData] = useState<FilteredExpertsResponse | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [retryCount, setRetryCount] = useState(0);\r\n  const [isRetrying, setIsRetrying] = useState(false);\r\n\r\n  // Use ref to track retry count for auto-retry logic to avoid dependency issues\r\n  const retryCountRef = useRef(0);\r\n\r\n  const fetchExperts = useCallback(async (isRetryAttempt = false) => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    if (isRetryAttempt) {\r\n      setIsRetrying(true);\r\n      retryCountRef.current += 1;\r\n      setRetryCount(retryCountRef.current);\r\n    }\r\n\r\n    try {\r\n      const result = await api.getFilteredExperts({\r\n        filter: params.filter,\r\n        timeline: params.timeline,\r\n        search: params.search,\r\n        page: params.page,\r\n        limit: params.limit\r\n      });\r\n\r\n      setData(result.data);\r\n    } catch (err) {\r\n      let errorMessage = 'An unexpected error occurred';\r\n\r\n      if (err instanceof Error) {\r\n        errorMessage = err.message;\r\n\r\n        // Provide more specific error messages based on error type\r\n        if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {\r\n          errorMessage = 'Network connection error. Please check your internet connection and try again.';\r\n        } else if (err.message.includes('500')) {\r\n          errorMessage = 'Server error. Our team has been notified. Please try again in a few moments.';\r\n        } else if (err.message.includes('404')) {\r\n          errorMessage = 'The requested resource was not found. Please try a different filter.';\r\n        } else if (err.message.includes('403')) {\r\n          errorMessage = 'Access denied. Please log in and try again.';\r\n        }\r\n      }\r\n\r\n      setError(errorMessage);\r\n      console.error('Error fetching filtered experts:', err);\r\n\r\n      // Auto-retry for network errors (max 3 attempts)\r\n      // Use ref value to avoid dependency issues\r\n      if (retryCountRef.current < 3 && (errorMessage.includes('Network') || errorMessage.includes('Server'))) {\r\n        setTimeout(() => {\r\n          fetchExperts(true);\r\n        }, Math.pow(2, retryCountRef.current) * 1000); // Exponential backoff\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n      setIsRetrying(false);\r\n    }\r\n  }, [params.filter, params.timeline, params.search, params.page, params.limit]); // Remove retryCount from dependencies\r\n\r\n  // Reset retry count when parameters change\r\n  useEffect(() => {\r\n    retryCountRef.current = 0;\r\n    setRetryCount(0);\r\n  }, [params.filter, params.timeline, params.search, params.page, params.limit]);\r\n\r\n  // Fetch data when parameters change\r\n  useEffect(() => {\r\n    fetchExperts();\r\n  }, [fetchExperts]);\r\n\r\n  const refetch = useCallback(() => {\r\n    retryCountRef.current = 0; // Reset retry count ref\r\n    setRetryCount(0); // Reset retry count state for UI\r\n    fetchExperts();\r\n  }, [fetchExperts]);\r\n\r\n  const retry = useCallback(() => {\r\n    fetchExperts(true);\r\n  }, [fetchExperts]);\r\n\r\n  const clearError = useCallback(() => {\r\n    setError(null);\r\n    retryCountRef.current = 0; // Reset retry count ref\r\n    setRetryCount(0); // Reset retry count state\r\n  }, []);\r\n\r\n  return {\r\n    data,\r\n    loading,\r\n    error,\r\n    retryCount,\r\n    isRetrying,\r\n    refetch,\r\n    retry,\r\n    clearError\r\n  };\r\n};"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAgBO,MAAM,qBAAqB,CAAC;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,+EAA+E;IAC/E,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,iBAAiB,KAAK;QAC5D,WAAW;QACX,SAAS;QAET,IAAI,gBAAgB;YAClB,cAAc;YACd,cAAc,OAAO,IAAI;YACzB,cAAc,cAAc,OAAO;QACrC;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,iHAAA,CAAA,MAAG,CAAC,kBAAkB,CAAC;gBAC1C,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;gBACrB,MAAM,OAAO,IAAI;gBACjB,OAAO,OAAO,KAAK;YACrB;YAEA,QAAQ,OAAO,IAAI;QACrB,EAAE,OAAO,KAAK;YACZ,IAAI,eAAe;YAEnB,IAAI,eAAe,OAAO;gBACxB,eAAe,IAAI,OAAO;gBAE1B,2DAA2D;gBAC3D,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,sBAAsB,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB;oBACnF,eAAe;gBACjB,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACtC,eAAe;gBACjB,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACtC,eAAe;gBACjB,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ;oBACtC,eAAe;gBACjB;YACF;YAEA,SAAS;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAElD,iDAAiD;YACjD,2CAA2C;YAC3C,IAAI,cAAc,OAAO,GAAG,KAAK,CAAC,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,SAAS,GAAG;gBACtG,WAAW;oBACT,aAAa;gBACf,GAAG,KAAK,GAAG,CAAC,GAAG,cAAc,OAAO,IAAI,OAAO,sBAAsB;YACvE;QACF,SAAU;YACR,WAAW;YACX,cAAc;QAChB;IACF,GAAG;QAAC,OAAO,MAAM;QAAE,OAAO,QAAQ;QAAE,OAAO,MAAM;QAAE,OAAO,IAAI;QAAE,OAAO,KAAK;KAAC,GAAG,sCAAsC;IAEtH,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc,OAAO,GAAG;QACxB,cAAc;IAChB,GAAG;QAAC,OAAO,MAAM;QAAE,OAAO,QAAQ;QAAE,OAAO,MAAM;QAAE,OAAO,IAAI;QAAE,OAAO,KAAK;KAAC;IAE7E,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,cAAc,OAAO,GAAG,GAAG,wBAAwB;QACnD,cAAc,IAAI,iCAAiC;QACnD;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,aAAa;IACf,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;QACT,cAAc,OAAO,GAAG,GAAG,wBAAwB;QACnD,cAAc,IAAI,0BAA0B;IAC9C,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/common/ErrorBoundary.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { Component, ErrorInfo, ReactNode } from \"react\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { AlertTriangle, RefreshCw } from \"lucide-react\";\r\n\r\ninterface Props {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\r\n}\r\n\r\ninterface State {\r\n  hasError: boolean;\r\n  error?: Error;\r\n}\r\n\r\nexport class ErrorBoundary extends Component<Props, State> {\r\n  constructor(props: Props) {\r\n    super(props);\r\n    this.state = { hasError: false };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): State {\r\n    // Update state so the next render will show the fallback UI\r\n    return { hasError: true, error };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\r\n    // Log the error to an error reporting service\r\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\r\n\r\n    // Call the onError callback if provided\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo);\r\n    }\r\n  }\r\n\r\n  handleRetry = () => {\r\n    this.setState({ hasError: false, error: undefined });\r\n  };\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      // Custom fallback UI\r\n      if (this.props.fallback) {\r\n        return this.props.fallback;\r\n      }\r\n\r\n      // Default fallback UI using shadcn/ui components\r\n      return (\r\n        <div className=\"flex items-center justify-center min-h-[200px] p-4\">\r\n          <Alert className=\"max-w-md\">\r\n            <AlertTriangle className=\"h-4 w-4\" />\r\n            <AlertTitle>Something went wrong</AlertTitle>\r\n            <AlertDescription className=\"mt-2\">\r\n              <p className=\"mb-4\">\r\n                We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.\r\n              </p>\r\n              <div className=\"flex gap-2\">\r\n                <Button\r\n                  onClick={this.handleRetry}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                >\r\n                  <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n                  Try Again\r\n                </Button>\r\n                <Button\r\n                  onClick={() => window.location.reload()}\r\n                  size=\"sm\"\r\n                >\r\n                  Refresh Page\r\n                </Button>\r\n              </div>\r\n            </AlertDescription>\r\n          </Alert>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\n// Hook-based error boundary for functional components\r\nexport const useErrorHandler = () => {\r\n  const [error, setError] = React.useState<Error | null>(null);\r\n\r\n  const resetError = () => setError(null);\r\n\r\n  const handleError = React.useCallback((error: Error) => {\r\n    setError(error);\r\n    console.error('Error caught by useErrorHandler:', error);\r\n  }, []);\r\n\r\n  React.useEffect(() => {\r\n    if (error) {\r\n      throw error;\r\n    }\r\n  }, [error]);\r\n\r\n  return { handleError, resetError };\r\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAkBO,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IAC1C,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAS;QACnD,4DAA4D;QAC5D,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,8CAA8C;QAC9C,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IACF;IAEA,cAAc;QACZ,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,iDAAiD;YACjD,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;sCACf,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC,iIAAA,CAAA,aAAU;sCAAC;;;;;;sCACZ,8OAAC,iIAAA,CAAA,mBAAgB;4BAAC,WAAU;;8CAC1B,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAI,CAAC,WAAW;4CACzB,MAAK;4CACL,SAAQ;;8DAER,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGxC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4CACrC,MAAK;sDACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQb;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAe;IAEvD,MAAM,aAAa,IAAM,SAAS;IAElC,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACrC,SAAS;QACT,QAAQ,KAAK,CAAC,oCAAoC;IACpD,GAAG,EAAE;IAEL,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,OAAO;YACT,MAAM;QACR;IACF,GAAG;QAAC;KAAM;IAEV,OAAO;QAAE;QAAa;IAAW;AACnC", "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/MarketplacePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback } from \"react\";\r\nimport { FilterProvider } from \"@/contexts/FilterProvider\";\r\nimport { MarketplaceFilters } from \"./MarketplaceFilters\";\r\nimport { ExpertsGrid } from \"./ExpertsGrid\";\r\nimport { useFilters } from \"@/hooks/useFilters\";\r\nimport { useFilteredExperts } from \"@/hooks/useFilteredExperts\";\r\nimport { ErrorBoundary } from \"@/components/common/ErrorBoundary\";\r\nimport { FilterErrorFallback } from \"./FilterErrorFallback\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface MarketplacePageProps {\r\n  className?: string;\r\n  onChatClick?: (expertId: number) => void;\r\n}\r\n\r\n// Inner component that uses the filter context\r\nconst MarketplaceContent: React.FC<MarketplacePageProps> = ({\r\n  className = \"\",\r\n  onChatClick\r\n}) => {\r\n  const { state } = useFilters();\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [allExperts, setAllExperts] = useState<any[]>([]);\r\n\r\n  // Fetch filtered experts\r\n  const { data, loading, error, retry } = useFilteredExperts({\r\n    filter: state.selectedFilter,\r\n    timeline: state.selectedTimeline,\r\n    search: state.searchQuery,\r\n    page: currentPage,\r\n    limit: 20\r\n  });\r\n\r\n  // Handle filter changes - reset to first page and clear accumulated experts\r\n  const handleFiltersChange = useCallback(() => {\r\n    setCurrentPage(1);\r\n    setAllExperts([]);\r\n  }, []);\r\n\r\n  // Handle load more\r\n  const handleLoadMore = useCallback(() => {\r\n    if (data && data.pagination.page < data.pagination.totalPages) {\r\n      setCurrentPage(prev => prev + 1);\r\n    }\r\n  }, [data]);\r\n\r\n  // Accumulate experts when loading more pages\r\n  React.useEffect(() => {\r\n    if (data) {\r\n      if (currentPage === 1) {\r\n        // First page or filter change - replace all experts\r\n        setAllExperts(data.experts);\r\n      } else {\r\n        // Additional pages - append to existing experts\r\n        setAllExperts(prev => [...prev, ...data.experts]);\r\n      }\r\n    }\r\n  }, [data, currentPage]);\r\n\r\n  const hasMore = data ? data.pagination.page < data.pagination.totalPages : false;\r\n  const loadingMore = loading && currentPage > 1;\r\n\r\n  return (\r\n    <div className={cn(\"container mx-auto px-4 py-8 space-y-8\", className)}>\r\n      {/* Filters Section */}\r\n      <div className=\"bg-white rounded-lg border border-gray-200 p-6 shadow-sm\">\r\n        <MarketplaceFilters onFiltersChange={handleFiltersChange} />\r\n      </div>\r\n\r\n      {/* Results Section */}\r\n      <div className=\"bg-white rounded-lg border border-gray-200 p-6 shadow-sm\">\r\n        {error ? (\r\n          <FilterErrorFallback\r\n            error={new Error(error)}\r\n            onRetry={retry}\r\n            showTrendingFallback={state.selectedFilter !== 'trending'}\r\n          />\r\n        ) : (\r\n          <ExpertsGrid\r\n            experts={allExperts}\r\n            loading={loading && currentPage === 1}\r\n            error={null}\r\n            onChatClick={onChatClick}\r\n            onLoadMore={handleLoadMore}\r\n            hasMore={hasMore}\r\n            loadingMore={loadingMore}\r\n            searchTerm={state.searchQuery}\r\n            emptyStateMessage={\r\n              state.searchQuery\r\n                ? `No experts found for \"${state.searchQuery}\". Try adjusting your search or filters.`\r\n                : \"No experts found matching your current filters. Try adjusting your criteria.\"\r\n            }\r\n          />\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Main component with provider and error boundary\r\nexport const MarketplacePage: React.FC<MarketplacePageProps> = (props) => {\r\n  return (\r\n    <ErrorBoundary\r\n      onError={(error, errorInfo) => {\r\n        console.error('Marketplace error:', error, errorInfo);\r\n        // Here you could send error to monitoring service\r\n      }}\r\n    >\r\n      <FilterProvider>\r\n        <ErrorBoundary\r\n          fallback={\r\n            <FilterErrorFallback\r\n              error={new Error('Filter system error')}\r\n              showTrendingFallback={true}\r\n            />\r\n          }\r\n        >\r\n          <MarketplaceContent {...props} />\r\n        </ErrorBoundary>\r\n      </FilterProvider>\r\n    </ErrorBoundary>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAiBA,+CAA+C;AAC/C,MAAM,qBAAqD,CAAC,EAC1D,YAAY,EAAE,EACd,WAAW,EACZ;IACC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEtD,yBAAyB;IACzB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;QACzD,QAAQ,MAAM,cAAc;QAC5B,UAAU,MAAM,gBAAgB;QAChC,QAAQ,MAAM,WAAW;QACzB,MAAM;QACN,OAAO;IACT;IAEA,4EAA4E;IAC5E,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,eAAe;QACf,cAAc,EAAE;IAClB,GAAG,EAAE;IAEL,mBAAmB;IACnB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,QAAQ,KAAK,UAAU,CAAC,IAAI,GAAG,KAAK,UAAU,CAAC,UAAU,EAAE;YAC7D,eAAe,CAAA,OAAQ,OAAO;QAChC;IACF,GAAG;QAAC;KAAK;IAET,6CAA6C;IAC7C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,MAAM;YACR,IAAI,gBAAgB,GAAG;gBACrB,oDAAoD;gBACpD,cAAc,KAAK,OAAO;YAC5B,OAAO;gBACL,gDAAgD;gBAChD,cAAc,CAAA,OAAQ;2BAAI;2BAAS,KAAK,OAAO;qBAAC;YAClD;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,MAAM,UAAU,OAAO,KAAK,UAAU,CAAC,IAAI,GAAG,KAAK,UAAU,CAAC,UAAU,GAAG;IAC3E,MAAM,cAAc,WAAW,cAAc;IAE7C,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;;0BAE1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uJAAA,CAAA,qBAAkB;oBAAC,iBAAiB;;;;;;;;;;;0BAIvC,8OAAC;gBAAI,WAAU;0BACZ,sBACC,8OAAC,wJAAA,CAAA,sBAAmB;oBAClB,OAAO,IAAI,MAAM;oBACjB,SAAS;oBACT,sBAAsB,MAAM,cAAc,KAAK;;;;;6EAGjD,8OAAC,gJAAA,CAAA,cAAW;oBACV,SAAS;oBACT,SAAS,WAAW,gBAAgB;oBACpC,OAAO;oBACP,aAAa;oBACb,YAAY;oBACZ,SAAS;oBACT,aAAa;oBACb,YAAY,MAAM,WAAW;oBAC7B,mBACE,MAAM,WAAW,GACb,CAAC,sBAAsB,EAAE,MAAM,WAAW,CAAC,wCAAwC,CAAC,GACpF;;;;;;;;;;;;;;;;;AAOlB;AAGO,MAAM,kBAAkD,CAAC;IAC9D,qBACE,8OAAC,6IAAA,CAAA,gBAAa;QACZ,SAAS,CAAC,OAAO;YACf,QAAQ,KAAK,CAAC,sBAAsB,OAAO;QAC3C,kDAAkD;QACpD;kBAEA,cAAA,8OAAC,kIAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,6IAAA,CAAA,gBAAa;gBACZ,wBACE,8OAAC,wJAAA,CAAA,sBAAmB;oBAClB,OAAO,IAAI,MAAM;oBACjB,sBAAsB;;;;;;0BAI1B,cAAA,8OAAC;oBAAoB,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 2471, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/marketplace/index.ts"], "sourcesContent": ["// Marketplace filtering system components\r\nexport { FilterSelect } from './FilterSelect';\r\nexport { TimelineSelect } from './TimelineSelect';\r\nexport { ExpertCard } from './ExpertCard';\r\nexport { MarketplaceFilters } from './MarketplaceFilters';\r\nexport { ExpertsGrid } from './ExpertsGrid';\r\nexport { FilterErrorFallback } from './FilterErrorFallback';\r\nexport { MarketplacePage } from './MarketplacePage';"], "names": [], "mappings": "AAAA,0CAA0C;;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2504, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/app/ai-experts/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport { MarketplacePage } from \"@/components/marketplace\";\r\n\r\nconst AIExpertsPage = () => {\r\n  const router = useRouter();\r\n\r\n  const handleChatClick = (expertId: number) => {\r\n    router.push(`/chat?expert=${expertId}`);\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      <div className=\"max-w-7xl mx-auto px-4 py-8\">\r\n        <div className=\"mb-8\">\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">AI Experts</h1>\r\n          <p className=\"text-gray-600\">\r\n            Discover and connect with AI specialists\r\n          </p>\r\n        </div>\r\n\r\n        <MarketplacePage onChatClick={handleChatClick} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AIExpertsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,gBAAgB;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB,CAAC;QACvB,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,UAAU;IACxC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC,oJAAA,CAAA,kBAAe;oBAAC,aAAa;;;;;;;;;;;;;;;;;AAItC;uCAEe", "debugId": null}}]}