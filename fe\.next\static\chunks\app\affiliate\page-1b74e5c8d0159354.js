(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[362],{1214:(e,s,a)=>{Promise.resolve().then(a.bind(a,1740))},1740:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(5155),r=a(2115),l=a(6695),i=a(285),n=a(2523),d=a(5057),c=a(7313),o=a(238),x=a(4357),m=a(6516),f=a(7580),h=a(8186),u=a(5868),g=a(3109),p=a(5731);let j=e=>{let{onClose:s}=e,[a,j]=(0,r.useState)(null),[b,v]=(0,r.useState)([]),[N,y]=(0,r.useState)([]),[w,C]=(0,r.useState)(!0),[k,S]=(0,r.useState)("overview");(0,r.useEffect)(()=>{A()},[]);let A=async()=>{try{C(!0),localStorage.getItem("token");let{data:e}=await p.SP.get("/api/affiliate/dashboard");j(e.dashboard.stats),v(e.dashboard.referrals||[]),y(e.dashboard.recentCommissions||[])}catch(e){console.error("Error loading affiliate dashboard:",e),alert("Failed to load affiliate data")}finally{C(!1)}},R=async()=>{try{localStorage.getItem("token");let{data:e}=await p.SP.post("/api/affiliate/generate-code");j(s=>s?{...s,referralCode:e.referralCode}:null),alert("Referral code generated successfully!")}catch(e){console.error("Error generating referral code:",e),alert("Failed to generate referral code")}},E=()=>{if(null==a?void 0:a.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(a.referralCode);navigator.clipboard.writeText(e),alert("Referral link copied to clipboard!")}},T=async()=>{if(null==a?void 0:a.referralCode){let e="".concat(window.location.origin,"/register?ref=").concat(a.referralCode);if(navigator.share)try{await navigator.share({title:"Join AI Trainer Hub",text:"Get started with AI experts and earn rewards!",url:e})}catch(e){}else E()}};if(w)return(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading affiliate dashboard..."})]})});let U=(null==a?void 0:a.referralCode)?"".concat(window.location.origin,"/register?ref=").concat(a.referralCode):"";return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900",children:"Affiliate Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Earn 25% commission from your referrals"})]}),s&&(0,t.jsx)(i.$,{onClick:s,variant:"outline",children:"Close"})]}),(0,t.jsxs)(l.Zp,{className:"p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,t.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Your Referral Code"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Share this code to earn commissions"})]})]})}),(null==a?void 0:a.referralCode)?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(d.J,{htmlFor:"referralCode",children:"Referral Code"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,t.jsx)(n.p,{id:"referralCode",value:a.referralCode,readOnly:!0,className:"font-mono text-lg"}),(0,t.jsx)(i.$,{onClick:()=>{(null==a?void 0:a.referralCode)&&(navigator.clipboard.writeText(a.referralCode),alert("Referral code copied to clipboard!"))},variant:"outline",size:"sm",children:(0,t.jsx)(x.A,{className:"w-4 h-4"})})]})]})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(d.J,{htmlFor:"referralLink",children:"Referral Link"}),(0,t.jsxs)("div",{className:"flex gap-2 mt-1",children:[(0,t.jsx)(n.p,{id:"referralLink",value:U,readOnly:!0,className:"text-sm"}),(0,t.jsx)(i.$,{onClick:E,variant:"outline",size:"sm",children:(0,t.jsx)(x.A,{className:"w-4 h-4"})}),(0,t.jsx)(i.$,{onClick:T,variant:"outline",size:"sm",children:(0,t.jsx)(m.A,{className:"w-4 h-4"})})]})]})]}):(0,t.jsxs)("div",{className:"text-center py-4",children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have a referral code yet"}),(0,t.jsxs)(i.$,{onClick:R,className:"bg-blue-600 hover:bg-blue-700",children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Generate Referral Code"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(l.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Referrals"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:(null==a?void 0:a.totalReferrals)||0})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(f.A,{className:"w-6 h-6 text-blue-600"})})]})}),(0,t.jsx)(l.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Commissions"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:(null==a?void 0:a.totalCommissions)||0})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(h.A,{className:"w-6 h-6 text-green-600"})})]})}),(0,t.jsx)(l.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Earned"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==a?void 0:a.totalCommissionEarnedFormatted)||"Rp 0"})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(u.A,{className:"w-6 h-6 text-purple-600"})})]})}),(0,t.jsx)(l.Zp,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Last 30 Days"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==a?void 0:a.last30DaysCommissionFormatted)||"Rp 0"})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(g.A,{className:"w-6 h-6 text-orange-600"})})]})})]}),(0,t.jsxs)(c.tU,{value:k,onValueChange:S,children:[(0,t.jsxs)(c.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(c.Xi,{value:"referrals",children:"My Referrals"}),(0,t.jsx)(c.Xi,{value:"commissions",children:"Commission History"})]}),(0,t.jsx)(c.av,{value:"referrals",className:"space-y-4",children:(0,t.jsxs)(l.Zp,{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Referral Performance"}),b.length>0?(0,t.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-900",children:e.referredUserName}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.referredUserEmail}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Joined:"," ",new Date(e.referralDate).toLocaleDateString()]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("span",{className:"inline-block px-2 py-1 bg-green-100 text-green-800 text-sm rounded",children:e.totalCommissionGeneratedFormatted})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Sessions"}),(0,t.jsx)("p",{className:"font-medium",children:e.totalSessions})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Messages"}),(0,t.jsx)("p",{className:"font-medium",children:e.totalMessages})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Tokens"}),(0,t.jsx)("p",{className:"font-medium",children:e.totalTokensUsed.toLocaleString()})]})]})]},e.referredUserId))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(f.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"No referrals yet"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Share your referral code to start earning!"})]})]})}),(0,t.jsx)(c.av,{value:"commissions",className:"space-y-4",children:(0,t.jsxs)(l.Zp,{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Recent Commissions"}),N.length>0?(0,t.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.referredUserName}),(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded ".concat("direct_usage"===e.commissionType?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:"direct_usage"===e.commissionType?"Direct Usage":"Expert Usage"})]}),e.expertName&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Expert: ",e.expertName]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleString()})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"font-semibold text-green-600",children:e.commissionAmountFormatted}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:[e.commissionRate,"% commission"]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,t.jsxs)("span",{children:["Base Cost: ",e.baseCostFormatted]}),(0,t.jsxs)("span",{children:["Tokens: ",e.tokensUsed.toLocaleString()]})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(u.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"No commissions yet"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Start referring users to earn commissions!"})]})]})})]})]})};function b(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsx)(j,{})})})}},2523:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(5155);a(2115);var r=a(9434);function l(e){let{className:s,type:a,...l}=e;return(0,t.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...l})}},5057:(e,s,a)=>{"use strict";a.d(s,{J:()=>i});var t=a(5155);a(2115);var r=a(968),l=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...a})}},7313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>i});var t=a(5155);a(2115);var r=a(888),l=a(9434);function i(e){let{className:s,...a}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",s),...a})}function n(e){let{className:s,...a}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...a})}function d(e){let{className:s,...a}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...a})}function c(e){let{className:s,...a}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",s),...a})}}},e=>{e.O(0,[445,352,888,253,573,441,964,358],()=>e(e.s=1214)),_N_E=e.O()}]);