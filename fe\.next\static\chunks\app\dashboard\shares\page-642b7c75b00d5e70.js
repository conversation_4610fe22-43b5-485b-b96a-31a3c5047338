(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[946],{968:(e,a,s)=>{"use strict";s.d(a,{b:()=>i});var t=s(2115),r=s(3655),l=s(5155),n=t.forwardRef((e,a)=>(0,l.jsx)(r.sG.label,{...e,ref:a,onMouseDown:a=>{var s;a.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));n.displayName="Label";var i=n},1366:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},2283:(e,a,s)=>{Promise.resolve().then(s.bind(s,5935))},2525:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3109:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3786:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4357:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4616:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5196:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5433:(e,a,s)=>{"use strict";s.d(a,{E:()=>n,_:()=>i});var t=s(2115),r=s(7481),l=s(5731);function n(){let{toast:e}=(0,r.d)(),[a,s]=(0,t.useState)(!1),[n,i]=(0,t.useState)(null),c=(0,t.useCallback)(async a=>{try{s(!0),i(null);let t=await l.FH.post("/sharing/shares",{body:a,headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(t.data.success){let a=t.data.data,s="".concat(window.location.origin,"/shared/").concat(a.shareToken);return e({title:"Share Created!",description:"Your expert share link has been created successfully."}),{...a,shareUrl:s}}throw Error(t.data.message||"Failed to create share")}catch(s){var t,r;let a=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||s.message||"Failed to create share";return i(a),e({title:"Error",description:a,variant:"destructive"}),null}finally{s(!1)}},[e]),o=(0,t.useCallback)(async a=>{try{s(!0),i(null);let e=await l.FH.get("/sharing/shared/".concat(a));if(e.data.success)return e.data.data;throw Error(e.data.message||"Failed to load shared expert")}catch(s){var t,r,n;let a=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||s.message||"Failed to load shared expert";return i(a),(null==(n=s.response)?void 0:n.status)!==404&&e({title:"Error",description:a,variant:"destructive"}),null}finally{s(!1)}},[e]),d=(0,t.useCallback)(async(e,a)=>{try{return(await l.FH.post("/sharing/track/".concat(e,"/click"),{body:{metadata:{userAgent:navigator.userAgent,referrer:document.referrer,timestamp:new Date().toISOString(),...a}}})).data.success}catch(e){return console.error("Failed to track click:",e),!1}},[]),h=(0,t.useCallback)(async(e,a)=>{try{return(await l.FH.post("/sharing/track/".concat(e,"/conversion"),{body:{metadata:{timestamp:new Date().toISOString(),...a}},headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).data.success}catch(e){return console.error("Failed to track conversion:",e),!1}},[]),x=(0,t.useCallback)(async e=>{try{let a=await l.FH.get("/sharing/consent/".concat(e));if(a.data.success)return a.data.data;return null}catch(e){return console.error("Failed to get consent:",e),null}},[]),u=(0,t.useCallback)(async function(a,s){let t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];try{if((await l.FH.post("/sharing/consent/".concat(a),{body:{consent:s,trackingEnabled:t}})).data.success)return e({title:s?"Consent Granted":"Consent Withdrawn",description:s?"Thank you for allowing us to improve your experience.":"Your privacy preferences have been updated."}),!0;return!1}catch(a){return console.error("Failed to set consent:",a),e({title:"Error",description:"Failed to update consent preferences.",variant:"destructive"}),!1}},[e]),m=(0,t.useCallback)(async(a,t)=>{try{s(!0),i(null);let r=await l.FH.put("/sharing/shares/".concat(a),{body:t,headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(r.data.success)return e({title:"Share Updated",description:"Your share settings have been updated successfully."}),!0;throw Error(r.data.message||"Failed to update share")}catch(s){var r,n;let a=(null==(n=s.response)||null==(r=n.data)?void 0:r.message)||s.message||"Failed to update share";return i(a),e({title:"Error",description:a,variant:"destructive"}),!1}finally{s(!1)}},[e]),p=(0,t.useCallback)(async a=>{try{s(!0),i(null);let t=await l.FH.delete("/sharing/shares/".concat(a),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(t.data.success)return e({title:"Share Deleted",description:"The share link has been deactivated successfully."}),!0;throw Error(t.data.message||"Failed to delete share")}catch(s){var t,r;let a=(null==(r=s.response)||null==(t=r.data)?void 0:t.message)||s.message||"Failed to delete share";return i(a),e({title:"Error",description:a,variant:"destructive"}),!1}finally{s(!1)}},[e]),g=(0,t.useCallback)(async()=>{try{s(!0),i(null);let e=await l.FH.get("/sharing/shares/my",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.data.success)return e.data.data;throw Error(e.data.message||"Failed to load shares")}catch(r){var a,t;let s=(null==(t=r.response)||null==(a=t.data)?void 0:a.message)||r.message||"Failed to load shares";return i(s),e({title:"Error",description:s,variant:"destructive"}),null}finally{s(!1)}},[e]),y=(0,t.useCallback)(async function(a){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"7d";try{s(!0),i(null);let e=await l.FH.get("/sharing/analytics/".concat(a,"?timeRange=").concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.data.success)return e.data.data;throw Error(e.data.message||"Failed to load analytics")}catch(s){var r,n;let a=(null==(n=s.response)||null==(r=n.data)?void 0:r.message)||s.message||"Failed to load analytics";return i(a),e({title:"Error",description:a,variant:"destructive"}),null}finally{s(!1)}},[e]),v=(0,t.useCallback)(async a=>{try{let s="".concat(window.location.origin,"/shared/").concat(a);return await navigator.clipboard.writeText(s),e({title:"Copied!",description:"Share link copied to clipboard."}),!0}catch(a){return console.error("Failed to copy to clipboard:",a),e({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"}),!1}},[e]);return{loading:a,error:n,createShare:c,getSharedExpert:o,trackClick:d,trackConversion:h,getConsent:x,setConsent:u,updateShare:m,deleteShare:p,getUserShares:g,getAnalytics:y,copyShareUrl:v,generateShareUrl:(0,t.useCallback)(e=>"".concat(window.location.origin,"/shared/").concat(e),[]),clearError:(0,t.useCallback)(()=>{i(null)},[])}}function i(e){let[a,s]=(0,t.useState)(e||null),[r,l]=(0,t.useState)(null),[i,c]=(0,t.useState)(null),o=n(),d=(0,t.useCallback)(async e=>{s(e);let a=await o.getSharedExpert(e);return l(a),a},[o]);return{shareToken:a,shareData:r,consentData:i,loadSharedExpert:d,loadConsent:(0,t.useCallback)(async e=>{let a=await o.getConsent(e);return c(a),a},[o]),handleConsent:(0,t.useCallback)(async function(e,a){let s=!(arguments.length>2)||void 0===arguments[2]||arguments[2],t=await o.setConsent(e,a,s);return t&&c({hasConsented:a,consentTimestamp:new Date().toISOString(),trackingEnabled:s}),t},[o]),...o}}},5935:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>Y});var t=s(5155),r=s(2115),l=s(285),n=s(6695),i=s(7313),c=s(4616),o=s(6516),d=s(2657),h=s(1366),x=s(3109),u=s(2713),m=s(7481),p=s(5433),g=s(9338),y=s(6126),v=s(2523),j=s(6474),f=s(9434);let N=r.forwardRef((e,a)=>{let{className:s,children:r,...l}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("relative inline-block text-left",s),...l,children:r})});N.displayName="DropdownMenu";let b=r.forwardRef((e,a)=>{let{className:s,children:r,...l}=e;return(0,t.jsxs)("button",{ref:a,className:(0,f.cn)("inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",s),...l,children:[r,(0,t.jsx)(j.A,{className:"-mr-1 ml-2 h-5 w-5","aria-hidden":"true"})]})});b.displayName="DropdownMenuTrigger";let w=r.forwardRef((e,a)=>{let{className:s,align:r="center",...l}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("absolute z-50 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none","end"===r&&"right-0","start"===r&&"left-0","center"===r&&"left-1/2 transform -translate-x-1/2",s),...l})});w.displayName="DropdownMenuContent";let k=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer",s),...r})});k.displayName="DropdownMenuItem",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("border-t border-gray-100 my-1",s),...r})}).displayName="DropdownMenuSeparator",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("px-4 py-2 text-sm font-medium text-gray-900",s),...r})}).displayName="DropdownMenuLabel";var A=s(4416);let C=r.createContext(void 0),S=()=>{let e=r.useContext(C);if(!e)throw Error("useAlertDialog must be used within AlertDialog");return e},F=r.forwardRef((e,a)=>{let{className:s,open:l=!1,onOpenChange:n,children:i,...c}=e,[o,d]=r.useState(l),h=void 0!==n,x=h?l:o,u=h?n:d;return(0,t.jsx)(C.Provider,{value:{open:x,setOpen:u},children:(0,t.jsxs)("div",{ref:a,className:s,...c,children:[i,x&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50",onClick:()=>u(!1)}),(0,t.jsx)("div",{className:"relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4",children:i})]})]})})});F.displayName="AlertDialog",r.forwardRef((e,a)=>{let{className:s,onClick:r,...l}=e,{setOpen:n}=S();return(0,t.jsx)("button",{ref:a,className:s,onClick:e=>{n(!0),null==r||r(e)},...l})}).displayName="AlertDialogTrigger";let M=r.forwardRef((e,a)=>{let{className:s,children:r,...l}=e,{open:n,setOpen:i}=S();return n?(0,t.jsxs)("div",{ref:a,className:(0,f.cn)("p-6",s),...l,children:[(0,t.jsx)("button",{className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600",onClick:()=>i(!1),children:(0,t.jsx)(A.A,{className:"h-4 w-4"})}),r]}):null});M.displayName="AlertDialogContent";let E=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("mb-4",s),...r})});E.displayName="AlertDialogHeader";let D=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("h2",{ref:a,className:(0,f.cn)("text-lg font-semibold text-gray-900",s),...r})});D.displayName="AlertDialogTitle";let T=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("p",{ref:a,className:(0,f.cn)("text-sm text-gray-600 mt-2",s),...r})});T.displayName="AlertDialogDescription";let R=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{ref:a,className:(0,f.cn)("flex justify-end space-x-2 mt-6",s),...r})});R.displayName="AlertDialogFooter";let z=r.forwardRef((e,a)=>{let{className:s,onClick:r,...l}=e,{setOpen:n}=S();return(0,t.jsx)("button",{ref:a,className:(0,f.cn)("px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",s),onClick:e=>{null==r||r(e),n(!1)},...l})});z.displayName="AlertDialogAction";let I=r.forwardRef((e,a)=>{let{className:s,onClick:r,...l}=e,{setOpen:n}=S();return(0,t.jsx)("button",{ref:a,className:(0,f.cn)("px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500",s),onClick:e=>{null==r||r(e),n(!1)},...l})});I.displayName="AlertDialogCancel";var Z=s(7924),B=s(6932),H=s(9074),P=s(5196),L=s(4357);let V=(0,s(9946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var W=s(3786),$=s(2525),O=s(5731);function _(e){let{refreshTrigger:a}=e,{toast:s}=(0,m.d)(),[i,c]=(0,r.useState)([]),[x,p]=(0,r.useState)(!0),[g,j]=(0,r.useState)(""),[f,A]=(0,r.useState)("all"),[C,S]=(0,r.useState)(!1),[_,Y]=(0,r.useState)(null),[q,U]=(0,r.useState)(null),X=(0,r.useCallback)(async()=>{try{p(!0);let e=await O.FH.get("/sharing/shares/my",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});e.data.success&&c(e.data.data)}catch(e){console.error("Error loading shares:",e),s({title:"Error",description:"Failed to load shares",variant:"destructive"})}finally{p(!1)}},[s]);(0,r.useEffect)(()=>{X()},[a,X]);let G=async e=>{let a="".concat(window.location.origin,"/shared/").concat(e);try{await navigator.clipboard.writeText(a),U(e),s({title:"Copied!",description:"Share link copied to clipboard."}),setTimeout(()=>U(null),2e3)}catch(e){console.error("Failed to copy:",e),s({title:"Error",description:"Failed to copy link. Please copy manually.",variant:"destructive"})}},Q=async()=>{if(_)try{(await O.FH.delete("/sharing/shares/".concat(_.shareToken),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).data.success&&(c(e=>e.filter(e=>e.id!==_.id)),s({title:"Share Deleted",description:"The share link has been deactivated successfully."}))}catch(t){var e,a;console.error("Error deleting share:",t),s({title:"Error",description:(null==(a=t.response)||null==(e=a.data)?void 0:e.message)||"Failed to delete share. Please try again.",variant:"destructive"})}finally{S(!1),Y(null)}},J=async e=>{try{(await O.FH.put("/sharing/shares/".concat(e.shareToken),{body:{isActive:!e.isActive},headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).data.success&&(c(a=>a.map(a=>a.id===e.id?{...a,isActive:!a.isActive}:a)),s({title:e.isActive?"Share Deactivated":"Share Activated",description:"The share link has been ".concat(e.isActive?"deactivated":"activated",".")}))}catch(e){console.error("Error toggling share status:",e),s({title:"Error",description:"Failed to update share status. Please try again.",variant:"destructive"})}},K=i.filter(e=>{let a=e.expert.name.toLowerCase().includes(g.toLowerCase())||e.expert.description.toLowerCase().includes(g.toLowerCase()),s="all"===f||"active"===f&&e.isActive||"inactive"===f&&!e.isActive;return a&&s});return x?(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,a)=>(0,t.jsx)(n.Zp,{className:"animate-pulse",children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4 mb-4"}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]})]})},a))}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"My Shares"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage your shared expert links and track their performance"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(Z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(v.p,{placeholder:"Search shares...",value:g,onChange:e=>j(e.target.value),className:"pl-10 w-64"})]}),(0,t.jsxs)(N,{children:[(0,t.jsx)(b,{children:(0,t.jsxs)(l.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(B.A,{className:"h-4 w-4 mr-2"}),"all"===f?"All":"active"===f?"Active":"Inactive"]})}),(0,t.jsxs)(w,{children:[(0,t.jsx)(k,{onClick:()=>A("all"),children:"All Shares"}),(0,t.jsx)(k,{onClick:()=>A("active"),children:"Active Only"}),(0,t.jsx)(k,{onClick:()=>A("inactive"),children:"Inactive Only"})]})]})]})]}),0===K.length?(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===i.length?"No shares created yet":"No shares match your filters"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:0===i.length?"Start sharing your AI experts to track their performance and reach more users.":"Try adjusting your search or filter criteria."}),0===i.length&&(0,t.jsx)(l.$,{onClick:()=>window.location.reload(),children:"Create Your First Share"})]})}):(0,t.jsx)("div",{className:"space-y-4",children:K.map(e=>(0,t.jsx)(n.Zp,{className:"transition-all ".concat(e.isActive?"":"opacity-60"),children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.expert.name}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(y.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"}),e.monitorEnabled&&(0,t.jsxs)(y.E,{variant:"outline",children:[(0,t.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Monitored"]})]})]}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:e.expert.description}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-blue-600 mb-1",children:[(0,t.jsx)(d.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Clicks"})]}),(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-900",children:e.clickCount})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-green-600 mb-1",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Chats"})]}),(0,t.jsx)("p",{className:"text-2xl font-bold text-green-900",children:e.conversionCount})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-purple-600 mb-1",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Rate"})]}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-purple-900",children:[0===e.clickCount?0:Math.round(e.conversionCount/e.clickCount*100),"%"]})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-1 text-gray-600 mb-1",children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Created"})]}),(0,t.jsx)("p",{className:"text-sm font-bold text-gray-900",children:new Date(e.createdAt).toLocaleDateString()})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsx)(v.p,{value:"".concat(window.location.origin,"/shared/").concat(e.shareToken),readOnly:!0,className:"flex-1 text-sm"}),(0,t.jsx)(l.$,{onClick:()=>G(e.shareToken),variant:"outline",size:"sm",className:"px-3",children:q===e.shareToken?(0,t.jsx)(P.A,{className:"h-4 w-4"}):(0,t.jsx)(L.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)(N,{children:[(0,t.jsx)(b,{children:(0,t.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(V,{className:"h-4 w-4"})})}),(0,t.jsxs)(w,{align:"end",children:[(0,t.jsxs)(k,{onClick:()=>window.open("/shared/".concat(e.shareToken),"_blank"),children:[(0,t.jsx)(W.A,{className:"h-4 w-4 mr-2"}),"View Share Page"]}),(0,t.jsxs)(k,{onClick:()=>window.open("/dashboard/shares/".concat(e.shareToken,"/analytics"),"_blank"),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"View Analytics"]}),(0,t.jsxs)(k,{onClick:()=>J(e),children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),e.isActive?"Deactivate":"Activate"]}),(0,t.jsxs)(k,{onClick:()=>{Y(e),S(!0)},className:"text-red-600",children:[(0,t.jsx)($.A,{className:"h-4 w-4 mr-2"}),"Delete Share"]})]})]})]})})},e.id))}),(0,t.jsx)(F,{open:C,onOpenChange:S,children:(0,t.jsxs)(M,{children:[(0,t.jsxs)(E,{children:[(0,t.jsx)(D,{children:"Delete Share Link"}),(0,t.jsxs)(T,{children:['Are you sure you want to delete this share link for "',null==_?void 0:_.expert.name,'"? This action cannot be undone and the link will no longer be accessible.']})]}),(0,t.jsxs)(R,{children:[(0,t.jsx)(I,{children:"Cancel"}),(0,t.jsx)(z,{onClick:Q,className:"bg-red-600 hover:bg-red-700",children:"Delete Share"})]})]})})]})}function Y(){var e,a,s,y,v,j;let{toast:f}=(0,m.d)();(0,p.E)();let[N,b]=(0,r.useState)(null),[w,k]=(0,r.useState)(!0),[A,C]=(0,r.useState)(!1),[S,F]=(0,r.useState)(0),[M,E]=(0,r.useState)("overview");(0,r.useEffect)(()=>{D()},[S]);let D=async()=>{try{k(!0);let e=await O.FH.get("/sharing/dashboard/stats",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});e.data.success&&b(e.data.data)}catch(e){console.error("Error loading dashboard stats:",e)}finally{k(!1)}},T=()=>{F(e=>e+1)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between py-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Share Management"}),(0,t.jsx)("p",{className:"text-gray-600 mt-1",children:"Create, manage, and track your shared AI experts"})]}),(0,t.jsxs)(l.$,{onClick:()=>C(!0),className:"bg-blue-600 hover:bg-blue-700",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Create New Share"]})]})})}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)(i.tU,{value:M,onValueChange:E,className:"space-y-6",children:[(0,t.jsxs)(i.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(i.Xi,{value:"overview",children:"Overview"}),(0,t.jsx)(i.Xi,{value:"shares",children:"My Shares"}),(0,t.jsx)(i.Xi,{value:"analytics",children:"Analytics"})]}),(0,t.jsxs)(i.av,{value:"overview",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-blue-600 mb-2",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"Total Shares"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-900",children:w?"...":(null==N?void 0:N.totalShares)||0}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[w?"...":(null==N?void 0:N.activeShares)||0," active"]})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-green-600 mb-2",children:[(0,t.jsx)(d.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"Total Views"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-900",children:w?"...":(null==N||null==(e=N.totalClicks)?void 0:e.toLocaleString())||0}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Across all shares"})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-purple-600 mb-2",children:[(0,t.jsx)(h.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"Conversations"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-purple-900",children:w?"...":(null==N||null==(a=N.totalConversions)?void 0:a.toLocaleString())||0}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Total chats started"})]})}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-orange-600 mb-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{className:"font-medium",children:"Conversion Rate"})]}),(0,t.jsx)("p",{className:"text-3xl font-bold text-orange-900",children:w?"...":"".concat((null==N||null==(s=N.conversionRate)?void 0:s.toFixed(1))||0,"%")}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:"Views to chats"})]})})]}),(null==N?void 0:N.topPerformingShare)&&(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-yellow-500"}),(0,t.jsx)("span",{children:"Top Performing Share"})]}),(0,t.jsx)(n.BT,{children:"Your most successful shared expert this period"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-900",children:N.topPerformingShare.expertName}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Share Token: ",N.topPerformingShare.shareToken]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"flex space-x-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:N.topPerformingShare.clicks}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Views"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-2xl font-bold text-green-600",children:N.topPerformingShare.conversions}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Chats"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-2xl font-bold text-purple-600",children:[N.topPerformingShare.clicks>0?Math.round(N.topPerformingShare.conversions/N.topPerformingShare.clicks*100):0,"%"]}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Rate"})]})]})})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsx)(n.ZB,{children:"Quick Actions"}),(0,t.jsx)(n.BT,{children:"Common tasks for managing your shared experts"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",onClick:()=>C(!0),children:[(0,t.jsx)(c.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{children:"Create New Share"})]}),(0,t.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",onClick:()=>E("shares"),children:[(0,t.jsx)(o.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{children:"Manage Shares"})]}),(0,t.jsxs)(l.$,{variant:"outline",className:"h-20 flex-col space-y-2",onClick:()=>E("analytics"),children:[(0,t.jsx)(u.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{children:"View Analytics"})]})]})})]}),(!N||0===N.totalShares)&&(0,t.jsx)(n.Zp,{className:"border-dashed border-2 border-gray-300",children:(0,t.jsxs)(n.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Start Sharing Your Experts"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Share your AI experts with others and track their performance. Create your first share to get started with analytics and insights."}),(0,t.jsxs)(l.$,{onClick:()=>C(!0),size:"lg",className:"bg-blue-600 hover:bg-blue-700",children:[(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Create Your First Share"]})]})})]}),(0,t.jsxs)(i.av,{value:"shares",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"My Shares"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage all your shared expert links"})]}),(0,t.jsx)(l.$,{onClick:T,variant:"outline",children:"Refresh"})]}),(0,t.jsx)(_,{refreshTrigger:S})]}),(0,t.jsxs)(i.av,{value:"analytics",className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Analytics Overview"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Detailed insights into your sharing performance"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"text-lg",children:"Performance Metrics"})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Shares"}),(0,t.jsx)("span",{className:"font-semibold",children:(null==N?void 0:N.totalShares)||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Active Shares"}),(0,t.jsx)("span",{className:"font-semibold",children:(null==N?void 0:N.activeShares)||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Views"}),(0,t.jsx)("span",{className:"font-semibold",children:(null==N||null==(y=N.totalClicks)?void 0:y.toLocaleString())||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Chats"}),(0,t.jsx)("span",{className:"font-semibold",children:(null==N||null==(v=N.totalConversions)?void 0:v.toLocaleString())||0})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"text-lg",children:"Conversion Insights"})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:[(null==N||null==(j=N.conversionRate)?void 0:j.toFixed(1))||0,"%"]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Overall Conversion Rate"})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 text-center",children:[(null==N?void 0:N.totalConversions)||0," conversations started from ",(null==N?void 0:N.totalClicks)||0," total views"]})})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{className:"text-lg",children:"Quick Actions"})}),(0,t.jsxs)(n.Wu,{className:"space-y-3",children:[(0,t.jsx)(l.$,{variant:"outline",className:"w-full",onClick:()=>E("shares"),children:"View All Shares"}),(0,t.jsx)(l.$,{variant:"outline",className:"w-full",onClick:()=>C(!0),children:"Create New Share"}),(0,t.jsx)(l.$,{variant:"outline",className:"w-full",onClick:T,children:"Refresh Data"})]})]})]}),(0,t.jsx)(n.Zp,{children:(0,t.jsxs)(n.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Detailed Analytics"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:'For detailed analytics of individual shares, visit the share management section and click on "View Analytics" for any specific share.'}),(0,t.jsx)(l.$,{variant:"outline",onClick:()=>E("shares"),children:"Go to Share Management"})]})})]})]})}),A&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,t.jsx)("div",{className:"bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Create New Share"}),(0,t.jsx)(l.$,{variant:"ghost",onClick:()=>C(!1),className:"text-gray-500 hover:text-gray-700",children:"\xd7"})]}),(0,t.jsx)(g.A,{expert:{id:0,name:"New Expert",description:"Create a new expert to share",imageUrl:void 0,labels:["general"]},onShareCreated:()=>{C(!1),F(e=>e+1),f({title:"Share Created!",description:"Your expert has been shared successfully."})},onCancel:()=>C(!1)})]})})})]})}},6474:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6932:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7313:(e,a,s)=>{"use strict";s.d(a,{Xi:()=>c,av:()=>o,j7:()=>i,tU:()=>n});var t=s(5155);s(2115);var r=s(888),l=s(9434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",a),...s})}function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...s})}function o(e){let{className:a,...s}=e;return(0,t.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",a),...s})}},7924:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9074:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])}},e=>{e.O(0,[445,352,431,888,573,691,441,964,358],()=>e(e.s=2283)),_N_E=e.O()}]);