{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ExpertMarketplace.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ExpertMarketplace.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ExpertMarketplace.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/components/ExpertMarketplace.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ExpertMarketplace.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ExpertMarketplace.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/Web/pakarai/ai-trainer/fe/src/app/page.tsx"], "sourcesContent": ["import ExpertMarketplace from \"@/components/ExpertMarketplace\";\r\n\r\nexport default function Home() {\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50\">\r\n      {/* Hero Section */}\r\n      <section className=\"relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/5 to-indigo-900/5\"></div>\r\n        <div className=\"relative max-w-7xl mx-auto px-4 py-20\">\r\n          <div className=\"text-center space-y-8\">\r\n            <div className=\"space-y-4\">\r\n              <h1 className=\"text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 bg-clip-text text-transparent\">\r\n                AI Expert Marketplace\r\n              </h1>\r\n              <p className=\"text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n                Connect with specialized AI experts tailored to your needs. \r\n                From business consulting to creative solutions.\r\n              </p>\r\n            </div>\r\n            \r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n              <div className=\"px-6 py-3 bg-white rounded-full shadow-lg border border-gray-100\">\r\n                <span className=\"text-sm text-gray-600\">✨ Over <strong className=\"font-semibold\" style={{color: '#1E3A8A'}}>50+ AI Experts</strong> Available</span>\r\n              </div>\r\n              <div className=\"px-6 py-3 bg-white rounded-full shadow-lg border border-gray-100\">\r\n                <span className=\"text-sm text-gray-600\">🚀 <strong className=\"font-semibold\" style={{color: '#1E3A8A'}}>Instant</strong> Responses</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Featured Experts Section */}\r\n      <section className=\"max-w-7xl mx-auto px-4 py-16\">\r\n        <div className=\"text-center mb-12\">\r\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n            Featured AI Experts\r\n          </h2>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\r\n            Discover our curated selection of top-performing AI experts, each specialized in their domain\r\n          </p>\r\n        </div>\r\n        \r\n        <ExpertMarketplace />\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"bg-white py-20\">\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Why Choose Our Platform?\r\n            </h2>\r\n          </div>\r\n          \r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            <div className=\"text-center p-8 rounded-2xl bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100\">\r\n              <div className=\"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl\" style={{backgroundColor: '#1E3A8A', color: 'white'}}>\r\n                🎯\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Specialized Expertise</h3>\r\n              <p className=\"text-gray-600\">Each AI expert is fine-tuned for specific domains, ensuring highly relevant and accurate responses.</p>\r\n            </div>\r\n            \r\n            <div className=\"text-center p-8 rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 border border-green-100\">\r\n              <div className=\"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl\" style={{backgroundColor: '#1E3A8A', color: 'white'}}>\r\n                ⚡\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Instant Access</h3>\r\n              <p className=\"text-gray-600\">Start conversations immediately. No waiting, no appointments - just instant expert guidance.</p>\r\n            </div>\r\n            \r\n            <div className=\"text-center p-8 rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-100\">\r\n              <div className=\"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-3xl\" style={{backgroundColor: '#1E3A8A', color: 'white'}}>\r\n                🔒\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">Secure & Private</h3>\r\n              <p className=\"text-gray-600\">Your conversations are encrypted and private. We prioritize your data security above all.</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyH;;;;;;sDAGvI,8OAAC;4CAAE,WAAU;sDAAsE;;;;;;;;;;;;8CAMrF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAwB;kEAAO,8OAAC;wDAAO,WAAU;wDAAgB,OAAO;4DAAC,OAAO;wDAAS;kEAAG;;;;;;oDAAuB;;;;;;;;;;;;sDAErI,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;oDAAwB;kEAAG,8OAAC;wDAAO,WAAU;wDAAgB,OAAO;4DAAC,OAAO;wDAAS;kEAAG;;;;;;oDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlI,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,8OAAC,uIAAA,CAAA,UAAiB;;;;;;;;;;;0BAIpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;;;;;;sCAKpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAgF,OAAO;gDAAC,iBAAiB;gDAAW,OAAO;4CAAO;sDAAG;;;;;;sDAGpJ,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAgF,OAAO;gDAAC,iBAAiB;gDAAW,OAAO;4CAAO;sDAAG;;;;;;sDAGpJ,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAgF,OAAO;gDAAC,iBAAiB;gDAAW,OAAO;4CAAO;sDAAG;;;;;;sDAGpJ,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}